#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// File extensions to analyze
const FRONTEND_EXTENSIONS = ['.js', '.jsx', '.ts', '.tsx'];
const BACKEND_EXTENSIONS = ['.js', '.ts'];
const TEST_EXTENSIONS = ['.test.js', '.test.jsx', '.test.ts', '.test.tsx', '.spec.js', '.spec.jsx'];

// Directories to exclude
const EXCLUDE_DIRS = [
    'node_modules', 
    '.git', 
    'dist', 
    'build', 
    'coverage', 
    '.next', 
    '.nuxt',
    '__pycache__',
    'vendor',
    'uploads',
    '.vscode',
    '.augment',
    '.qodo'
];

// Files to exclude
const EXCLUDE_FILES = [
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',
    '.DS_Store',
    'Thumbs.db'
];

function countLines(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');
        
        let codeLines = 0;
        let totalLines = lines.length;
        let commentLines = 0;
        let emptyLines = 0;
        
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed === '') {
                emptyLines++;
            } else if (trimmed.startsWith('//') || trimmed.startsWith('/*') || trimmed.startsWith('*') || trimmed.startsWith('#')) {
                commentLines++;
            } else {
                codeLines++;
            }
        }
        
        return {
            total: totalLines,
            code: codeLines,
            comments: commentLines,
            empty: emptyLines
        };
    } catch (error) {
        console.error(`Error reading file ${filePath}:`, error.message);
        return { total: 0, code: 0, comments: 0, empty: 0 };
    }
}

function shouldExcludeDir(dirName) {
    return EXCLUDE_DIRS.some(exclude => dirName.includes(exclude));
}

function shouldExcludeFile(fileName) {
    return EXCLUDE_FILES.includes(fileName);
}

function isTestFile(fileName) {
    return TEST_EXTENSIONS.some(ext => fileName.endsWith(ext)) || 
           fileName.includes('.test.') || 
           fileName.includes('.spec.') ||
           fileName.includes('test') ||
           fileName.includes('spec');
}

function getFileType(filePath, baseDir) {
    const ext = path.extname(filePath).toLowerCase();
    
    if (isTestFile(filePath)) return 'Test';
    
    if (baseDir.includes('client') || baseDir.includes('frontend')) {
        if (FRONTEND_EXTENSIONS.includes(ext)) return 'Frontend';
    }
    
    if (baseDir.includes('server') || baseDir.includes('backend') || baseDir.includes('api')) {
        if (BACKEND_EXTENSIONS.includes(ext)) return 'Backend';
    }
    
    // Fallback based on extension
    if (FRONTEND_EXTENSIONS.includes(ext)) return 'Frontend';
    if (BACKEND_EXTENSIONS.includes(ext)) return 'Backend';
    
    return 'Other';
}

function getComponentCategory(filePath) {
    const pathLower = filePath.toLowerCase();
    const fileName = path.basename(filePath, path.extname(filePath)).toLowerCase();
    
    // Test files
    if (isTestFile(filePath)) return 'Test';
    
    // Backend categories
    if (pathLower.includes('controller')) return 'Controller';
    if (pathLower.includes('model')) return 'Model';
    if (pathLower.includes('service')) return 'Service';
    if (pathLower.includes('middleware')) return 'Middleware';
    if (pathLower.includes('route')) return 'Route';
    if (pathLower.includes('validator')) return 'Validator';
    if (pathLower.includes('util')) return 'Utility';
    if (pathLower.includes('config')) return 'Config';
    if (pathLower.includes('migration')) return 'Migration';
    if (pathLower.includes('seed')) return 'Seed';
    if (pathLower.includes('schema')) return 'Schema';
    
    // Frontend categories
    if (pathLower.includes('component')) return 'Component';
    if (pathLower.includes('page')) return 'Page';
    if (pathLower.includes('context')) return 'Context';
    if (pathLower.includes('hook')) return 'Hook';
    if (pathLower.includes('layout')) return 'Layout';
    
    // Check by file extension and path structure
    const ext = path.extname(filePath).toLowerCase();
    if (['.jsx', '.tsx'].includes(ext)) {
        if (pathLower.includes('pages')) return 'Page';
        if (pathLower.includes('components')) return 'Component';
        if (pathLower.includes('contexts')) return 'Context';
        if (pathLower.includes('hooks')) return 'Hook';
        if (pathLower.includes('layouts')) return 'Layout';
        return 'Component';
    }
    
    if (['.css', '.scss', '.sass'].includes(ext)) return 'Style';
    if (['.js', '.ts'].includes(ext)) {
        if (pathLower.includes('pages')) return 'Page';
        return 'Script';
    }
    
    return 'Other';
}

function analyzeDirectory(dirPath, results = [], baseDir = '') {
    try {
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                if (!shouldExcludeDir(item)) {
                    analyzeDirectory(fullPath, results, baseDir || item);
                }
            } else if (stat.isFile()) {
                if (!shouldExcludeFile(item)) {
                    const lines = countLines(fullPath);
                    const fileType = getFileType(fullPath, baseDir);
                    const category = getComponentCategory(fullPath);
                    const relativePath = path.relative(process.cwd(), fullPath);
                    
                    results.push({
                        name: item,
                        path: relativePath,
                        fullPath: fullPath,
                        type: fileType,
                        category: category,
                        lines: lines,
                        size: stat.size
                    });
                }
            }
        }
    } catch (error) {
        console.error(`Error analyzing directory ${dirPath}:`, error.message);
    }
    
    return results;
}

function findTestsForComponent(componentPath, allFiles) {
    const componentName = path.basename(componentPath, path.extname(componentPath));
    const componentDir = path.dirname(componentPath);
    
    const possibleTestPaths = [
        // Same directory
        path.join(componentDir, `${componentName}.test.js`),
        path.join(componentDir, `${componentName}.test.jsx`),
        path.join(componentDir, `${componentName}.spec.js`),
        path.join(componentDir, `${componentName}.spec.jsx`),
        // __tests__ directory
        path.join(componentDir, '__tests__', `${componentName}.test.js`),
        path.join(componentDir, '__tests__', `${componentName}.test.jsx`),
        path.join(componentDir, '__tests__', `${componentName}.spec.js`),
        path.join(componentDir, '__tests__', `${componentName}.spec.jsx`),
        // tests directory
        path.join(componentDir, 'tests', `${componentName}.test.js`),
        path.join(componentDir, 'tests', `${componentName}.test.jsx`),
        // Root test directory
        path.join('test', `${componentName}.test.js`),
        path.join('tests', `${componentName}.test.js`),
    ];
    
    const foundTests = [];
    
    // Check for exact matches
    possibleTestPaths.forEach(testPath => {
        const normalizedTestPath = path.normalize(testPath);
        const matchingFile = allFiles.find(file => 
            path.normalize(file.path) === normalizedTestPath ||
            file.path.includes(normalizedTestPath) ||
            normalizedTestPath.includes(file.path)
        );
        if (matchingFile) {
            foundTests.push(matchingFile);
        }
    });
    
    // Check for files that contain the component name in test files
    const testFiles = allFiles.filter(file => file.type === 'Test');
    testFiles.forEach(testFile => {
        if (testFile.name.toLowerCase().includes(componentName.toLowerCase()) ||
            testFile.path.toLowerCase().includes(componentName.toLowerCase())) {
            if (!foundTests.some(t => t.path === testFile.path)) {
                foundTests.push(testFile);
            }
        }
    });
    
    return foundTests;
}

function generateLargeComponentsReport(results) {
    console.log('\n' + '='.repeat(100));
    console.log('LARGE COMPONENTS ANALYSIS & TEST COVERAGE REPORT');
    console.log('='.repeat(100));
    
    // Filter for significant files (more than 50 lines) and exclude test files
    const significantFiles = results.filter(file => 
        file.lines.total > 50 && 
        file.type !== 'Test' && 
        (file.type === 'Frontend' || file.type === 'Backend')
    );
    
    // Sort by total lines (descending)
    const sortedByLines = [...significantFiles].sort((a, b) => b.lines.total - a.lines.total);
    
    // Get large components (>200 lines)
    const largeComponents = sortedByLines.filter(file => file.lines.total > 200);
    
    // Get medium components (100-200 lines)
    const mediumComponents = sortedByLines.filter(file => 
        file.lines.total >= 100 && file.lines.total <= 200
    );
    
    // Get all test files
    const testFiles = results.filter(file => file.type === 'Test');
    
    console.log('\n📊 SUMMARY STATISTICS:');
    console.log('-'.repeat(100));
    console.log(`Total Components Analyzed: ${significantFiles.length}`);
    console.log(`Large Components (>200 lines): ${largeComponents.length}`);
    console.log(`Medium Components (100-200 lines): ${mediumComponents.length}`);
    console.log(`Test Files Found: ${testFiles.length}`);
    
    // Analyze large components
    console.log('\n🔴 LARGE COMPONENTS (>200 lines) - HIGH PRIORITY FOR TESTING:');
    console.log('-'.repeat(100));
    console.log('Lines | Code  | Type      | Category   | Test Coverage | File Name');
    console.log('-'.repeat(100));
    
    largeComponents.forEach((component, index) => {
        const tests = findTestsForComponent(component.path, results);
        const hasTests = tests.length > 0;
        const testCoverage = hasTests ? `✅ ${tests.length} test(s)` : '❌ No tests';
        
        const lines = component.lines.total.toString().padStart(5);
        const code = component.lines.code.toString().padStart(4);
        const type = component.type.padEnd(9);
        const category = component.category.padEnd(10);
        const coverage = testCoverage.padEnd(13);
        
        console.log(`${lines} | ${code} | ${type} | ${category} | ${coverage} | ${component.name}`);
        console.log(`      |       |           |            |               | ${component.path}`);
        
        if (hasTests) {
            tests.forEach(test => {
                console.log(`      |       |           |            |               |   └─ Test: ${test.name} (${test.lines.total} lines)`);
            });
        }
        console.log('-'.repeat(100));
    });
    
    // Analyze medium components
    console.log('\n🟡 MEDIUM COMPONENTS (100-200 lines) - MEDIUM PRIORITY FOR TESTING:');
    console.log('-'.repeat(100));
    console.log('Lines | Code  | Type      | Category   | Test Coverage | File Name');
    console.log('-'.repeat(100));
    
    mediumComponents.slice(0, 15).forEach((component, index) => {
        const tests = findTestsForComponent(component.path, results);
        const hasTests = tests.length > 0;
        const testCoverage = hasTests ? `✅ ${tests.length} test(s)` : '❌ No tests';
        
        const lines = component.lines.total.toString().padStart(5);
        const code = component.lines.code.toString().padStart(4);
        const type = component.type.padEnd(9);
        const category = component.category.padEnd(10);
        const coverage = testCoverage.padEnd(13);
        
        console.log(`${lines} | ${code} | ${type} | ${category} | ${coverage} | ${component.name}`);
        console.log(`      |       |           |            |               | ${component.path}`);
        console.log('-'.repeat(100));
    });
    
    // Test coverage analysis
    console.log('\n📋 TEST COVERAGE ANALYSIS:');
    console.log('-'.repeat(100));
    
    const componentsWithTests = significantFiles.filter(component => 
        findTestsForComponent(component.path, results).length > 0
    );
    
    const testCoveragePercentage = ((componentsWithTests.length / significantFiles.length) * 100).toFixed(1);
    
    console.log(`Components with Tests: ${componentsWithTests.length}/${significantFiles.length} (${testCoveragePercentage}%)`);
    console.log(`Components without Tests: ${significantFiles.length - componentsWithTests.length}`);
    
    // Break down by type
    const frontendComponents = significantFiles.filter(f => f.type === 'Frontend');
    const backendComponents = significantFiles.filter(f => f.type === 'Backend');
    
    const frontendWithTests = frontendComponents.filter(component => 
        findTestsForComponent(component.path, results).length > 0
    );
    const backendWithTests = backendComponents.filter(component => 
        findTestsForComponent(component.path, results).length > 0
    );
    
    console.log(`\nFrontend Components: ${frontendComponents.length} (${frontendWithTests.length} with tests)`);
    console.log(`Backend Components: ${backendComponents.length} (${backendWithTests.length} with tests)`);
    
    // Existing test files analysis
    console.log('\n📁 EXISTING TEST FILES:');
    console.log('-'.repeat(100));
    console.log('Lines | Type      | File Name & Path');
    console.log('-'.repeat(100));
    
    testFiles.forEach(testFile => {
        const lines = testFile.lines.total.toString().padStart(5);
        const type = testFile.type.padEnd(9);
        
        console.log(`${lines} | ${type} | ${testFile.name}`);
        console.log(`      |           | ${testFile.path}`);
        console.log('-'.repeat(100));
    });
    
    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('-'.repeat(100));
    
    const largeComponentsWithoutTests = largeComponents.filter(component => 
        findTestsForComponent(component.path, results).length === 0
    );
    
    console.log(`\n🔴 HIGH PRIORITY - Create tests for ${largeComponentsWithoutTests.length} large components:`);
    largeComponentsWithoutTests.slice(0, 10).forEach((component, index) => {
        console.log(`   ${index + 1}. ${component.name} (${component.lines.total} lines) - ${component.category}`);
        console.log(`      Path: ${component.path}`);
    });
    
    const mediumComponentsWithoutTests = mediumComponents.filter(component => 
        findTestsForComponent(component.path, results).length === 0
    );
    
    console.log(`\n🟡 MEDIUM PRIORITY - Create tests for ${mediumComponentsWithoutTests.length} medium components:`);
    mediumComponentsWithoutTests.slice(0, 10).forEach((component, index) => {
        console.log(`   ${index + 1}. ${component.name} (${component.lines.total} lines) - ${component.category}`);
        console.log(`      Path: ${component.path}`);
    });
    
    // Testing framework recommendations
    console.log('\n🛠️ TESTING FRAMEWORK SETUP:');
    console.log('-'.repeat(100));
    
    const hasJest = fs.existsSync(path.join(process.cwd(), 'server', 'package.json')) &&
                   fs.readFileSync(path.join(process.cwd(), 'server', 'package.json'), 'utf8').includes('jest');
    
    console.log(`Jest (Backend): ${hasJest ? '✅ Configured' : '❌ Not configured'}`);
    
    const clientPackageJson = path.join(process.cwd(), 'client', 'package.json');
    const hasVitest = fs.existsSync(clientPackageJson) &&
                     fs.readFileSync(clientPackageJson, 'utf8').includes('vitest');
    
    console.log(`Vitest (Frontend): ${hasVitest ? '✅ Configured' : '❌ Not configured'}`);
    
    if (!hasJest) {
        console.log('\n📦 To setup Jest for backend testing:');
        console.log('   npm install --save-dev jest supertest');
        console.log('   Add to package.json: "test": "jest"');
    }
    
    if (!hasVitest) {
        console.log('\n📦 To setup Vitest for frontend testing:');
        console.log('   npm install --save-dev vitest @testing-library/react @testing-library/jest-dom');
        console.log('   Add to package.json: "test": "vitest"');
    }
    
    console.log('\n✅ Analysis completed!');
    console.log(`\nNext steps:`);
    console.log(`1. Focus on testing the ${largeComponents.length} large components first`);
    console.log(`2. Set up testing frameworks if not already configured`);
    console.log(`3. Create unit tests for components without test coverage`);
    console.log(`4. Consider refactoring components with >300 lines`);
}

// Main execution
console.log('🔍 Starting Large Components & Test Coverage Analysis...');

const results = [];

// Analyze client directory
const clientPath = path.join(process.cwd(), 'client');
if (fs.existsSync(clientPath)) {
    console.log('📁 Analyzing client directory...');
    analyzeDirectory(clientPath, results, 'client');
}

// Analyze server directory
const serverPath = path.join(process.cwd(), 'server');
if (fs.existsSync(serverPath)) {
    console.log('📁 Analyzing server directory...');
    analyzeDirectory(serverPath, results, 'server');
}

// Analyze root level files
console.log('📁 Analyzing root directory files...');
const rootItems = fs.readdirSync(process.cwd());
rootItems.forEach(item => {
    const fullPath = path.join(process.cwd(), item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isFile() && !shouldExcludeFile(item)) {
        const lines = countLines(fullPath);
        const fileType = getFileType(fullPath, '');
        const category = getComponentCategory(fullPath);
        
        results.push({
            name: item,
            path: item,
            fullPath: fullPath,
            type: fileType,
            category: category,
            lines: lines,
            size: stat.size
        });
    }
});

generateLargeComponentsReport(results);