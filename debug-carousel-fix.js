const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');
const mongoose = require('mongoose');

const BASE_URL = 'http://localhost:5000';

// Connect to MongoDB to check for existing admin users
async function connectDB() {
  try {
    await mongoose.connect('mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('Connected to MongoDB');
    return true;
  } catch (error) {
    console.log('MongoDB connection error:', error.message);
    return false;
  }
}

// Create admin user schema for checking existing users
const userSchema = new mongoose.Schema({
  firstName: String,
  lastName: String,
  email: String,
  password: String,
  userType: String,
  isActive: Boolean,
  isEmailVerified: Boolean
}, { collection: 'users' });

const User = mongoose.model('User', userSchema);

async function findAdminUser() {
  try {
    const adminUser = await User.findOne({ userType: 'admin', isActive: true });
    if (adminUser) {
      console.log('Found admin user:', adminUser.email);
      return {
        email: adminUser.email,
        // We can't get the password, so we'll need to create a test password
        needsPasswordReset: true
      };
    }
    return null;
  } catch (error) {
    console.log('Error finding admin user:', error.message);
    return null;
  }
}

async function createTestAdmin() {
  try {
    const bcrypt = require('bcrypt');
    const password = 'AdminTest123!';
    const hashedPassword = await bcrypt.hash(password, 12);
    
    const testAdmin = new User({
      firstName: 'Test',
      lastName: 'Admin',
      email: '<EMAIL>',
      password: hashedPassword,
      userType: 'admin',
      isActive: true,
      isEmailVerified: true
    });
    
    await testAdmin.save();
    console.log('Created test admin user');
    return { email: '<EMAIL>', password: password };
  } catch (error) {
    if (error.code === 11000) {
      console.log('Test admin already exists');
      return { email: '<EMAIL>', password: 'AdminTest123!' };
    }
    console.log('Error creating test admin:', error.message);
    return null;
  }
}

async function loginUser(credentials) {
  try {
    const response = await axios.post(`${BASE_URL}/api/auth/login`, credentials);
    if (response.data.success) {
      console.log('Login successful');
      return response.data.data.token;
    }
    return null;
  } catch (error) {
    console.log('Login error:', error.response?.data?.message || error.message);
    return null;
  }
}

async function testCarouselUpload(token) {
  try {
    console.log('\\n=== Testing Carousel Upload ===');
    
    // First test getting homepage settings
    console.log('1. Testing GET /api/admin/homepage-settings...');
    try {
      const getResponse = await axios.get(`${BASE_URL}/api/admin/homepage-settings`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('✅ GET successful, status:', getResponse.status);
      console.log('Current settings keys:', Object.keys(getResponse.data.data || {}));
    } catch (error) {
      console.log('❌ GET failed:', error.response?.status, error.response?.data);
      return false;
    }
    
    // Create test image file
    const testImagePath = path.join(__dirname, 'test-carousel-debug.jpg');
    
    // Create a proper test JPEG image
    const createTestImage = () => {
      const jpegHeader = Buffer.from([
        0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
        0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
        0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
        0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
        0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
        0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
        0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
        0x3C, 0x2E, 0x33, 0x34, 0x32
      ]);
      
      const jpegData = Buffer.alloc(2000, 0x80);
      const jpegFooter = Buffer.from([0xFF, 0xD9]);
      
      return Buffer.concat([jpegHeader, jpegData, jpegFooter]);
    };
    
    fs.writeFileSync(testImagePath, createTestImage());
    console.log('Created test image:', testImagePath);
    
    // Test carousel upload
    console.log('2. Testing POST /api/admin/homepage-settings/carousel...');
    
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath), {
      filename: 'test-carousel.jpg',
      contentType: 'image/jpeg'
    });
    formData.append('title', 'Debug Test Carousel Image');
    formData.append('description', 'This is a test image for debugging carousel upload');
    formData.append('linkUrl', 'https://example.com');
    
    try {
      const uploadResponse = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, formData, {
        headers: {
          ...formData.getHeaders(),
          'Authorization': `Bearer ${token}`
        },
        timeout: 30000 // 30 second timeout
      });
      
      console.log('✅ Upload successful!');
      console.log('Response status:', uploadResponse.status);
      console.log('Response data:', JSON.stringify(uploadResponse.data, null, 2));
      
      // Clean up
      if (fs.existsSync(testImagePath)) {
        fs.unlinkSync(testImagePath);
      }
      
      return true;
      
    } catch (error) {
      console.log('❌ Upload failed!');
      console.log('Status:', error.response?.status);
      console.log('Status Text:', error.response?.statusText);
      console.log('Error Data:', JSON.stringify(error.response?.data, null, 2));
      console.log('Error Message:', error.message);
      
      // Check if it's a validation error
      if (error.response?.status === 400) {
        console.log('\\n🔍 Debugging 400 error...');
        
        // Test with a simpler request first
        console.log('Testing without image file...');
        const testForm = new FormData();
        testForm.append('title', 'Test Without Image');
        
        try {
          const testResponse = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, testForm, {
            headers: {
              ...testForm.getHeaders(),
              'Authorization': `Bearer ${token}`
            }
          });
          console.log('No image test passed (unexpected)');
        } catch (testError) {
          console.log('No image test failed as expected:', testError.response?.data?.message);
        }
      }
      
      // Clean up
      if (fs.existsSync(testImagePath)) {
        fs.unlinkSync(testImagePath);
      }
      
      return false;
    }
    
  } catch (error) {
    console.log('Test setup error:', error.message);
    return false;
  }
}

async function debugCarouselIssue() {
  console.log('=== Debugging Carousel Upload Issue ===\\n');
  
  // Connect to database
  const dbConnected = await connectDB();
  if (!dbConnected) {
    console.log('Cannot connect to database, will try API-only tests');
  }
  
  let credentials = null;
  
  if (dbConnected) {
    // Try to find existing admin
    const existingAdmin = await findAdminUser();
    if (existingAdmin && !existingAdmin.needsPasswordReset) {
      credentials = existingAdmin;
    } else {
      // Create test admin
      credentials = await createTestAdmin();
    }
    
    await mongoose.disconnect();
  }
  
  if (!credentials) {
    console.log('Could not get admin credentials, trying default...');
    credentials = { email: '<EMAIL>', password: 'password123' };
  }
  
  console.log('Attempting login with:', credentials.email);
  const token = await loginUser(credentials);
  
  if (!token) {
    console.log('❌ Authentication failed, cannot test carousel upload');
    console.log('\\n💡 To fix this, you need to:');
    console.log('1. Create an admin user in the database');
    console.log('2. Or fix the authentication system');
    return;
  }
  
  console.log('✅ Authentication successful');
  
  // Test carousel upload
  const uploadSuccess = await testCarouselUpload(token);
  
  if (uploadSuccess) {
    console.log('\\n🎉 Carousel upload is working correctly!');
  } else {
    console.log('\\n🐛 Carousel upload has issues that need to be fixed');
    console.log('\\n💡 Check the server logs for more details');
    console.log('Common issues:');
    console.log('1. Cloudinary configuration missing or incorrect');
    console.log('2. File upload middleware not working properly');
    console.log('3. Database schema mismatch');
    console.log('4. Image processing errors');
  }
}

// Run the debug script
debugCarouselIssue().catch(console.error);
