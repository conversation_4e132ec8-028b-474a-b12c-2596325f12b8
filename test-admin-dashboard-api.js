#!/usr/bin/env node

/**
 * Test script to debug admin dashboard API response
 */

const { User, Vendor, Product, Order, Category } = require('./server/src/models');
const mongoose = require('mongoose');

async function connectDB() {
  try {
    await mongoose.connect('mongodb://localhost:27017/alicartify-db', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('📊 Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function testAdminDashboardData() {
  try {
    console.log('🔍 Testing Admin Dashboard Data...\n');

    // Test User statistics
    console.log('👥 Testing User Statistics:');
    const userStats = await User.getStatistics();
    console.log('User Stats:', JSON.stringify(userStats, null, 2));

    // Test basic user count
    const totalUserCount = await User.countDocuments();
    console.log('Total User Count (direct):', totalUserCount);

    // Test user types
    const adminCount = await User.countDocuments({ userType: 'admin' });
    const vendorCount = await User.countDocuments({ userType: 'vendor' });
    const customerCount = await User.countDocuments({ userType: 'customer' });
    
    console.log('User Types:');
    console.log('  Admin:', adminCount);
    console.log('  Vendor:', vendorCount);
    console.log('  Customer:', customerCount);

    // Test Vendor statistics
    console.log('\n🏪 Testing Vendor Statistics:');
    const vendorStats = await Vendor.getStatistics();
    console.log('Vendor Stats:', JSON.stringify(vendorStats, null, 2));

    // Test Order statistics
    console.log('\n📦 Testing Order Statistics:');
    const orderStats = await Order.getStatistics();
    console.log('Order Stats:', JSON.stringify(orderStats, null, 2));

    // Test Product statistics
    console.log('\n📦 Testing Product Statistics:');
    const productStats = await Product.getStatistics();
    console.log('Product Stats:', JSON.stringify(productStats, null, 2));

    // Test Category statistics
    console.log('\n📂 Testing Category Statistics:');
    const categoryStats = await Category.getStatistics();
    console.log('Category Stats:', JSON.stringify(categoryStats, null, 2));

    // Test the full dashboard response structure
    console.log('\n📊 Full Dashboard Response Structure:');
    const dashboardData = {
      overview: {
        users: userStats,
        vendors: vendorStats,
        products: productStats,
        orders: orderStats,
        categories: categoryStats
      }
    };

    console.log('Dashboard Data Structure:');
    console.log(JSON.stringify(dashboardData, null, 2));

    // Test specific fields that frontend expects
    console.log('\n🎯 Frontend Expected Fields:');
    console.log('Total Users:', dashboardData.overview.users.totalUsers);
    console.log('Active Users:', dashboardData.overview.users.activeUsers);
    console.log('New This Month:', dashboardData.overview.users.newThisMonth);
    console.log('Active Vendors:', dashboardData.overview.vendors.activeVendors);

  } catch (error) {
    console.error('❌ Error testing dashboard data:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Test completed');
  }
}

async function main() {
  await connectDB();
  await testAdminDashboardData();
}

if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}
