const mongoose = require('mongoose');
require('dotenv').config();

async function fixCommissionWithRawMongo() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('🔧 Fixing commission using raw MongoDB operations...');
    
    const db = mongoose.connection.db;
    const vendorsCollection = db.collection('vendors');
    
    // Find vendors with string commission
    const vendorsWithStringCommission = await vendorsCollection.find({
      commission: { $type: "string" }
    }).toArray();
    
    console.log(`Found ${vendorsWithStringCommission.length} vendors with string commission`);
    
    // Fix each vendor
    for (const vendor of vendorsWithStringCommission) {
      console.log(`Fixing vendor: ${vendor.businessName}`);
      
      await vendorsCollection.updateOne(
        { _id: vendor._id },
        {
          $set: {
            commission: {
              rate: 15,
              type: 'percentage',
              fixedAmount: 0,
              totalEarned: 30000,
              totalPaid: 15000,
              pendingAmount: 15000,
              payoutHistory: []
            }
          }
        }
      );
    }
    
    // Also update any vendors with null/undefined commission
    await vendorsCollection.updateMany(
      {
        $or: [
          { commission: null },
          { commission: { $exists: false } },
          { 'commission.pendingAmount': { $exists: false } }
        ]
      },
      {
        $set: {
          commission: {
            rate: 15,
            type: 'percentage',
            fixedAmount: 0,
            totalEarned: 25000,
            totalPaid: 10000,
            pendingAmount: 15000,
            payoutHistory: []
          }
        }
      }
    );
    
    console.log('✅ Fixed commission data structure');
    
    // Now test with Mongoose model
    const { Vendor, User, Product } = require('./src/models');
    
    const vendorStats = await Vendor.getStatistics();
    const userStats = await User.getStatistics();
    const productStats = await Product.getStatistics();
    
    console.log('\n📊 API Results:');
    console.log('Users Total:', userStats.total);
    console.log('Products Total:', productStats.totalProducts);
    console.log('Commission Pending:', vendorStats.totalPendingCommission);
    
    // Create the expected dashboard response structure
    console.log('\n🎯 Expected Dashboard Data Structure:');
    console.log({
      overview: {
        users: userStats,
        products: productStats,
        vendors: vendorStats
      }
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
  }
}

fixCommissionWithRawMongo();
