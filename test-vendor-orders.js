// Test script for vendor order management improvements
// Vendor credentials: <EMAIL> : Free@007

const testVendorOrders = async () => {
  const baseURL = 'https://multi-vendor-server-1tb9.onrender.com/api';
  
  // Test login first
  const loginResponse = await fetch(`${baseURL}/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'Free@007'
    })
  });

  if (!loginResponse.ok) {
    console.error('Login failed:', await loginResponse.text());
    return;
  }

  const loginData = await loginResponse.json();
  const token = loginData.token;
  
  console.log('✅ Login successful');

  // Test getting vendor orders
  const ordersResponse = await fetch(`${baseURL}/vendor/orders?page=1&limit=10`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  if (ordersResponse.ok) {
    const ordersData = await ordersResponse.json();
    console.log('✅ Orders fetched successfully:', ordersData.data?.orders?.length || 0, 'orders');
    
    // Test with filters
    const filteredResponse = await fetch(`${baseURL}/vendor/orders?status=pending&paymentStatus=completed&page=1&limit=5`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (filteredResponse.ok) {
      const filteredData = await filteredResponse.json();
      console.log('✅ Filtered orders work:', filteredData.data?.orders?.length || 0, 'filtered orders');
    }
  } else {
    console.error('❌ Orders fetch failed:', await ordersResponse.text());
  }

  // Test analytics
  const analyticsResponse = await fetch(`${baseURL}/vendor/orders/analytics?period=30d`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  if (analyticsResponse.ok) {
    const analyticsData = await analyticsResponse.json();
    console.log('✅ Analytics working:', analyticsData.data?.stats);
  } else {
    console.error('❌ Analytics failed:', await analyticsResponse.text());
  }

  console.log('🎉 Test completed!');
};

// For browser testing
if (typeof window !== 'undefined') {
  window.testVendorOrders = testVendorOrders;
} else {
  // For Node.js testing
  testVendorOrders().catch(console.error);
}
