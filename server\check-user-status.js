const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

const User = require('./src/models/User');

async function checkUserStatus() {
    try {
        console.log('🔍 Checking user status...\n');
        
        const email = '<EMAIL>';
        const user = await User.findOne({ email });
        
        if (!user) {
            console.log('❌ User not found');
            return;
        }
        
        console.log('📋 User Details:');
        console.log('================');
        console.log('ID:', user._id);
        console.log('Email:', user.email);
        console.log('First Name:', user.firstName);
        console.log('Last Name:', user.lastName);
        console.log('User Type:', user.userType);
        console.log('Email Verified:', user.isEmailVerified);
        console.log('Email Verification Token:', user.emailVerificationToken);
        console.log('Email Verification Expires:', user.emailVerificationExpires);
        console.log('Created At:', user.createdAt);
        console.log('Updated At:', user.updatedAt);
        
        // Check if verification token is expired
        if (user.emailVerificationExpires) {
            const now = new Date();
            const isExpired = user.emailVerificationExpires < now;
            console.log('Token Expired:', isExpired);
            if (isExpired) {
                console.log('Token expired at:', user.emailVerificationExpires);
                console.log('Current time:', now);
            }
        }
        
        // Let's create a test user that needs verification
        console.log('\n🔧 Creating test user for verification...');
        
        const testEmail = '<EMAIL>';
        
        // Remove existing test user if any
        await User.deleteOne({ email: testEmail });
        
        const testUser = new User({
            firstName: 'Test',
            lastName: 'User',
            email: testEmail,
            password: 'password123',
            userType: 'customer',
            isEmailVerified: false
        });
        
        // Generate verification token
        const verificationToken = testUser.generateEmailVerificationToken();
        await testUser.save();
        
        console.log('✅ Test user created:');
        console.log('Email:', testUser.email);
        console.log('Verification Token (raw):', verificationToken);
        console.log('Verification Token (hashed):', testUser.emailVerificationToken);
        console.log('Verification Expires:', testUser.emailVerificationExpires);
        
        console.log('\n🔗 Test verification URL:');
        console.log(`http://localhost:5000/api/auth/verify-email?token=${verificationToken}&email=${testEmail}`);
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        mongoose.connection.close();
    }
}

checkUserStatus();