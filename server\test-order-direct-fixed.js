/**
 * Direct database test for order creation with manual orderNumber
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Order = require('./src/models/Order');
const User = require('./src/models/User');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('✅ MongoDB connected');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Test order creation directly in database
const testDirectOrderCreation = async () => {
  console.log('\n🧪 TESTING DIRECT ORDER CREATION WITH MANUAL ORDER NUMBER');
  console.log('='.repeat(60));
  
  try {
    // Find a customer
    const customer = await User.findOne({ email: '<EMAIL>' });
    if (!customer) {
      console.log('❌ Customer not found');
      return false;
    }
    
    console.log('✅ Customer found:', customer._id);
    
    // Generate order number manually
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const timestamp = Date.now().toString().slice(-6);
    const orderNumber = `ORD${year}${month}${day}${timestamp}`;
    
    console.log('📝 Generated order number:', orderNumber);
    
    // Create test order data
    const orderData = {
      orderNumber: orderNumber,
      customer: customer._id,
      items: [{
        product: new mongoose.Types.ObjectId('6881fd806652576061a0be95'),
        vendor: new mongoose.Types.ObjectId('6881fd066652576061a0be6e'),
        name: 'Samsun',
        sku: 'SKU6881fd806652576061a0be95',
        image: '',
        quantity: 1,
        unitPrice: 4400,
        totalPrice: 4400,
        status: 'pending'
      }],
      billing: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '1234567890',
        address: {
          street: 'Test Address',
          city: 'Test City',
          state: 'Test State',
          zipCode: '12345',
          country: 'India'
        }
      },
      shipping: {
        firstName: 'Test',
        lastName: 'User',
        address: {
          street: 'Test Address',
          city: 'Test City',
          state: 'Test State',
          zipCode: '12345',
          country: 'India'
        },
        method: 'standard',
        cost: 0
      },
      payment: {
        method: 'cod',
        status: 'pending'
      },
      pricing: {
        subtotal: 4400,
        tax: 0,
        shipping: 28,
        discount: 0,
        total: 4428
      },
      status: 'confirmed',
      timeline: [{
        status: 'confirmed',
        timestamp: new Date(),
        note: 'Order has been confirmed',
        updatedBy: customer._id
      }],
      customerNotes: 'Test order from debug script'
    };
    
    console.log('📦 Creating order...');
    
    // Create the order
    const order = new Order(orderData);
    await order.save();
    
    console.log('✅ Order created successfully!');
    console.log('Order details:', {
      id: order._id,
      orderNumber: order.orderNumber,
      customer: order.customer,
      status: order.status,
      total: order.pricing.total,
      itemCount: order.items.length
    });
    
    return true;
    
  } catch (error) {
    console.log('❌ Direct order creation error:', error.message);
    console.log('Error details:', error);
    return false;
  }
};

// Main function
const main = async () => {
  console.log('🚀 DIRECT ORDER CREATION TEST WITH MANUAL ORDER NUMBER');
  console.log('='.repeat(70));
  
  try {
    await connectDB();
    const success = await testDirectOrderCreation();
    
    if (success) {
      console.log('\n🎉 SUCCESS! Direct order creation works!');
      console.log('Now let\'s test the API endpoint...');
    } else {
      console.log('\n❌ Direct order creation failed');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n🏁 Database connection closed');
  }
};

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testDirectOrderCreation };