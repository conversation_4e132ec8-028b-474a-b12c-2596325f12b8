const mongoose = require('mongoose');
const express = require('express');
const request = require('supertest');
const { User, Vendor } = require('./src/models');
const vendorStoreRoutes = require('./src/routes/vendor/store');
const { verifyToken, requireUserType } = require('./src/middleware/auth/authMiddleware');
const jwt = require('jsonwebtoken');
require('dotenv').config();

// Mock middleware for testing
const mockAuth = (req, res, next) => {
  // Find a vendor user for testing
  User.findOne({ userType: 'vendor' }).then(user => {
    if (user) {
      req.user = { userId: user._id, userType: 'vendor' };
      next();
    } else {
      res.status(401).json({ success: false, message: 'No vendor user found' });
    }
  }).catch(err => {
    res.status(500).json({ success: false, message: 'Auth error' });
  });
};

async function testVendorAPIEndpoints() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Create Express app for testing
    const app = express();
    app.use(express.json());
    
    // Use mock auth middleware
    app.use('/api/vendor/store', mockAuth);
    app.use('/api/vendor/store', vendorStoreRoutes);

    console.log('\n🧪 Testing Vendor Store API Endpoints...');

    // Test 1: GET /api/vendor/store/profile
    console.log('\n📋 Test 1: GET /api/vendor/store/profile');
    try {
      const response = await request(app)
        .get('/api/vendor/store/profile')
        .expect('Content-Type', /json/);

      console.log('Status:', response.status);
      if (response.status === 200) {
        console.log('✅ Profile fetch successful');
        console.log('   - Business Name:', response.body.data?.businessName);
        console.log('   - User Email:', response.body.data?.user?.email);
      } else {
        console.log('❌ Profile fetch failed');
        console.log('   - Error:', response.body.message);
      }
    } catch (error) {
      console.log('❌ Profile fetch error:', error.message);
    }

    // Test 2: PUT /api/vendor/store/profile
    console.log('\n📋 Test 2: PUT /api/vendor/store/profile');
    const profileUpdateData = {
      businessName: 'API Test Business',
      businessDescription: 'Updated via API test',
      businessType: 'company',
      contactInfo: {
        businessPhone: '+1234567890',
        businessEmail: '<EMAIL>',
        website: 'https://test-business.com'
      },
      businessAddress: {
        street: '123 API Test Street',
        city: 'Test City',
        state: 'Test State',
        zipCode: '12345',
        country: 'Test Country'
      },
      settings: {
        returnPolicy: 'API test return policy',
        shippingPolicy: 'API test shipping policy',
        processingTime: 5
      },
      firstName: 'API',
      lastName: 'Tester',
      phone: '+9876543210'
    };

    try {
      const response = await request(app)
        .put('/api/vendor/store/profile')
        .send(profileUpdateData)
        .expect('Content-Type', /json/);

      console.log('Status:', response.status);
      if (response.status === 200) {
        console.log('✅ Profile update successful');
        console.log('   - Business Name:', response.body.data?.businessName);
        console.log('   - Processing Time:', response.body.data?.settings?.processingTime);
      } else {
        console.log('❌ Profile update failed');
        console.log('   - Error:', response.body.message);
      }
    } catch (error) {
      console.log('❌ Profile update error:', error.message);
    }

    // Test 3: PUT /api/vendor/store/settings
    console.log('\n📋 Test 3: PUT /api/vendor/store/settings');
    const settingsUpdateData = {
      storeSettings: {
        autoAcceptOrders: true,
        minimumOrderAmount: 100
      },
      notificationSettings: {
        newOrders: true,
        lowStock: false,
        reviews: true
      },
      paymentSettings: {
        acceptCreditCards: true,
        acceptPayPal: true
      },
      shippingSettings: {
        freeShippingThreshold: 500,
        standardShippingRate: 50
      }
    };

    try {
      const response = await request(app)
        .put('/api/vendor/store/settings')
        .send(settingsUpdateData)
        .expect('Content-Type', /json/);

      console.log('Status:', response.status);
      if (response.status === 200) {
        console.log('✅ Settings update successful');
        console.log('   - Auto Accept Orders:', response.body.data?.storeSettings?.autoAcceptOrders);
        console.log('   - Minimum Order Amount:', response.body.data?.storeSettings?.minimumOrderAmount);
      } else {
        console.log('❌ Settings update failed');
        console.log('   - Error:', response.body.message);
      }
    } catch (error) {
      console.log('❌ Settings update error:', error.message);
    }

    // Test 4: Test password change
    console.log('\n📋 Test 4: PUT /api/vendor/store/settings (Password Change)');
    const passwordChangeData = {
      security: {
        currentPassword: 'wrongpassword',
        newPassword: 'newpassword123'
      }
    };

    try {
      const response = await request(app)
        .put('/api/vendor/store/settings')
        .send(passwordChangeData)
        .expect('Content-Type', /json/);

      console.log('Status:', response.status);
      if (response.status === 200) {
        console.log('✅ Password change successful');
      } else {
        console.log('❌ Password change failed (expected for wrong password)');
        console.log('   - Error:', response.body.message);
      }
    } catch (error) {
      console.log('❌ Password change error:', error.message);
    }

    console.log('\n✅ API endpoint tests completed!');

  } catch (error) {
    console.error('❌ Error during API testing:', error);
    console.error('Error details:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testVendorAPIEndpoints();