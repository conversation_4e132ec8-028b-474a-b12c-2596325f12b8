const mongoose = require('mongoose');
const Category = require('./src/models/Category');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/multi-vendor-ecommerce', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const categories = [
  {
    name: 'Electronics',
    slug: 'electronics',
    description: 'Electronic devices and gadgets',
    status: 'active',
    featured: true,
    level: 0,
    sortOrder: 1,
    subcategories: [
      {
        name: 'Smartphones',
        slug: 'smartphones',
        description: 'Mobile phones and accessories',
        status: 'active',
        level: 1,
        sortOrder: 1
      },
      {
        name: 'Laptops',
        slug: 'laptops',
        description: 'Portable computers',
        status: 'active',
        level: 1,
        sortOrder: 2
      },
      {
        name: 'Tablets',
        slug: 'tablets',
        description: 'Tablet computers',
        status: 'active',
        level: 1,
        sortOrder: 3
      },
      {
        name: 'Audio & Headphones',
        slug: 'audio-headphones',
        description: 'Audio equipment and headphones',
        status: 'active',
        level: 1,
        sortOrder: 4
      }
    ]
  },
  {
    name: 'Clothing & Fashion',
    slug: 'clothing-fashion',
    description: 'Apparel and fashion accessories',
    status: 'active',
    featured: true,
    level: 0,
    sortOrder: 2,
    subcategories: [
      {
        name: "Men's Clothing",
        slug: 'mens-clothing',
        description: 'Clothing for men',
        status: 'active',
        level: 1,
        sortOrder: 1
      },
      {
        name: "Women's Clothing",
        slug: 'womens-clothing',
        description: 'Clothing for women',
        status: 'active',
        level: 1,
        sortOrder: 2
      },
      {
        name: 'Shoes',
        slug: 'shoes',
        description: 'Footwear for all',
        status: 'active',
        level: 1,
        sortOrder: 3
      }
    ]
  },
  {
    name: 'Home & Garden',
    slug: 'home-garden',
    description: 'Home improvement and garden supplies',
    status: 'active',
    featured: false,
    level: 0,
    sortOrder: 3,
    subcategories: [
      {
        name: 'Furniture',
        slug: 'furniture',
        description: 'Home and office furniture',
        status: 'active',
        level: 1,
        sortOrder: 1
      },
      {
        name: 'Kitchen & Dining',
        slug: 'kitchen-dining',
        description: 'Kitchen and dining essentials',
        status: 'active',
        level: 1,
        sortOrder: 2
      }
    ]
  },
  {
    name: 'Books',
    slug: 'books',
    description: 'Books and literature',
    status: 'active',
    featured: false,
    level: 0,
    sortOrder: 4,
    subcategories: [
      {
        name: 'Fiction',
        slug: 'fiction',
        description: 'Fiction books',
        status: 'active',
        level: 1,
        sortOrder: 1
      },
      {
        name: 'Non-Fiction',
        slug: 'non-fiction',
        description: 'Non-fiction books',
        status: 'active',
        level: 1,
        sortOrder: 2
      }
    ]
  },
  {
    name: 'Sports & Outdoors',
    slug: 'sports-outdoors',
    description: 'Sports equipment and outdoor gear',
    status: 'active',
    featured: false,
    level: 0,
    sortOrder: 5
  }
];

const seedCategories = async () => {
  try {
    console.log('🧹 Clearing existing categories...');
    await Category.deleteMany({});
    
    console.log('🌱 Seeding categories...');
    
    for (const categoryData of categories) {
      const { subcategories, ...mainCategoryData } = categoryData;
      
      // Create main category
      const mainCategory = new Category(mainCategoryData);
      await mainCategory.save();
      console.log(`✅ Created category: ${mainCategory.name}`);
      
      // Create subcategories if they exist
      if (subcategories && subcategories.length > 0) {
        for (const subCatData of subcategories) {
          const subcategory = new Category({
            ...subCatData,
            parent: mainCategory._id
          });
          await subcategory.save();
          console.log(`   ✅ Created subcategory: ${subcategory.name}`);
        }
      }
    }
    
    console.log('🎉 Categories seeded successfully!');
    console.log('📊 Summary:');
    
    const totalCategories = await Category.countDocuments({});
    const mainCategories = await Category.countDocuments({ level: 0 });
    const subCategories = await Category.countDocuments({ level: 1 });
    
    console.log(`   Total categories: ${totalCategories}`);
    console.log(`   Main categories: ${mainCategories}`);
    console.log(`   Subcategories: ${subCategories}`);
    
  } catch (error) {
    console.error('❌ Error seeding categories:', error);
  }
};

const main = async () => {
  await connectDB();
  await seedCategories();
  
  // Close connection
  await mongoose.connection.close();
  console.log('🔌 Database connection closed');
};

// Run the seeder
main().catch(console.error);
