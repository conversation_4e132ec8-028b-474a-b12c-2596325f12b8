# Dynamic Vendor Analytics Testing Guide

## Environment Configuration

The vendor analytics now use environment-based configuration for maximum flexibility:

### Development Testing
1. Use `.env.development` for local development
2. Enable debug mode for detailed logging
3. Shorter refresh intervals for faster testing

### Production Deployment
1. Use `.env.production` for production builds
2. Optimized settings for performance
3. Real-time updates with appropriate intervals

## Testing Real Dynamic Analytics

### Step 1: Start Development Environment
```bash
# In the client directory
npm run dev
```

### Step 2: Check Configuration
1. Open browser console
2. Look for debug logs showing configuration values
3. Verify API URLs are correct

### Step 3: Test Real-Time Updates
1. Login as a vendor
2. Navigate to dashboard
3. Open browser console to see real-time update logs
4. Create new orders/products to see live data changes

### Step 4: Test Different Environments
```bash
# Test with different environment files
cp .env.development .env    # For development
cp .env.production .env     # For production testing
```

## Environment Variables Reference

### API Configuration
- `VITE_API_URL`: Base API URL
- `VITE_API_TIMEOUT`: Request timeout in milliseconds
- `VITE_API_RETRY_ATTEMPTS`: Number of retry attempts
- `VITE_API_RETRY_DELAY`: Delay between retries

### Analytics Configuration
- `VITE_ANALYTICS_REFRESH_INTERVAL`: Real-time update interval
- `VITE_ANALYTICS_CACHE_TIMEOUT`: Cache timeout for data
- `VITE_ANALYTICS_REAL_TIME`: Enable/disable real-time updates
- `VITE_ANALYTICS_DEBUG`: Enable debug logging

### Performance Configuration
- `VITE_ENABLE_CACHING`: Enable API response caching
- `VITE_ENABLE_COMPRESSION`: Enable data compression
- `VITE_ENABLE_LAZY_LOADING`: Enable lazy loading

## Troubleshooting

### If Dashboard Shows Loading Spinner
1. Check browser console for errors
2. Verify environment variables are correct
3. Test API endpoints manually
4. Check authentication tokens

### If Real-Time Updates Don't Work
1. Verify `VITE_ANALYTICS_REAL_TIME=true`
2. Check refresh interval settings
3. Look for error logs in console
4. Ensure component is not unmounting

### If No Data Shows
1. Verify vendor has products and orders
2. Check API endpoint responses
3. Test with different vendor accounts
4. Check server logs for errors

## Configuration Examples

### High-Performance Setup
```
VITE_ANALYTICS_REFRESH_INTERVAL=15000
VITE_ANALYTICS_CACHE_TIMEOUT=30000
VITE_ENABLE_CACHING=true
VITE_DEBUG_MODE=false
```

### Debug-Heavy Setup
```
VITE_ANALYTICS_REFRESH_INTERVAL=5000
VITE_ANALYTICS_DEBUG=true
VITE_DEBUG_MODE=true
VITE_API_RETRY_ATTEMPTS=1
```

### Production-Optimized Setup
```
VITE_ANALYTICS_REFRESH_INTERVAL=60000
VITE_ANALYTICS_CACHE_TIMEOUT=120000
VITE_ENABLE_COMPRESSION=true
VITE_DEBUG_MODE=false
```
