const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

// Test different scenarios to identify the 400 error cause
const diagnose400Error = async () => {
    console.log('🔍 DIAGNOSING 400 ERROR ON /api/auth/register\n');
    console.log('='.repeat(60));
    
    const baseURL = 'http://localhost:5000/api';
    
    // Test 1: Check server health
    console.log('\n1️⃣ Testing server health...');
    try {
        const response = await axios.get(`${baseURL}/health`);
        console.log('✅ Server is responding');
        console.log('📋 Health response:', response.data);
    } catch (error) {
        console.log('❌ Server health check failed');
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 Server is not running. Please start the server with: npm start');
            return;
        }
        console.log('📋 Error:', error.message);
        return;
    }
    
    // Test 2: Check auth health
    console.log('\n2️⃣ Testing auth service health...');
    try {
        const response = await axios.get(`${baseURL}/auth/health`);
        console.log('✅ Auth service is responding');
        console.log('📋 Auth health response:', response.data);
    } catch (error) {
        console.log('❌ Auth service health check failed');
        console.log('📋 Status:', error.response?.status);
        console.log('📋 Error:', error.response?.data || error.message);
    }
    
    // Test 3: Test with minimal valid data
    console.log('\n3️⃣ Testing with minimal valid data...');
    try {
        const minimalData = {
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>',
            password: 'password123'
        };
        
        const response = await axios.post(`${baseURL}/auth/register`, minimalData);
        console.log('✅ Minimal registration succeeded');
        console.log('📋 Response:', response.data);
    } catch (error) {
        console.log('❌ Minimal registration failed');
        console.log('📋 Status:', error.response?.status);
        console.log('📋 Error response:', JSON.stringify(error.response?.data, null, 2));
        
        if (error.response?.status === 400) {
            console.log('\n🔍 ANALYSIS: 400 Bad Request detected');
            const errorData = error.response.data;
            
            if (errorData.errors) {
                console.log('📋 Validation errors found:');
                errorData.errors.forEach((err, index) => {
                    console.log(`   ${index + 1}. Field: ${err.field}, Message: ${err.message}`);
                });
            }
            
            if (errorData.message) {
                console.log('📋 Error message:', errorData.message);
            }
        }
    }
    
    // Test 4: Test with complete customer data
    console.log('\n4️⃣ Testing with complete customer data...');
    try {
        const customerData = {
            firstName: 'John',
            lastName: 'Customer',
            email: '<EMAIL>',
            password: 'password123',
            userType: 'customer',
            phone: '+1234567890',
            countryCode: 'US'
        };
        
        const response = await axios.post(`${baseURL}/auth/register`, customerData);
        console.log('✅ Customer registration succeeded');
        console.log('📋 Response:', response.data);
    } catch (error) {
        console.log('❌ Customer registration failed');
        console.log('📋 Status:', error.response?.status);
        console.log('📋 Error response:', JSON.stringify(error.response?.data, null, 2));
    }
    
    // Test 5: Test with vendor data
    console.log('\n5️⃣ Testing with vendor data...');
    try {
        const vendorData = {
            firstName: 'Jane',
            lastName: 'Vendor',
            email: '<EMAIL>',
            password: 'password123',
            userType: 'vendor',
            businessName: 'Jane\'s Business',
            businessType: 'retail'
        };
        
        const response = await axios.post(`${baseURL}/auth/register`, vendorData);
        console.log('✅ Vendor registration succeeded');
        console.log('📋 Response:', response.data);
    } catch (error) {
        console.log('❌ Vendor registration failed');
        console.log('📋 Status:', error.response?.status);
        console.log('📋 Error response:', JSON.stringify(error.response?.data, null, 2));
    }
    
    // Test 6: Test with invalid data to confirm validation works
    console.log('\n6️⃣ Testing with invalid data (should fail)...');
    try {
        const invalidData = {
            firstName: '',
            lastName: 'Test',
            email: 'invalid-email',
            password: '123'
        };
        
        const response = await axios.post(`${baseURL}/auth/register`, invalidData);
        console.log('❌ Invalid data registration should have failed but succeeded');
        console.log('📋 Response:', response.data);
    } catch (error) {
        console.log('✅ Invalid data correctly rejected');
        console.log('📋 Status:', error.response?.status);
        console.log('📋 Error response:', JSON.stringify(error.response?.data, null, 2));
    }
    
    // Test 7: Check database connection
    console.log('\n7️⃣ Testing database connection...');
    try {
        const DB_URL = process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/multi-vendor-ecommerce';
        await mongoose.connect(DB_URL, {
            serverSelectionTimeoutMS: 5000,
        });
        console.log('✅ Database connection successful');
        await mongoose.connection.close();
    } catch (error) {
        console.log('❌ Database connection failed');
        console.log('📋 Error:', error.message);
        console.log('💡 This could be causing the 400 errors if the server can\'t connect to the database');
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('🏁 DIAGNOSIS COMPLETE');
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('1. Check the server console logs for detailed error messages');
    console.log('2. Ensure MongoDB is running and accessible');
    console.log('3. Verify all required environment variables are set');
    console.log('4. Check if the request data format matches the validation schema');
    console.log('5. Look for CORS issues if requests are coming from a different origin');
};

// Run diagnosis
diagnose400Error().catch(error => {
    console.error('❌ Diagnosis failed:', error.message);
});