<!DOCTYPE html>
<html>
<head>
    <title>Carousel Upload Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, button { padding: 8px; width: 300px; }
        button { width: auto; background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>🎠 Carousel Upload Test</h1>
    
    <form id="uploadForm" enctype="multipart/form-data">
        <div class="form-group">
            <label for="image">Image File:</label>
            <input type="file" name="image" id="image" accept="image/*" required>
        </div>
        
        <div class="form-group">
            <label for="title">Title:</label>
            <input type="text" name="title" id="title" value="Test Carousel Image" required>
        </div>
        
        <div class="form-group">
            <label for="description">Description:</label>
            <input type="text" name="description" id="description" value="This is a test carousel image">
        </div>
        
        <div class="form-group">
            <label for="linkUrl">Link URL:</label>
            <input type="url" name="linkUrl" id="linkUrl" value="https://example.com">
        </div>
        
        <button type="submit">🚀 Upload Image</button>
    </form>
    
    <div id="result"></div>
    <div id="logs" class="log"></div>
    
    <script>
        const logDiv = document.getElementById('logs');
        const resultDiv = document.getElementById('result');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            console.log(message);
        }
        
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            logDiv.textContent = '';
            resultDiv.innerHTML = '';
            
            const formData = new FormData(this);
            
            log('🚀 Starting upload process...');
            log('📋 Form data fields:');
            for (let [key, value] of formData.entries()) {
                if (value instanceof File) {
                    log(`  ${key}: ${value.name} (${value.size} bytes, ${value.type})`);
                } else {
                    log(`  ${key}: ${value}`);
                }
            }
            
            try {
                // Step 1: Login
                log('🔐 Logging in...');
                const loginResponse = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                
                const loginData = await loginResponse.json();
                log(`Login response: ${loginResponse.status} ${loginResponse.statusText}`);
                
                if (!loginData.success) {
                    throw new Error(`Login failed: ${loginData.message}`);
                }
                
                const token = loginData.data.token;
                log('✅ Login successful');
                
                // Step 2: Upload image
                log('📤 Uploading image...');
                const uploadResponse = await fetch('http://localhost:5000/api/admin/homepage-settings/carousel', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                        // Note: Don't set Content-Type for FormData - let browser set it with boundary
                    },
                    body: formData
                });
                
                log(`Upload response: ${uploadResponse.status} ${uploadResponse.statusText}`);
                
                const uploadData = await uploadResponse.json();
                
                if (uploadData.success) {
                    log('🎉 Upload successful!');
                    resultDiv.innerHTML = '<div class="result success">✅ Upload successful!</div>';
                } else {
                    log(`❌ Upload failed: ${uploadData.error || uploadData.message}`);
                    resultDiv.innerHTML = `<div class="result error">❌ Upload failed: ${uploadData.error || uploadData.message}</div>`;
                }
                
            } catch (error) {
                log(`💥 Error: ${error.message}`);
                resultDiv.innerHTML = `<div class="result error">💥 Error: ${error.message}</div>`;
            }
        });
        
        // Test server connectivity on page load
        window.addEventListener('load', async () => {
            try {
                log('🔍 Testing server connectivity...');
                const response = await fetch('http://localhost:5000/api/admin/homepage-settings', {
                    method: 'OPTIONS'
                });
                log(`Server test: ${response.status} ${response.statusText}`);
                if (response.ok) {
                    log('✅ Server is reachable');
                } else {
                    log('❌ Server returned error');
                }
            } catch (error) {
                log(`❌ Server unreachable: ${error.message}`);
            }
        });
    </script>
</body>
</html>