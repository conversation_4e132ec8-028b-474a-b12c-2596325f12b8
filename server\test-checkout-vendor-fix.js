#!/usr/bin/env node

/**
 * Test Script: Checkout Vendor ID Issue Reproduction and Fix
 * 
 * This script reproduces the vendor ID missing error that occurs when placing orders
 * and provides a comprehensive fix for the issue.
 * 
 * Issue: Vendor information is missing for product when placing order
 * Product ID: 6881fd806652576061a0be95
 * Error: "Vendor information is missing for product: Samsun. Please refresh and try again."
 */

const mongoose = require('mongoose');
const path = require('path');

// Load environment variables (run from server directory)
require('dotenv').config({ path: path.join(__dirname, '.env') });

// Import models
const Cart = require('./src/models/Cart');
const Product = require('./src/models/Product');
const User = require('./src/models/User');

// Configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce';
const PRODUCT_ID = '6881fd806652576061a0be95';

/**
 * Connect to MongoDB
 */
async function connectDB() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

/**
 * Find a test customer user
 */
async function findTestCustomer() {
  const customer = await User.findOne({ userType: 'customer' }).limit(1);
  if (!customer) {
    console.error('❌ No customer found. Please ensure you have at least one customer user.');
    process.exit(1);
  }
  console.log(`✅ Found test customer: ${customer.email} (${customer._id})`);
  return customer;
}

/**
 * Check if the problematic product exists and has vendor info
 */
async function checkProduct() {
  console.log('\n📊 Checking product data...');
  
  const product = await Product.findById(PRODUCT_ID).populate('vendor', 'businessName email userType');
  
  if (!product) {
    console.error(`❌ Product with ID ${PRODUCT_ID} not found`);
    process.exit(1);
  }
  
  console.log(`✅ Product found: ${product.name}`);
  console.log(`   - Status: ${product.status}`);
  console.log(`   - Vendor: ${product.vendor ? `${product.vendor.businessName} (${product.vendor._id})` : 'NOT POPULATED'}`);
  console.log(`   - Vendor Type: ${product.vendor ? product.vendor.userType : 'N/A'}`);
  
  if (!product.vendor) {
    console.error('❌ Product has no vendor assigned!');
    return null;
  }
  
  return product;
}

/**
 * Create a test cart with the problematic product
 */
async function createTestCart(customer, product) {
  console.log('\n🛒 Creating test cart...');
  
  // Remove existing cart for clean test
  await Cart.deleteOne({ customer: customer._id });
  
  // Create new cart
  const cart = new Cart({ customer: customer._id });
  
  // Add the problematic product
  await cart.addItem(
    product._id,
    product.vendor._id,
    1,
    product.pricing?.salePrice || product.pricing?.basePrice || 100
  );
  
  console.log('✅ Test cart created with problematic product');
  return cart;
}

/**
 * Simulate fetching cart data like the frontend does
 */
async function simulateFrontendCartFetch(customerId) {
  console.log('\n🔄 Simulating frontend cart fetch...');
  
  // This mimics the cartApi.getCart() call
  const cart = await Cart.findByCustomerWithCurrency(customerId, 'INR');
  
  if (!cart) {
    console.error('❌ No cart found for customer');
    return null;
  }
  
  console.log('✅ Cart fetched successfully');
  console.log(`   - Total items: ${cart.totalItems}`);
  console.log(`   - Total amount: ${cart.totalAmount}`);
  console.log(`   - Items count: ${cart.items.length}`);
  
  // Log each item's structure
  cart.items.forEach((item, index) => {
    console.log(`\n   Item ${index + 1}:`);
    console.log(`     - Product ID: ${item.product._id}`);
    console.log(`     - Product Name: ${item.product.name}`);
    console.log(`     - Vendor in item: ${item.vendor ? item.vendor._id : 'MISSING'}`);
    console.log(`     - Vendor business name: ${item.vendor ? item.vendor.businessName : 'MISSING'}`);
    console.log(`     - Product vendor: ${item.product.vendor ? item.product.vendor._id : 'MISSING'}`);
    console.log(`     - Quantity: ${item.quantity}`);
    console.log(`     - Price at add: ${item.priceAtAdd}`);
  });
  
  return cart;
}

/**
 * Simulate the problematic checkout process
 */
async function simulateCheckoutProcess(cart) {
  console.log('\n💳 Simulating checkout process...');
  
  try {
    // This mimics the CheckoutPage.jsx handlePlaceOrder function
    const orderItems = cart.items.map(item => {
      console.log(`\n   Processing item: ${item.product.name}`);
      
      // Enhanced vendor ID extraction (current problematic logic)
      let vendorId = null;
      
      // Try multiple sources for vendor ID
      if (item.vendor) {
        // Direct vendor reference from cart item
        vendorId = typeof item.vendor === 'object' ? item.vendor._id : item.vendor;
        console.log(`     ✅ Found vendor from item.vendor: ${vendorId}`);
      } else if (item.product?.vendor) {
        // Vendor from populated product
        vendorId = typeof item.product.vendor === 'object' ? item.product.vendor._id : item.product.vendor;
        console.log(`     ✅ Found vendor from item.product.vendor: ${vendorId}`);
      }
      
      if (!vendorId) {
        console.error(`     ❌ Missing vendor info for item:`, {
          productId: item.product?._id,
          productName: item.product?.name,
          itemVendor: item.vendor,
          productVendor: item.product?.vendor,
        });
        throw new Error(`Vendor information is missing for product: ${item.product?.name || 'Unknown Product'}. Please refresh and try again.`);
      }
      
      return {
        product: item.product._id,
        vendor: vendorId,
        name: item.product.name,
        sku: item.product.sku || `SKU${item.product._id}`,
        quantity: item.quantity,
        unitPrice: item.priceAtAdd,
        totalPrice: item.priceAtAdd * item.quantity,
      };
    });
    
    console.log('✅ All items processed successfully for order');
    console.log(`   - Total order items: ${orderItems.length}`);
    
    return orderItems;
    
  } catch (error) {
    console.error('❌ Checkout simulation failed:', error.message);
    return null;
  }
}

/**
 * Test the fixed vendor ID extraction logic
 */
async function testFixedVendorExtraction(cart) {
  console.log('\n🔧 Testing FIXED vendor extraction logic...');
  
  try {
    const orderItems = cart.items.map(item => {
      console.log(`\n   Processing item with FIXED logic: ${item.product.name}`);
      
      // FIXED: More robust vendor ID extraction
      let vendorId = null;
      
      // Strategy 1: Check direct vendor reference from cart item
      if (item.vendor) {
        if (typeof item.vendor === 'object' && item.vendor._id) {
          vendorId = item.vendor._id.toString();
          console.log(`     ✅ Found vendor from item.vendor._id: ${vendorId}`);
        } else if (typeof item.vendor === 'string') {
          vendorId = item.vendor;
          console.log(`     ✅ Found vendor from item.vendor (string): ${vendorId}`);
        }
      }
      
      // Strategy 2: Check vendor from populated product (fallback)
      if (!vendorId && item.product?.vendor) {
        if (typeof item.product.vendor === 'object' && item.product.vendor._id) {
          vendorId = item.product.vendor._id.toString();
          console.log(`     ✅ Found vendor from item.product.vendor._id: ${vendorId}`);
        } else if (typeof item.product.vendor === 'string') {
          vendorId = item.product.vendor;
          console.log(`     ✅ Found vendor from item.product.vendor (string): ${vendorId}`);
        }
      }
      
      // Strategy 3: If still no vendor, fetch from database
      if (!vendorId) {
        console.log(`     ⚠️  No vendor found in cart data, would need to fetch from database`);
        // This would require an async call in real implementation
      }
      
      if (!vendorId) {
        console.error(`     ❌ STILL missing vendor info after fixed logic:`, {
          productId: item.product?._id,
          productName: item.product?.name,
          itemVendor: item.vendor,
          productVendor: item.product?.vendor,
        });
        throw new Error(`Vendor information is missing for product: ${item.product?.name || 'Unknown Product'}. Please refresh and try again.`);
      }
      
      return {
        product: item.product._id,
        vendor: vendorId,
        name: item.product.name,
        sku: item.product.sku || `SKU${item.product._id}`,
        quantity: item.quantity,
        unitPrice: item.priceAtAdd,
        totalPrice: item.priceAtAdd * item.quantity,
      };
    });
    
    console.log('✅ FIXED logic worked! All items processed successfully');
    console.log(`   - Total order items: ${orderItems.length}`);
    
    return orderItems;
    
  } catch (error) {
    console.error('❌ FIXED logic also failed:', error.message);
    return null;
  }
}

/**
 * Generate the fix for CheckoutPage.jsx
 */
function generateCheckoutPageFix() {
  console.log('\n🔧 SOLUTION: Updated CheckoutPage.jsx vendor extraction logic');
  console.log('\n' + '='.repeat(80));
  console.log('REPLACE the vendor extraction logic in CheckoutPage.jsx (lines ~133-154) with:');
  console.log('='.repeat(80));
  
  const fixedCode = `
// FIXED: Enhanced vendor ID extraction with better error handling and fallbacks
let vendorId = null;

// Strategy 1: Check direct vendor reference from cart item (most reliable)
if (item.vendor) {
  if (typeof item.vendor === 'object' && item.vendor._id) {
    vendorId = item.vendor._id.toString();
  } else if (typeof item.vendor === 'string' && item.vendor.length === 24) {
    // Valid ObjectId string
    vendorId = item.vendor;
  }
}

// Strategy 2: Check vendor from populated product (fallback)
if (!vendorId && item.product?.vendor) {
  if (typeof item.product.vendor === 'object' && item.product.vendor._id) {
    vendorId = item.product.vendor._id.toString();
  } else if (typeof item.product.vendor === 'string' && item.product.vendor.length === 24) {
    // Valid ObjectId string
    vendorId = item.product.vendor;
  }
}

// Strategy 3: Log detailed debug info if still no vendor found
if (!vendorId) {
  console.error('Detailed vendor extraction debug:', {
    productId: item.product?._id,
    productName: item.product?.name,
    itemVendor: {
      exists: !!item.vendor,
      type: typeof item.vendor,
      value: item.vendor,
      hasId: !!(item.vendor?._id),
      id: item.vendor?._id
    },
    productVendor: {
      exists: !!(item.product?.vendor),
      type: typeof item.product?.vendor,
      value: item.product?.vendor,
      hasId: !!(item.product?.vendor?._id),
      id: item.product?.vendor?._id
    },
    fullItemStructure: JSON.stringify(item, null, 2)
  });
  
  throw new Error(\`Vendor information is missing for product: \${item.product?.name || 'Unknown Product'}. Please refresh your cart and try again.\`);
}`;

  console.log(fixedCode);
  console.log('='.repeat(80));
}

/**
 * Main test function
 */
async function runTest() {
  console.log('🧪 Starting Checkout Vendor ID Issue Test\n');
  
  try {
    // Connect to database
    await connectDB();
    
    // Find test customer
    const customer = await findTestCustomer();
    
    // Check the problematic product
    const product = await checkProduct();
    if (!product) return;
    
    // Create test cart
    await createTestCart(customer, product);
    
    // Simulate frontend cart fetch
    const cart = await simulateFrontendCartFetch(customer._id);
    if (!cart) return;
    
    // Simulate the problematic checkout process
    const originalResult = await simulateCheckoutProcess(cart);
    
    // Test the fixed logic
    const fixedResult = await testFixedVendorExtraction(cart);
    
    // Generate the fix
    generateCheckoutPageFix();
    
    console.log('\n✅ Test completed successfully!');
    
    if (fixedResult) {
      console.log('\n🎉 The FIXED logic successfully resolves the vendor ID issue!');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  } finally {
    // Close database connection
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Additional utility functions for debugging
function analyzeCartStructure(cart) {
  console.log('\n🔍 Detailed Cart Structure Analysis:');
  console.log('='.repeat(50));
  
  if (!cart || !cart.items) {
    console.log('❌ Cart or cart.items is null/undefined');
    return;
  }
  
  cart.items.forEach((item, index) => {
    console.log(`\nItem ${index + 1} Complete Structure:`);
    console.log('- Product Info:');
    console.log(`  - _id: ${item.product._id}`);
    console.log(`  - name: ${item.product.name}`);
    console.log(`  - vendor field: ${JSON.stringify(item.product.vendor)}`);
    
    console.log('- Cart Item Info:');
    console.log(`  - quantity: ${item.quantity}`);
    console.log(`  - priceAtAdd: ${item.priceAtAdd}`);
    console.log(`  - vendor field: ${JSON.stringify(item.vendor)}`);
    
    console.log('- Vendor Analysis:');
    if (item.vendor) {
      console.log(`  - item.vendor exists: YES`);
      console.log(`  - item.vendor type: ${typeof item.vendor}`);
      console.log(`  - item.vendor._id: ${item.vendor._id || 'NOT FOUND'}`);
      console.log(`  - item.vendor.businessName: ${item.vendor.businessName || 'NOT FOUND'}`);
    } else {
      console.log(`  - item.vendor exists: NO`);
    }
    
    if (item.product.vendor) {
      console.log(`  - item.product.vendor exists: YES`);
      console.log(`  - item.product.vendor type: ${typeof item.product.vendor}`);
      console.log(`  - item.product.vendor._id: ${item.product.vendor._id || 'NOT FOUND'}`);
    } else {
      console.log(`  - item.product.vendor exists: NO`);
    }
  });
}

// Export for potential use in other scripts
module.exports = {
  runTest,
  analyzeCartStructure,
  connectDB,
  PRODUCT_ID
};

// Run the test if this script is executed directly
if (require.main === module) {
  runTest();
}
