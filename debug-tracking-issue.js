const mongoose = require('mongoose');
require('dotenv').config({ path: './server/.env' });

async function debugTrackingIssue() {
  try {
    console.log('🔍 DEBUGGING TRACKING ID GENERATION ISSUE');
    console.log('==========================================\n');

    // Connect to database
    const dbUri = process.env.MONGODB_URI || 'mongodb+srv://cojeh66312:<EMAIL>/multi-vendor-ecommerce?retryWrites=true&w=majority&appName=Cluster0Test';
    await mongoose.connect(dbUri);
    console.log('✅ Connected to MongoDB\n');

    // Load models
    const Order = require('./server/src/models/Order');
    const OrderTracking = require('./server/src/models/OrderTracking');
    const User = require('./server/src/models/User');
    const Vendor = require('./server/src/models/Vendor');

    // 1. Check recent orders and their tracking
    console.log('📋 STEP 1: Checking recent orders...');
    const recentOrders = await Order.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('customer', 'firstName lastName email')
      .populate('items.vendor', 'businessName')
      .lean();

    console.log(`Found ${recentOrders.length} recent orders\n`);

    for (const order of recentOrders) {
      console.log(`Order: ${order.orderNumber}`);
      console.log(`Customer: ${order.customer?.firstName} ${order.customer?.lastName}`);
      console.log(`Status: ${order.status}`);
      console.log(`Items: ${order.items?.length || 0}`);
      console.log(`Created: ${order.createdAt}`);

      // Check tracking for this order
      const trackings = await OrderTracking.find({ order: order._id })
        .populate('vendor', 'businessName')
        .lean();

      console.log(`🔍 Tracking records: ${trackings.length}`);
      
      if (trackings.length === 0) {
        console.log('❌ NO TRACKING FOUND - This is the issue!');
        
        // Analyze the order structure
        console.log('\n🔬 Order Analysis:');
        console.log('Items structure:', JSON.stringify(order.items?.map(item => ({
          productId: item.product,
          vendorId: item.vendor?._id || item.vendor,
          quantity: item.quantity,
          status: item.status
        })), null, 2));

        // Check if vendors exist
        if (order.items) {
          for (const item of order.items) {
            const vendorId = item.vendor?._id || item.vendor;
            if (vendorId) {
              const vendor = await Vendor.findById(vendorId);
              console.log(`Vendor ${vendorId}: ${vendor ? '✅ Exists' : '❌ Not found'}`);
            }
          }
        }
      } else {
        console.log('✅ Tracking found:');
        trackings.forEach((track, i) => {
          console.log(`  ${i + 1}. ${track.trackingNumber} (${track.currentStatus})`);
          console.log(`     Vendor: ${track.vendor?.businessName || track.vendor}`);
        });
      }
      console.log('---\n');
    }

    // 2. Test OrderTracking.createTracking method
    console.log('🧪 STEP 2: Testing OrderTracking.createTracking method...');
    
    if (recentOrders.length > 0) {
      const testOrder = recentOrders[0];
      const hasTracking = await OrderTracking.findOne({ order: testOrder._id });
      
      if (!hasTracking && testOrder.items && testOrder.items.length > 0) {
        console.log('Testing tracking creation for order:', testOrder.orderNumber);
        
        try {
          // Get the first vendor from the order
          const firstItem = testOrder.items[0];
          const vendorId = firstItem.vendor?._id || firstItem.vendor;
          
          if (vendorId) {
            const trackingData = {
              orderId: testOrder._id,
              vendorId: vendorId,
              deliveryAddress: {
                street: testOrder.shipping?.address?.street || 'Test Street',
                city: testOrder.shipping?.address?.city || 'Test City',
                state: testOrder.shipping?.address?.state || 'Test State',
                zipCode: testOrder.shipping?.address?.zipCode || '12345',
                country: testOrder.shipping?.address?.country || 'Test Country'
              },
              recipient: {
                name: `${testOrder.shipping?.firstName || 'Test'} ${testOrder.shipping?.lastName || 'User'}`,
                phone: testOrder.billing?.phone || '1234567890',
                email: testOrder.billing?.email || testOrder.customer?.email || '<EMAIL>'
              }
            };

            console.log('Creating tracking with data:', JSON.stringify(trackingData, null, 2));
            
            const tracking = await OrderTracking.createTracking(trackingData);
            console.log('✅ Tracking created successfully!');
            console.log('Tracking Number:', tracking.trackingNumber);
            console.log('Tracking ID:', tracking._id);
            
            // Clean up test tracking
            await OrderTracking.findByIdAndDelete(tracking._id);
            console.log('🧹 Test tracking cleaned up');
            
          } else {
            console.log('❌ No vendor ID found in order items');
          }
        } catch (error) {
          console.log('❌ Failed to create tracking:', error.message);
          console.log('Error details:', error);
        }
      }
    }

    // 3. Check for any issues with the tracking creation during order placement
    console.log('\n🔧 STEP 3: Analyzing potential issues...');
    
    // Check if there are orphaned orders without tracking
    const ordersWithoutTracking = await Order.aggregate([
      {
        $lookup: {
          from: 'ordertrackings',
          localField: '_id',
          foreignField: 'order',
          as: 'trackings'
        }
      },
      {
        $match: {
          trackings: { $size: 0 }
        }
      },
      {
        $project: {
          orderNumber: 1,
          status: 1,
          createdAt: 1,
          itemCount: { $size: '$items' }
        }
      }
    ]);

    console.log(`Orders without tracking: ${ordersWithoutTracking.length}`);
    if (ordersWithoutTracking.length > 0) {
      console.log('Orders missing tracking:');
      ordersWithoutTracking.forEach(order => {
        console.log(`- ${order.orderNumber} (${order.itemCount} items, created: ${order.createdAt})`);
      });
    }

    // 4. Summary and recommendations
    console.log('\n📊 SUMMARY:');
    const totalOrders = await Order.countDocuments();
    const totalTrackings = await OrderTracking.countDocuments();
    const trackingCoverage = totalOrders > 0 ? (totalTrackings / totalOrders * 100).toFixed(1) : 0;
    
    console.log(`Total Orders: ${totalOrders}`);
    console.log(`Total Tracking Records: ${totalTrackings}`);
    console.log(`Tracking Coverage: ${trackingCoverage}%`);
    
    if (trackingCoverage < 100) {
      console.log('\n🔧 RECOMMENDATIONS:');
      console.log('1. Some orders are missing tracking records');
      console.log('2. Check if tracking creation is failing during order placement');
      console.log('3. Verify vendor IDs are properly stored in order items');
      console.log('4. Ensure OrderTracking.createTracking() is being called');
    }

  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    mongoose.connection.close();
  }
}

debugTrackingIssue();
