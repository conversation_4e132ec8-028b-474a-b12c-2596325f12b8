/**
 * Debug script for vendor extraction issue in checkout
 * Product ID: 6881fd806652576061a0be95
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Cart = require('./server/src/models/Cart');
const Product = require('./server/src/models/Product');
const User = require('./server/src/models/User');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('✅ MongoDB connected for debugging');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Debug function to analyze vendor data
const debugVendorExtraction = async (productId) => {
  console.log('\n🔍 DEBUGGING VENDOR EXTRACTION');
  console.log('='.repeat(50));
  console.log(`Product ID: ${productId}`);
  
  try {
    // 1. Check if product exists
    console.log('\n1. Checking product existence...');
    const product = await Product.findById(productId).populate('vendor');
    
    if (!product) {
      console.log('❌ Product not found');
      return;
    }
    
    console.log('✅ Product found:', {
      id: product._id,
      name: product.name,
      status: product.status,
      hasVendor: !!product.vendor,
      vendorType: typeof product.vendor,
      vendorId: product.vendor?._id || product.vendor,
      vendorName: product.vendor?.businessName || 'N/A'
    });
    
    // 2. Check vendor details
    console.log('\n2. Vendor details:');
    if (product.vendor) {
      if (typeof product.vendor === 'object') {
        console.log('✅ Vendor populated:', {
          id: product.vendor._id,
          businessName: product.vendor.businessName,
          email: product.vendor.email,
          userType: product.vendor.userType
        });
      } else {
        console.log('⚠️ Vendor not populated, ID only:', product.vendor);
        
        // Try to fetch vendor separately
        const vendor = await User.findById(product.vendor);
        if (vendor) {
          console.log('✅ Vendor found separately:', {
            id: vendor._id,
            businessName: vendor.businessName,
            email: vendor.email,
            userType: vendor.userType
          });
        } else {
          console.log('❌ Vendor not found in database');
        }
      }
    } else {
      console.log('❌ No vendor associated with product');
    }
    
    // 3. Find carts containing this product
    console.log('\n3. Checking carts containing this product...');
    const cartsWithProduct = await Cart.find({
      'items.product': productId
    }).populate('items.product').populate('items.vendor');
    
    console.log(`Found ${cartsWithProduct.length} cart(s) with this product`);
    
    for (let i = 0; i < cartsWithProduct.length; i++) {
      const cart = cartsWithProduct[i];
      console.log(`\nCart ${i + 1} (Customer: ${cart.customer}):`);
      
      const itemsWithProduct = cart.items.filter(item => 
        item.product._id.toString() === productId
      );
      
      itemsWithProduct.forEach((item, index) => {
        console.log(`  Item ${index + 1}:`, {
          productId: item.product._id,
          productName: item.product.name,
          quantity: item.quantity,
          priceAtAdd: item.priceAtAdd,
          hasVendorField: !!item.vendor,
          vendorType: typeof item.vendor,
          vendorValue: item.vendor,
          vendorPopulated: typeof item.vendor === 'object' ? {
            id: item.vendor._id,
            businessName: item.vendor.businessName,
            email: item.vendor.email
          } : 'Not populated'
        });
      });
    }
    
    // 4. Simulate checkout vendor extraction
    console.log('\n4. Simulating checkout vendor extraction...');
    
    if (cartsWithProduct.length > 0) {
      const testCart = cartsWithProduct[0];
      const testItem = testCart.items.find(item => 
        item.product._id.toString() === productId
      );
      
      if (testItem) {
        console.log('\nTesting vendor extraction strategies:');
        
        // Strategy 1: Direct vendor from cart item
        let vendorId = null;
        let strategy = 'none';
        
        if (testItem.vendor) {
          if (typeof testItem.vendor === 'object' && testItem.vendor._id) {
            vendorId = testItem.vendor._id.toString();
            strategy = 'item.vendor._id';
          } else if (typeof testItem.vendor === 'string' && testItem.vendor.length === 24) {
            vendorId = testItem.vendor;
            strategy = 'item.vendor (string)';
          }
        }
        
        console.log(`Strategy 1 (item.vendor): ${strategy} -> ${vendorId || 'FAILED'}`);
        
        // Strategy 2: Vendor from populated product
        if (!vendorId && testItem.product?.vendor) {
          if (typeof testItem.product.vendor === 'object' && testItem.product.vendor._id) {
            vendorId = testItem.product.vendor._id.toString();
            strategy = 'item.product.vendor._id';
          } else if (typeof testItem.product.vendor === 'string' && testItem.product.vendor.length === 24) {
            vendorId = testItem.product.vendor;
            strategy = 'item.product.vendor (string)';
          }
        }
        
        console.log(`Strategy 2 (product.vendor): ${strategy} -> ${vendorId || 'FAILED'}`);
        
        // Final result
        if (vendorId) {
          console.log(`✅ Vendor extraction successful: ${vendorId} (via ${strategy})`);
        } else {
          console.log('❌ Vendor extraction FAILED - This is the root cause!');
          
          // Detailed debug info
          console.log('\nDetailed debug info:', {
            itemVendor: {
              exists: !!testItem.vendor,
              type: typeof testItem.vendor,
              value: testItem.vendor,
              hasId: !!(testItem.vendor?._id),
              id: testItem.vendor?._id
            },
            productVendor: {
              exists: !!(testItem.product?.vendor),
              type: typeof testItem.product?.vendor,
              value: testItem.product?.vendor,
              hasId: !!(testItem.product?.vendor?._id),
              id: testItem.product?.vendor?._id
            }
          });
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Debug error:', error);
  }
};

// Test cart creation with proper vendor data
const testCartCreation = async (productId, customerId = null) => {
  console.log('\n🧪 TESTING CART CREATION');
  console.log('='.repeat(50));
  
  try {
    // Find a customer if not provided
    if (!customerId) {
      const customer = await User.findOne({ userType: 'customer' });
      if (!customer) {
        console.log('❌ No customer found for testing');
        return;
      }
      customerId = customer._id;
    }
    
    console.log(`Using customer: ${customerId}`);
    
    // Get product with vendor
    const product = await Product.findById(productId).populate('vendor');
    if (!product) {
      console.log('❌ Product not found');
      return;
    }
    
    console.log('Product vendor info:', {
      hasVendor: !!product.vendor,
      vendorId: product.vendor?._id || product.vendor,
      vendorName: product.vendor?.businessName
    });
    
    // Create or get cart
    let cart = await Cart.findOne({ customer: customerId });
    if (!cart) {
      cart = new Cart({ customer: customerId });
    }
    
    // Add item with proper vendor
    const vendorId = product.vendor?._id || product.vendor;
    const price = product.pricing?.salePrice || product.pricing?.basePrice || 100;
    
    console.log(`Adding item with vendor: ${vendorId}`);
    
    await cart.addItem(productId, vendorId, 1, price);
    
    // Fetch cart with populated data
    const populatedCart = await Cart.findByCustomer(customerId);
    
    console.log('\nCart after adding item:');
    const addedItem = populatedCart.items.find(item => 
      item.product._id.toString() === productId
    );
    
    if (addedItem) {
      console.log('✅ Item added successfully:', {
        productId: addedItem.product._id,
        productName: addedItem.product.name,
        vendorId: addedItem.vendor,
        vendorPopulated: typeof addedItem.vendor === 'object' ? {
          id: addedItem.vendor._id,
          businessName: addedItem.vendor.businessName
        } : 'Not populated',
        quantity: addedItem.quantity,
        priceAtAdd: addedItem.priceAtAdd
      });
      
      // Test vendor extraction on this fresh item
      console.log('\nTesting vendor extraction on fresh item:');
      let vendorId = null;
      let strategy = 'none';
      
      if (addedItem.vendor) {
        if (typeof addedItem.vendor === 'object' && addedItem.vendor._id) {
          vendorId = addedItem.vendor._id.toString();
          strategy = 'item.vendor._id';
        } else if (typeof addedItem.vendor === 'string' && addedItem.vendor.length === 24) {
          vendorId = addedItem.vendor;
          strategy = 'item.vendor (string)';
        }
      }
      
      if (vendorId) {
        console.log(`✅ Vendor extraction successful: ${vendorId} (via ${strategy})`);
      } else {
        console.log('❌ Vendor extraction still failing!');
      }
    } else {
      console.log('❌ Item not found in cart after adding');
    }
    
  } catch (error) {
    console.error('❌ Test error:', error);
  }
};

// Main debug function
const main = async () => {
  await connectDB();
  
  const productId = '6881fd806652576061a0be95';
  
  await debugVendorExtraction(productId);
  await testCartCreation(productId);
  
  console.log('\n🏁 Debug complete');
  process.exit(0);
};

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  debugVendorExtraction,
  testCartCreation
};