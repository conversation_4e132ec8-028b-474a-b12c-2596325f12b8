/**
 * Test script to verify cart vendor population fix
 */

const mongoose = require('mongoose');
const axios = require('axios');
require('dotenv').config();

// Import models
const Cart = require('./src/models/Cart');
const Product = require('./src/models/Product');
const User = require('./src/models/User');
const Vendor = require('./src/models/Vendor');

const API_BASE_URL = process.env.API_URL || 'http://localhost:5000/api';

// Test credentials
const TEST_CUSTOMER = {
  email: '<EMAIL>',
  password: 'Free@009'
};

let authToken = null;

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('✅ MongoDB connected');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// API call helper
const apiCall = async (method, endpoint, data = null) => {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...(authToken && { Authorization: `Bearer ${authToken}` })
      }
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, error.response?.data?.message || error.message);
    throw error;
  }
};

// Login
const login = async () => {
  console.log('🔐 Logging in...');
  const response = await apiCall('POST', '/auth/login', TEST_CUSTOMER);
  authToken = response.data?.token || response.token;
  console.log('✅ Login successful');
  return response;
};

// Test the fixed cart population
const testCartPopulation = async () => {
  console.log('\n🧪 TESTING CART POPULATION FIX');
  console.log('='.repeat(50));
  
  try {
    // Find customer
    const customer = await User.findOne({ email: TEST_CUSTOMER.email });
    console.log('✅ Customer found:', customer._id);
    
    // Test the fixed findByCustomerWithCurrency method
    console.log('\n1. Testing direct database query with proper population:');
    
    const cart = await Cart.findOne({ customer: customer._id })
      .populate('items.product', 'name pricing images inventory.quantity status')
      .populate('items.vendor', 'businessName businessEmail _id status');
    
    if (cart && cart.items.length > 0) {
      console.log('✅ Cart found with', cart.items.length, 'items');
      
      cart.items.forEach((item, index) => {
        console.log(`\nItem ${index + 1}:`, {
          productName: item.product?.name,
          hasVendor: !!item.vendor,
          vendorType: typeof item.vendor,
          vendorId: item.vendor?._id,
          vendorName: item.vendor?.businessName,
          vendorStatus: item.vendor?.status
        });
      });
    } else {
      console.log('❌ No cart or items found');
      return false;
    }
    
    // Test API response
    console.log('\n2. Testing API response:');
    const apiResponse = await apiCall('GET', '/customer/cart');
    
    if (apiResponse.data && apiResponse.data.items) {
      console.log('✅ API cart response received');
      
      apiResponse.data.items.forEach((item, index) => {
        console.log(`\nAPI Item ${index + 1}:`, {
          productName: item.product?.name,
          hasVendor: !!item.vendor,
          vendorType: typeof item.vendor,
          vendorId: item.vendor?._id,
          vendorName: item.vendor?.businessName
        });
        
        // Test vendor extraction
        let vendorId = null;
        let strategy = 'none';
        
        if (item.vendor) {
          if (typeof item.vendor === 'object' && item.vendor._id) {
            vendorId = item.vendor._id.toString();
            strategy = 'item.vendor._id';
          } else if (typeof item.vendor === 'string' && item.vendor.length === 24) {
            vendorId = item.vendor;
            strategy = 'item.vendor (string)';
          }
        }
        
        if (vendorId) {
          console.log(`  ✅ Vendor extraction successful: ${vendorId} (via ${strategy})`);
        } else {
          console.log('  ❌ Vendor extraction FAILED!');
        }
      });
    } else {
      console.log('❌ No API response data');
      return false;
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Test error:', error);
    return false;
  }
};

// Test order placement
const testOrderPlacement = async () => {
  console.log('\n🛍️ TESTING ORDER PLACEMENT');
  console.log('='.repeat(50));
  
  try {
    // Get user profile
    const userResponse = await apiCall('GET', '/customer/profile');
    const user = userResponse.data;
    
    // Get cart
    const cartResponse = await apiCall('GET', '/customer/cart');
    const cart = cartResponse.data;
    
    if (!cart || !cart.items || cart.items.length === 0) {
      console.log('❌ Cart is empty');
      return false;
    }
    
    // Process order items with vendor extraction
    const orderItems = cart.items.map((item, index) => {
      console.log(`\nProcessing item ${index + 1}: ${item.product?.name}`);
      
      let vendorId = null;
      let strategy = 'none';
      
      // Strategy 1: Direct vendor from cart item
      if (item.vendor) {
        if (typeof item.vendor === 'object' && item.vendor._id) {
          vendorId = item.vendor._id.toString();
          strategy = 'item.vendor._id';
        } else if (typeof item.vendor === 'string' && item.vendor.length === 24) {
          vendorId = item.vendor;
          strategy = 'item.vendor (string)';
        }
      }
      
      // Strategy 2: Vendor from populated product
      if (!vendorId && item.product?.vendor) {
        if (typeof item.product.vendor === 'object' && item.product.vendor._id) {
          vendorId = item.product.vendor._id.toString();
          strategy = 'item.product.vendor._id';
        } else if (typeof item.product.vendor === 'string' && item.product.vendor.length === 24) {
          vendorId = item.product.vendor;
          strategy = 'item.product.vendor (string)';
        }
      }
      
      if (!vendorId) {
        throw new Error(`Vendor information is missing for product: ${item.product?.name}`);
      }
      
      console.log(`✅ Vendor ID extracted: ${vendorId} (via ${strategy})`);
      
      return {
        product: item.product._id,
        vendor: vendorId,
        name: item.product.name,
        quantity: item.quantity,
        unitPrice: item.priceAtAdd,
        totalPrice: item.priceAtAdd * item.quantity
      };
    });
    
    console.log('\n✅ All items processed successfully!');
    
    // Prepare order data
    const subtotal = cart.totalAmount || 0;
    const protectPromiseFee = 28;
    const total = subtotal + protectPromiseFee;
    
    const orderData = {
      items: orderItems,
      billing: {
        firstName: user.firstName || 'Test',
        lastName: user.lastName || 'User',
        email: user.email || TEST_CUSTOMER.email,
        phone: user.phone || '1234567890',
        address: {
          street: user.address || 'Test Address',
          city: user.city || 'Test City',
          state: user.state || 'Test State',
          zipCode: user.zipCode || '12345',
          country: user.country || 'India'
        }
      },
      shipping: {
        firstName: user.firstName || 'Test',
        lastName: user.lastName || 'User',
        address: {
          street: user.address || 'Test Address',
          city: user.city || 'Test City',
          state: user.state || 'Test State',
          zipCode: user.zipCode || '12345',
          country: user.country || 'India'
        },
        method: 'standard',
        cost: 0
      },
      payment: {
        method: 'cod',
        status: 'pending'
      },
      pricing: {
        subtotal: subtotal,
        tax: 0,
        shipping: protectPromiseFee,
        discount: 0,
        total: total
      },
      customerNotes: 'Test order after cart fix',
      estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
    };
    
    console.log('\n📦 Placing order...');
    const response = await apiCall('POST', '/customer/orders', orderData);
    
    if (response.success) {
      console.log('🎉 ORDER PLACED SUCCESSFULLY!');
      console.log('Order details:', {
        orderId: response.data.order.orderNumber,
        total: response.data.order.pricing.total,
        status: response.data.order.status
      });
      return true;
    } else {
      console.log('❌ Order placement failed:', response.message);
      return false;
    }
    
  } catch (error) {
    console.log('❌ Order placement error:', error.message);
    return false;
  }
};

// Main function
const main = async () => {
  console.log('🚀 TESTING CART VENDOR POPULATION FIX');
  console.log('='.repeat(60));
  
  try {
    await connectDB();
    await login();
    
    const cartTestPassed = await testCartPopulation();
    
    if (cartTestPassed) {
      console.log('\n✅ Cart population test PASSED!');
      
      const orderTestPassed = await testOrderPlacement();
      
      if (orderTestPassed) {
        console.log('\n🎉 ALL TESTS PASSED! The vendor extraction issue is FIXED!');
      } else {
        console.log('\n❌ Order placement test failed');
      }
    } else {
      console.log('\n❌ Cart population test failed');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🏁 Test complete');
  }
};

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testCartPopulation, testOrderPlacement };