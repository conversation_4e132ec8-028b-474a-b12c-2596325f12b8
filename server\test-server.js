const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(204);
    res.end();
    return;
  }
  
  console.log(`${req.method} ${req.url}`);
  
  if (req.url === '/' || req.url === '/test') {
    try {
      const html = fs.readFileSync(path.join(__dirname, 'carousel-upload-test.html'), 'utf8');
      res.writeHead(200, { 'Content-Type': 'text/html' });
      res.end(html);
    } catch (error) {
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('Test file not found');
    }
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not found - try http://localhost:3001/test');
  }
});

const PORT = 3001;
server.listen(PORT, () => {
  console.log(`\n🌐 Test server running at http://localhost:${PORT}`);
  console.log(`📝 Open http://localhost:${PORT}/test to test carousel upload`);
  console.log('\n📋 Instructions:');
  console.log('1. Make sure your main server is running on port 5000');
  console.log('2. Open the test URL in your browser');
  console.log('3. Select an image file and fill in the form');
  console.log('4. Click Upload Image to test');
  console.log('\nPress Ctrl+C to stop this server\n');
});