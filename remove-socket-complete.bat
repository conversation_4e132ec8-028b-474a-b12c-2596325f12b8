@echo off
echo ========================================
echo REMOVING SOCKET.IO COMPLETELY
echo ========================================
echo.
echo This will remove all socket.io dependencies and replace with refresh mechanisms
echo.

pause

echo Step 1: Stopping all Node.js processes...
taskkill /f /im node.exe 2>nul
timeout /t 2 /nobreak >nul

echo Step 2: Removing socket.io from server...
cd server
if exist "package.json" (
    echo Removing socket.io from server package.json...
    npm uninstall socket.io 2>nul
)

echo Step 3: Removing socket.io-client from client...
cd ..\client
if exist "package.json" (
    echo Removing socket.io-client from client package.json...
    npm uninstall socket.io-client 2>nul
)

echo Step 4: Cleaning caches...
cd ..
cd server
npm cache clean --force 2>nul
cd ..\client
npm cache clean --force 2>nul

echo Step 5: Removing socket files...
cd ..
if exist "server\src\socket" rmdir /s /q "server\src\socket"
if exist "server\src\services\socketService.js" del "server\src\services\socketService.js"
if exist "client\src\services\socketService.js" del "client\src\services\socketService.js"
if exist "client\src\services\socketService-disabled.js" del "client\src\services\socketService-disabled.js"

echo Step 6: Installing dependencies...
cd server
npm install
cd ..\client
npm install

echo ========================================
echo SOCKET.IO REMOVAL COMPLETED!
echo ========================================
echo.
echo Socket.io has been completely removed and replaced with:
echo - Auto-refresh mechanisms
echo - Polling for updates
echo - Manual refresh buttons
echo.
echo Starting development server...
cd ..\client
npm run dev

pause