# Multi-Vendor eCommerce Platform

A modern, secure, and scalable multi-vendor eCommerce platform built with React, Node.js, Express, and MongoDB.

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud)
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd multi-vendor-eCommerce
   ```

2. **Setup Server**
   ```bash
   cd server
   npm install
   cp .env.example .env
   # Edit .env with your configuration
   npm run dev
   ```

3. **Setup Client**
   ```bash
   cd ../client
   npm install
   npm run dev
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000/api

## 🔧 Configuration

### Environment Variables

Copy `server/.env.example` to `server/.env` and configure:

```bash
# Database
MONGODB_URI=mongodb://localhost:27017/multi-vendor-ecommerce

# JWT Secrets
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-super-secret-refresh-key

# Email Configuration (Required for email verification)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Frontend URL
FRONTEND_URL=http://localhost:3000
```

### SMTP Setup (Gmail)

1. Enable 2-Factor Authentication in your Google Account
2. Generate an App Password:
   - Go to Google Account → Security → 2-Step Verification → App passwords
   - Select "Mail" and your device
   - Copy the 16-character password
3. Use this password in `SMTP_PASS`

## 🧪 Testing

### Test Authentication API
```bash
cd server
npm run test:auth
```

### Manual Testing
1. Register a new user at http://localhost:3000/auth
2. Check your email for verification link
3. Test login/logout functionality
4. Try password reset flow

## 📚 Features

### ✅ Authentication System
- **User Registration & Login** - Secure authentication for users and vendors
- **Email Verification** - Required email verification with professional templates
- **Password Reset** - Secure password reset via email
- **JWT Tokens** - Access and refresh token system
- **Account Security** - Rate limiting, account locking, password strength validation

### ✅ User Management
- **User Types** - Support for customers, vendors, and admins
- **Vendor Approval** - Admin approval system for vendor accounts
- **Profile Management** - Complete user profile system
- **Role-Based Access** - Different permissions for different user types

### ✅ Security Features
- **Input Validation** - Comprehensive validation and sanitization
- **Rate Limiting** - Protection against brute force attacks
- **XSS Protection** - Cross-site scripting prevention
- **CORS Security** - Secure cross-origin resource sharing
- **Password Hashing** - Bcrypt with salt rounds

## 📁 Project Structure

```
multi-vendor-eCommerce/
├── server/                 # Backend API
│   ├── schema/            # Database schemas
│   ├── src/
│   │   ├── controllers/   # Route controllers
│   │   ├── middleware/    # Custom middleware
│   │   ├── routes/        # API routes
│   │   └── utils/         # Utility functions
│   ├── .env.example       # Environment template
│   ├── index.js           # Server entry point
│   └── package.json
├── client/                # Frontend React app
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── contexts/      # React contexts
│   │   ├── pages/         # Page components
│   │   └── utils/         # Utility functions
│   └── package.json
└── README.md
```

## 🔗 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user/vendor
- `POST /api/auth/login` - User login
- `GET /api/auth/verify-email` - Verify email address
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password
- `GET /api/auth/profile` - Get user profile
- `POST /api/auth/logout` - User logout

### Health Check
- `GET /api/auth/health` - API health status

## 🛠️ Development

### Server Development
```bash
cd server
npm run dev  # Starts with nodemon for auto-reload
```

### Client Development
```bash
cd client
npm run dev  # Starts Vite dev server
```

### Database
Make sure MongoDB is running:
```bash
# Local MongoDB
mongod

# Or use MongoDB Atlas (cloud)
# Update MONGODB_URI in .env
```

## 📖 Documentation

- **[Authentication Guide](AUTH_IMPLEMENTATION_GUIDE.md)** - Comprehensive authentication documentation
- **API Testing** - Use the included Postman collection or test script
- **SMTP Setup** - Detailed email configuration guide

## 🚀 Deployment

### Production Checklist
- [ ] Set strong JWT secrets
- [ ] Configure production SMTP
- [ ] Set up MongoDB Atlas
- [ ] Configure CORS for production domain
- [ ] Set NODE_ENV=production
- [ ] Enable HTTPS
- [ ] Set up monitoring and logging

### Environment Variables for Production
```bash
NODE_ENV=production
MONGODB_URI=mongodb+srv://...
FRONTEND_URL=https://yourdomain.com
JWT_SECRET=strong-production-secret
JWT_REFRESH_SECRET=strong-production-refresh-secret
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

If you encounter any issues:

1. Check the [Authentication Guide](AUTH_IMPLEMENTATION_GUIDE.md)
2. Verify your environment configuration
3. Test with the included test script: `npm run test:auth`
4. Check server logs for detailed error messages

## 🔮 Roadmap

- [ ] Two-Factor Authentication
- [ ] Social Login (Google, Facebook, GitHub)
- [ ] Admin Dashboard
- [ ] Product Management
- [ ] Order Processing
- [ ] Payment Integration
- [ ] Real-time Notifications
- [ ] Advanced Analytics

---

**Built with ❤️ for modern eCommerce needs**