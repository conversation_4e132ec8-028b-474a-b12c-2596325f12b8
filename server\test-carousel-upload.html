<!DOCTYPE html>
<html>
<head>
    <title>Test Carousel Upload</title>
</head>
<body>
    <h1>Test Carousel Image Upload</h1>
    
    <form id="uploadForm" enctype="multipart/form-data">
        <div>
            <label for="image">Image:</label>
            <input type="file" name="image" id="image" accept="image/*" required>
        </div>
        
        <div>
            <label for="title">Title:</label>
            <input type="text" name="title" id="title" value="Test HTML Upload">
        </div>
        
        <div>
            <label for="description">Description:</label>
            <input type="text" name="description" id="description" value="Test description from HTML">
        </div>
        
        <div>
            <label for="linkUrl">Link URL:</label>
            <input type="text" name="linkUrl" id="linkUrl" value="https://example.com">
        </div>
        
        <div>
            <button type="submit">Upload Image</button>
        </div>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            
            try {
                // First login to get token
                const loginResponse = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123'
                    })
                });
                
                const loginData = await loginResponse.json();
                console.log('Login response:', loginData);
                
                if (!loginData.success) {
                    throw new Error('Login failed');
                }
                
                const token = loginData.data.token;
                
                // Now upload the image
                const uploadResponse = await fetch('http://localhost:5000/api/admin/homepage-settings/carousel', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });
                
                const uploadData = await uploadResponse.json();
                console.log('Upload response:', uploadData);
                
                if (uploadData.success) {
                    resultDiv.innerHTML = '<p style="color: green;">✅ Upload successful!</p>';
                } else {
                    resultDiv.innerHTML = `<p style="color: red;">❌ Upload failed: ${uploadData.error || uploadData.message}</p>`;
                }
                
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `<p style="color: red;">❌ Error: ${error.message}</p>`;
            }
        });
    </script>
</body>
</html>
