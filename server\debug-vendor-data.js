const mongoose = require('mongoose');
const Vendor = require('./models/Vendor');
const Review = require('./models/Review');
const Product = require('./models/Product');
const Customer = require('./models/Customer');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/ecommerce', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function debugVendorData() {
  try {
    console.log('=== VENDOR DATA DEBUG ===\n');

    // 1. List all vendors
    console.log('1. EXISTING VENDORS:');
    console.log('====================');
    const vendors = await Vendor.find({}).select('_id name email status');
    if (vendors.length === 0) {
      console.log('No vendors found in the database');
    } else {
      vendors.forEach((vendor, index) => {
        console.log(`${index + 1}. ID: ${vendor._id}`);
        console.log(`   Name: ${vendor.name}`);
        console.log(`   Email: ${vendor.email}`);
        console.log(`   Status: ${vendor.status}`);
        console.log('');
      });
    }

    // 2. List all products with their vendor assignments
    console.log('\n2. PRODUCTS AND VENDOR ASSIGNMENTS:');
    console.log('===================================');
    const products = await Product.find({}).select('_id name vendor price');
    if (products.length === 0) {
      console.log('No products found in the database');
    } else {
      for (const product of products) {
        console.log(`Product: ${product.name} (ID: ${product._id})`);
        console.log(`  Vendor ID: ${product.vendor}`);
        console.log(`  Price: $${product.price}`);
        
        // Check if vendor exists
        if (product.vendor) {
          const vendorExists = await Vendor.findById(product.vendor);
          console.log(`  Vendor exists: ${vendorExists ? 'YES' : 'NO'}`);
          if (vendorExists) {
            console.log(`  Vendor name: ${vendorExists.name}`);
          }
        } else {
          console.log('  Vendor exists: NO VENDOR ASSIGNED');
        }
        console.log('');
      }
    }

    // 3. List all reviews with their assignments
    console.log('\n3. REVIEWS AND ASSIGNMENTS:');
    console.log('===========================');
    const reviews = await Review.find({}).select('_id product customer vendor rating comment createdAt');
    if (reviews.length === 0) {
      console.log('No reviews found in the database');
    } else {
      for (const review of reviews) {
        console.log(`Review ID: ${review._id}`);
        console.log(`  Product ID: ${review.product}`);
        console.log(`  Customer ID: ${review.customer}`);
        console.log(`  Vendor ID: ${review.vendor}`);
        console.log(`  Rating: ${review.rating}/5`);
        console.log(`  Comment: ${review.comment?.substring(0, 50)}${review.comment?.length > 50 ? '...' : ''}`);
        console.log(`  Created: ${review.createdAt}`);
        
        // Check if referenced entities exist
        const productExists = await Product.findById(review.product);
        const customerExists = await Customer.findById(review.customer);
        const vendorExists = review.vendor ? await Vendor.findById(review.vendor) : null;
        
        console.log(`  Product exists: ${productExists ? 'YES' : 'NO'}`);
        console.log(`  Customer exists: ${customerExists ? 'YES' : 'NO'}`);
        console.log(`  Vendor exists: ${review.vendor ? (vendorExists ? 'YES' : 'NO') : 'NO VENDOR ASSIGNED'}`);
        
        if (productExists && productExists.vendor && review.vendor) {
          const vendorMatch = productExists.vendor.toString() === review.vendor.toString();
          console.log(`  Vendor matches product vendor: ${vendorMatch ? 'YES' : 'NO'}`);
          if (!vendorMatch) {
            console.log(`    Product vendor: ${productExists.vendor}`);
            console.log(`    Review vendor: ${review.vendor}`);
          }
        }
        console.log('');
      }
    }

    // 4. Summary statistics
    console.log('\n4. SUMMARY STATISTICS:');
    console.log('======================');
    console.log(`Total vendors: ${vendors.length}`);
    console.log(`Total products: ${products.length}`);
    console.log(`Total reviews: ${reviews.length}`);
    
    const productsWithVendors = products.filter(p => p.vendor).length;
    const reviewsWithVendors = reviews.filter(r => r.vendor).length;
    
    console.log(`Products with vendors: ${productsWithVendors}/${products.length}`);
    console.log(`Reviews with vendors: ${reviewsWithVendors}/${reviews.length}`);

    // 5. Check for orphaned reviews (reviews without valid vendor/product references)
    console.log('\n5. ORPHANED REVIEWS CHECK:');
    console.log('==========================');
    let orphanedCount = 0;
    for (const review of reviews) {
      const productExists = await Product.findById(review.product);
      const vendorExists = review.vendor ? await Vendor.findById(review.vendor) : null;
      
      const isOrphaned = !productExists || (review.vendor && !vendorExists);
      if (isOrphaned) {
        orphanedCount++;
        console.log(`Orphaned review: ${review._id}`);
        console.log(`  Missing product: ${!productExists}`);
        console.log(`  Missing vendor: ${review.vendor && !vendorExists}`);
      }
    }
    
    if (orphanedCount === 0) {
      console.log('No orphaned reviews found');
    } else {
      console.log(`Found ${orphanedCount} orphaned reviews`);
    }

    console.log('\n=== DEBUG COMPLETE ===');

  } catch (error) {
    console.error('Error during debug:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

debugVendorData();
