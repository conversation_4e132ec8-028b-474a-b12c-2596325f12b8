const mongoose = require('mongoose');
require('dotenv').config();
const { Vendor } = require('./src/models');

const seedCommissionData = async () => {
  try {
    console.log('🌱 Starting commission data seeding...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('📊 Connected to MongoDB');

    // Update existing vendors with commission data
    const vendors = await Vendor.find().limit(5);
    
    if (vendors.length === 0) {
      console.log('⚠️ No vendors found. Create some vendors first.');
      return;
    }

    const commissionUpdates = [
      { totalEarned: 25000, totalPaid: 15000, pendingAmount: 10000 },
      { totalEarned: 18500, totalPaid: 12000, pendingAmount: 6500 },
      { totalEarned: 32000, totalPaid: 25000, pendingAmount: 7000 },
      { totalEarned: 14500, totalPaid: 8000, pendingAmount: 6500 },
      { totalEarned: 28000, totalPaid: 20000, pendingAmount: 8000 }
    ];

    for (let i = 0; i < vendors.length && i < commissionUpdates.length; i++) {
      const vendor = vendors[i];
      const commissionData = commissionUpdates[i];
      
      try {
        // Update using $set to ensure proper data structure
        await Vendor.findByIdAndUpdate(vendor._id, {
          $set: {
            'commission': {
              rate: 15,
              type: 'percentage',
              fixedAmount: 0,
              totalEarned: commissionData.totalEarned,
              totalPaid: commissionData.totalPaid,
              pendingAmount: commissionData.pendingAmount,
              payoutHistory: []
            }
          }
        });
        
        console.log(`✅ Updated commission for ${vendor.businessName}: ₹${commissionData.pendingAmount} pending`);
      } catch (error) {
        console.error(`❌ Error updating ${vendor.businessName}:`, error.message);
      }
    }

    console.log('🎉 Commission data seeding completed!');
    console.log('📊 Total pending commission should now appear in admin dashboard');
    
  } catch (error) {
    console.error('❌ Error seeding commission data:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📊 Disconnected from MongoDB');
  }
};

// Run the seeding
if (require.main === module) {
  seedCommissionData();
}

module.exports = seedCommissionData;
