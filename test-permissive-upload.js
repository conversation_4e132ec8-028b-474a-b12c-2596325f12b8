const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

async function testPermissiveUpload() {
  console.log('🔍 Testing Permissive Carousel Upload...\n');
  
  const BASE_URL = 'http://localhost:5000';
  const ADMIN_CREDENTIALS = {
    email: '<EMAIL>',
    password: 'password@admin123'
  };
  
  try {
    // Step 1: Login as admin
    console.log('1. Logging in as admin...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, ADMIN_CREDENTIALS);
    
    let adminToken = null;
    if (loginResponse.data.success) {
      adminToken = loginResponse.data.data?.token || loginResponse.data.token;
    }
    
    if (!adminToken) {
      console.log('❌ Could not extract token from login response');
      return;
    }
    
    console.log('✅ Successfully logged in as admin');
    
    // Step 2: Test with the JPEG file that was failing before
    console.log('\n2. Testing with JPEG file (previously failing)...');
    
    const jpegPath = path.join(__dirname, 'test-image.jpg');
    if (!fs.existsSync(jpegPath)) {
      console.log('❌ JPEG test file not found. Run create-real-test-image.js first');
      return;
    }
    
    const jpegForm = new FormData();
    jpegForm.append('image', fs.createReadStream(jpegPath), {
      filename: 'test-carousel-permissive.jpg',
      contentType: 'image/jpeg'
    });
    jpegForm.append('title', 'Permissive JPEG Test');
    jpegForm.append('description', 'Testing JPEG upload with permissive validation');
    jpegForm.append('linkUrl', 'https://example.com/permissive-test');
    
    try {
      console.log('📤 Uploading JPEG with permissive settings...');
      const jpegResponse = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, jpegForm, {
        headers: {
          ...jpegForm.getHeaders(),
          'Authorization': `Bearer ${adminToken}`
        },
        timeout: 60000
      });
      
      console.log('✅ JPEG UPLOAD NOW SUCCESSFUL!');
      console.log(`📥 Status: ${jpegResponse.status}`);
      console.log('📄 Response:');
      console.log(JSON.stringify(jpegResponse.data, null, 2));
      
    } catch (jpegError) {
      console.log('❌ JPEG upload still failed:');
      console.log(`📥 Status: ${jpegError.response?.status}`);
      console.log('📄 Error:');
      console.log(JSON.stringify(jpegError.response?.data, null, 2));
      
      // Check if it's a different error now
      if (jpegError.response?.data?.error !== 'Invalid image file') {
        console.log('✅ Progress: Error changed from "Invalid image file" to something else');
      }
    }
    
    // Step 3: Test with a larger file to check size limits
    console.log('\n3. Testing file size limits...');
    
    // Create a larger test file
    const largeImagePath = path.join(__dirname, 'test-large-image.jpg');
    const largeImageData = Buffer.alloc(12 * 1024 * 1024, 0xFF); // 12MB file
    fs.writeFileSync(largeImagePath, largeImageData);
    
    const largeForm = new FormData();
    largeForm.append('image', fs.createReadStream(largeImagePath), {
      filename: 'large-test.jpg',
      contentType: 'image/jpeg'
    });
    largeForm.append('title', 'Large File Test');
    largeForm.append('description', 'Testing file size limits');
    largeForm.append('linkUrl', '');
    
    try {
      console.log('📤 Uploading large file (12MB)...');
      const largeResponse = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, largeForm, {
        headers: {
          ...largeForm.getHeaders(),
          'Authorization': `Bearer ${adminToken}`
        },
        timeout: 60000
      });
      
      console.log('✅ Large file upload successful (unexpected)');
      
    } catch (largeError) {
      if (largeError.response?.data?.error?.includes('size')) {
        console.log('✅ Large file correctly rejected due to size limit');
      } else {
        console.log('❌ Large file failed for different reason:', largeError.response?.data);
      }
    }
    
    // Cleanup large file
    if (fs.existsSync(largeImagePath)) {
      fs.unlinkSync(largeImagePath);
    }
    
    // Step 4: Test with no file (should still fail validation)
    console.log('\n4. Testing validation (no file)...');
    
    const noFileForm = new FormData();
    noFileForm.append('title', 'No File Test');
    noFileForm.append('description', 'Testing without file');
    noFileForm.append('linkUrl', '');
    
    try {
      const noFileResponse = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, noFileForm, {
        headers: {
          ...noFileForm.getHeaders(),
          'Authorization': `Bearer ${adminToken}`
        },
        timeout: 10000
      });
      
      console.log('❌ No file upload succeeded (should have failed)');
      
    } catch (noFileError) {
      if (noFileError.response?.data?.message === 'Image file is required') {
        console.log('✅ No file correctly rejected');
      } else {
        console.log('⚠️ No file rejected for different reason:', noFileError.response?.data);
      }
    }
    
    console.log('\n📋 PERMISSIVE UPLOAD TEST SUMMARY:');
    console.log('');
    console.log('Changes made:');
    console.log('✅ Removed strict allowed_formats restriction from Cloudinary');
    console.log('✅ Added resource_type: "auto" for auto-detection');
    console.log('✅ More permissive file filter (allows more extensions)');
    console.log('✅ Increased file size limits to 10MB');
    console.log('✅ Better error handling and logging');
    console.log('');
    console.log('The carousel upload should now accept:');
    console.log('- JPG, JPEG, PNG, GIF, WebP, BMP, TIFF, SVG, ICO');
    console.log('- AVIF, HEIC, HEIF (modern formats)');
    console.log('- Files up to 10MB in size');
    console.log('- Files with or without proper MIME types');
    
  } catch (error) {
    console.error('❌ Test script failed:', error.message);
  }
}

// Run the test
testPermissiveUpload().catch(console.error);