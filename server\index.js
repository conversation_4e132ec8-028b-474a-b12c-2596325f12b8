require('dotenv').config();
const http = require('http');

// Import database connection
const connectDB = require('./src/utils/database');

// Import the main app
const app = require('./src/app');

// Import refresh service (Socket.IO replacement)
const refreshService = require('./src/services/refreshService');

// Connect to database
connectDB();

const server = http.createServer(app);
const PORT = process.env.PORT || 5000;

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received. Shutting down gracefully...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('SIGINT received. Shutting down gracefully...');
    process.exit(0);
});

server.listen(PORT, () => {
    console.log(`🚀 Server is running on port ${PORT}`);
    console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`📱 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:5173'}`);
    console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
    console.log(`🔄 Refresh service enabled (Socket.IO replacement)`);
    console.log(`\n💡 Local Development URLs:`);
    console.log(`   Frontend: http://localhost:5173`);
    console.log(`   Backend:  http://localhost:${PORT}`);
    console.log(`   API:      http://localhost:${PORT}/api`);
});

module.exports = app;