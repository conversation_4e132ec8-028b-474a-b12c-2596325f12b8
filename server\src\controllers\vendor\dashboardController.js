const { Vendor, Product, Order } = require('../../models');

// In-memory cache for dashboard data
const dashboardCache = new Map();
const CACHE_DURATION = 30000; // 30 seconds

// Rate limiting map to track requests per user
const rateLimitMap = new Map();
const RATE_LIMIT_WINDOW = 10000; // 10 seconds
const MAX_REQUESTS_PER_WINDOW = 5; // Max 5 requests per 10 seconds per user

/**
 * Rate limiting middleware function
 */
const checkRateLimit = (userId) => {
  const now = Date.now();
  const userRequests = rateLimitMap.get(userId) || [];
  
  // Remove old requests outside the window
  const recentRequests = userRequests.filter(timestamp => now - timestamp < RATE_LIMIT_WINDOW);
  
  if (recentRequests.length >= MAX_REQUESTS_PER_WINDOW) {
    return false; // Rate limit exceeded
  }
  
  // Add current request
  recentRequests.push(now);
  rateLimitMap.set(userId, recentRequests);
  
  return true; // Request allowed
};

/**
 * Get cached data or fetch fresh data
 */
const getCachedData = async (cacheKey, fetchFunction, forceRefresh = false) => {
  if (!forceRefresh) {
    const cached = dashboardCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      console.log('📋 Serving cached dashboard data for:', cacheKey);
      return cached.data;
    }
  }
  
  console.log('🌐 Fetching fresh dashboard data for:', cacheKey);
  const data = await fetchFunction();
  
  // Cache the result
  dashboardCache.set(cacheKey, {
    data,
    timestamp: Date.now()
  });
  
  return data;
};

/**
 * Get vendor dashboard statistics
 */
const getDashboardStats = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const requestTime = new Date().toISOString();
    
    console.log(`📊 Dashboard stats request from vendor ${vendorId} at ${requestTime}`);

    // Check rate limit
    if (!checkRateLimit(vendorId)) {
      console.log(`⚠️ Rate limit exceeded for vendor ${vendorId}`);
      return res.status(429).json({
        success: false,
        message: 'Too many requests. Please wait before making another request.',
        retryAfter: Math.ceil(RATE_LIMIT_WINDOW / 1000)
      });
    }

    // Get vendor details (should be attached by ensureVendorRecord middleware)
    const vendor = req.vendor || await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor record not found'
      });
    }

    // Check for force refresh parameter
    const forceRefresh = req.query.refresh === 'true';
    const cacheKey = `dashboard-stats-${vendor._id}`;

    // Use cached data or fetch fresh data
    const stats = await getCachedData(cacheKey, async () => {
      // Get current date ranges
      const now = new Date();
      const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      // Get product statistics
      const productStats = await Product.aggregate([
        { $match: { vendor: vendor._id } },
        {
          $group: {
            _id: null,
            totalProducts: { $sum: 1 },
            activeProducts: {
              $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
            },
            draftProducts: {
              $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] }
            },
            outOfStockProducts: {
              $sum: { $cond: [{ $lte: ['$inventory.quantity', 0] }, 1, 0] }
            },
            lowStockProducts: {
              $sum: { $cond: [{ $and: [{ $gt: ['$inventory.quantity', 0] }, { $lte: ['$inventory.quantity', '$inventory.lowStockThreshold'] }] }, 1, 0] }
            },
            totalRevenue: { $sum: '$sales.totalRevenue' },
            totalSold: { $sum: '$sales.totalSold' },
            averagePrice: { $avg: '$pricing.basePrice' },
            averageRating: { $avg: '$reviews.averageRating' }
          }
        }
      ]);

      // Get order statistics
      const orderStats = await Order.aggregate([
        { $match: { 'items.vendor': vendor._id } },
        {
          $addFields: {
            vendorItems: {
              $filter: {
                input: '$items',
                cond: { $eq: ['$$this.vendor', vendor._id] }
              }
            }
          }
        },
        {
          $group: {
            _id: null,
            totalOrders: { $sum: 1 },
            pendingOrders: {
              $sum: { $cond: [{ $eq: ['$orderStatus', 'pending'] }, 1, 0] }
            },
            processingOrders: {
              $sum: { $cond: [{ $eq: ['$orderStatus', 'processing'] }, 1, 0] }
            },
            shippedOrders: {
              $sum: { $cond: [{ $eq: ['$orderStatus', 'shipped'] }, 1, 0] }
            },
            deliveredOrders: {
              $sum: { $cond: [{ $eq: ['$orderStatus', 'delivered'] }, 1, 0] }
            },
            cancelledOrders: {
              $sum: { $cond: [{ $eq: ['$orderStatus', 'cancelled'] }, 1, 0] }
            },
            totalRevenue: {
              $sum: {
                $reduce: {
                  input: '$vendorItems',
                  initialValue: 0,
                  in: { $add: ['$$value', '$$this.totalPrice'] }
                }
              }
            },
            totalItems: {
              $sum: {
                $reduce: {
                  input: '$vendorItems',
                  initialValue: 0,
                  in: { $add: ['$$value', '$$this.quantity'] }
                }
              }
            }
          }
        },
        {
          $addFields: {
            averageOrderValue: {
              $cond: {
                if: { $eq: ['$totalOrders', 0] },
                then: 0,
                else: { $divide: ['$totalRevenue', '$totalOrders'] }
              }
            }
          }
        }
      ]);

      // Get today's statistics
      const todayStats = await Order.aggregate([
        {
          $match: {
            'items.vendor': vendor._id,
            createdAt: { $gte: startOfToday }
          }
        },
        {
          $addFields: {
            vendorItems: {
              $filter: {
                input: '$items',
                cond: { $eq: ['$$this.vendor', vendor._id] }
              }
            }
          }
        },
        {
          $group: {
            _id: null,
            todayOrders: { $sum: 1 },
            todayRevenue: {
              $sum: {
                $reduce: {
                  input: '$vendorItems',
                  initialValue: 0,
                  in: { $add: ['$$value', '$$this.totalPrice'] }
                }
              }
            }
          }
        }
      ]);

      // Get recent orders
      const recentOrders = await Order.find({ 'items.vendor': vendor._id })
        .populate('customer', 'firstName lastName email')
        .sort({ createdAt: -1 })
        .limit(5)
        .select('orderNumber orderStatus pricing.total createdAt customer items');

      // Get top selling products
      const topProducts = await Product.find({ vendor: vendor._id })
        .sort({ 'sales.totalSold': -1 })
        .limit(5)
        .select('name sales.totalSold sales.totalRevenue pricing.basePrice images');

      return {
        products: productStats[0] || {
          totalProducts: 0,
          activeProducts: 0,
          draftProducts: 0,
          outOfStockProducts: 0,
          lowStockProducts: 0,
          totalRevenue: 0,
          totalSold: 0,
          averagePrice: 0,
          averageRating: 0
        },
        orders: orderStats[0] || {
          totalOrders: 0,
          pendingOrders: 0,
          processingOrders: 0,
          shippedOrders: 0,
          deliveredOrders: 0,
          cancelledOrders: 0,
          totalRevenue: 0,
          averageOrderValue: 0
        },
        today: todayStats[0] || {
          todayOrders: 0,
          todayRevenue: 0
        },
        recentOrders,
        topProducts
      };
    }, forceRefresh);

    console.log(`✅ Dashboard stats response sent for vendor ${vendorId}`);

    res.json({
      success: true,
      data: stats,
      cached: !forceRefresh && dashboardCache.has(cacheKey),
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get vendor dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get vendor analytics data
 */
const getAnalytics = async (req, res) => {
  try {
    const vendorId = req.user.userId;
    const { period = '30d', type = 'revenue' } = req.query;
    const requestTime = new Date().toISOString();

    console.log(`📈 Analytics request from vendor ${vendorId} for ${period}/${type} at ${requestTime}`);

    // Check rate limit
    if (!checkRateLimit(vendorId)) {
      console.log(`⚠️ Rate limit exceeded for vendor ${vendorId} (analytics)`);
      return res.status(429).json({
        success: false,
        message: 'Too many requests. Please wait before making another request.',
        retryAfter: Math.ceil(RATE_LIMIT_WINDOW / 1000)
      });
    }

    // Get vendor details
    const vendor = req.vendor || await Vendor.findOne({ user: vendorId });
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Check for force refresh parameter
    const forceRefresh = req.query.refresh === 'true';
    const cacheKey = `analytics-${vendor._id}-${period}-${type}`;

    // Use cached data or fetch fresh data
    const analyticsResult = await getCachedData(cacheKey, async () => {
      // Calculate date range based on period
      const now = new Date();
      let startDate;

      switch (period) {
        case '7d':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        case '1y':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }

      let analyticsData;

      if (type === 'revenue') {
        // Revenue analytics
        analyticsData = await Order.aggregate([
          {
            $match: {
              'items.vendor': vendor._id,
              createdAt: { $gte: startDate },
              orderStatus: { $in: ['delivered', 'shipped', 'processing'] }
            }
          },
          {
            $addFields: {
              vendorItems: {
                $filter: {
                  input: '$items',
                  cond: { $eq: ['$$this.vendor', vendor._id] }
                }
              }
            }
          },
          {
            $group: {
              _id: {
                year: { $year: '$createdAt' },
                month: { $month: '$createdAt' },
                day: { $dayOfMonth: '$createdAt' }
              },
              revenue: {
                $sum: {
                  $reduce: {
                    input: '$vendorItems',
                    initialValue: 0,
                    in: { $add: ['$$value', '$$this.totalPrice'] }
                  }
                }
              },
              orders: { $sum: 1 }
            }
          },
          {
            $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
          }
        ]);
      } else if (type === 'orders') {
        // Orders analytics
        analyticsData = await Order.aggregate([
          {
            $match: {
              'items.vendor': vendor._id,
              createdAt: { $gte: startDate }
            }
          },
          {
            $group: {
              _id: {
                year: { $year: '$createdAt' },
                month: { $month: '$createdAt' },
                day: { $dayOfMonth: '$createdAt' },
                status: '$orderStatus'
              },
              count: { $sum: 1 }
            }
          },
          {
            $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
          }
        ]);
      }

      return {
        period,
        type,
        analytics: analyticsData || []
      };
    }, forceRefresh);

    console.log(`✅ Analytics response sent for vendor ${vendorId}`);

    res.json({
      success: true,
      data: analyticsResult,
      cached: !forceRefresh && dashboardCache.has(cacheKey),
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get vendor analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch analytics data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Cleanup function to remove old cache entries and rate limit data
const cleanup = () => {
  const now = Date.now();
  
  // Clean up cache
  for (const [key, value] of dashboardCache.entries()) {
    if (now - value.timestamp > CACHE_DURATION * 2) { // Keep cache for 2x duration
      dashboardCache.delete(key);
    }
  }
  
  // Clean up rate limit data
  for (const [userId, requests] of rateLimitMap.entries()) {
    const recentRequests = requests.filter(timestamp => now - timestamp < RATE_LIMIT_WINDOW * 2);
    if (recentRequests.length === 0) {
      rateLimitMap.delete(userId);
    } else {
      rateLimitMap.set(userId, recentRequests);
    }
  }
};

// Run cleanup every 5 minutes
setInterval(cleanup, 5 * 60 * 1000);

module.exports = {
  getDashboardStats,
  getAnalytics
};