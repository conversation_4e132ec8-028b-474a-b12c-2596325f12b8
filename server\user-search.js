#!/usr/bin/env node

require('dotenv').config();
const readline = require('readline');
const mongoose = require('mongoose');
const User = require('./schema/userSchema');

// Simple colors for better readability
const c = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    bold: '\x1b[1m',
    reset: '\x1b[0m'
};

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Display user information
function showUser(user) {
    if (!user) {
        console.log(`${c.red}❌ User not found${c.reset}\n`);
        return;
    }

    console.log(`\n${c.bold}${c.cyan}=== USER FOUND ===${c.reset}`);
    console.log(`${c.bold}ID:${c.reset} ${user._id}`);
    console.log(`${c.bold}Email:${c.reset} ${user.email}`);
    console.log(`${c.bold}Type:${c.reset} ${user.userType}`);
    console.log(`${c.bold}Role:${c.reset} ${user.role}`);
    
    if (user.userType === 'user') {
        console.log(`${c.bold}Name:${c.reset} ${user.firstName} ${user.lastName}`);
    } else if (user.userType === 'vendor') {
        console.log(`${c.bold}Business:${c.reset} ${user.businessName}`);
        console.log(`${c.bold}Business Type:${c.reset} ${user.businessType}`);
        console.log(`${c.bold}Contact:${c.reset} ${user.contactPerson}`);
    }
    
    console.log(`${c.bold}Status:${c.reset} ${user.isActive ? c.green + 'Active' : c.red + 'Inactive'}${c.reset}`);
    console.log(`${c.bold}Blocked:${c.reset} ${user.isBlocked ? c.red + 'Yes' : c.green + 'No'}${c.reset}`);
    if (user.blockReason) {
        console.log(`${c.bold}Block Reason:${c.reset} ${c.red}${user.blockReason}${c.reset}`);
    }
    console.log(`${c.bold}Email Verified:${c.reset} ${user.emailVerification.isVerified ? c.green + 'Yes' : c.yellow + 'No'}${c.reset}`);
    
    if (user.address) {
        console.log(`${c.bold}Address:${c.reset} ${user.address}`);
        if (user.city) console.log(`${c.bold}Location:${c.reset} ${user.city}, ${user.state} ${user.zipCode}`);
        if (user.country) console.log(`${c.bold}Country:${c.reset} ${user.country}`);
    }
    
    if (user.userType === 'vendor') {
        console.log(`${c.bold}Vendor Approved:${c.reset} ${user.vendorStatus.isApproved ? c.green + 'Yes' : c.yellow + 'Pending'}${c.reset}`);
        if (user.vendorStatus.rejectionReason) {
            console.log(`${c.bold}Rejection Reason:${c.reset} ${c.red}${user.vendorStatus.rejectionReason}${c.reset}`);
        }
    }
    
    console.log(`${c.bold}Created:${c.reset} ${user.createdAt.toLocaleDateString()}`);
    console.log(`${c.bold}Last Active:${c.reset} ${user.lastActiveAt.toLocaleDateString()}`);
    
    if (user.security.lastLogin) {
        console.log(`${c.bold}Last Login:${c.reset} ${user.security.lastLogin.toLocaleString()}`);
        if (user.security.lastLoginIP) {
            console.log(`${c.bold}Last IP:${c.reset} ${user.security.lastLoginIP}`);
        }
    }
    
    console.log(`${c.bold}Login Attempts:${c.reset} ${user.security.loginAttempts}`);
    console.log(`${c.bold}Account Locked:${c.reset} ${user.isLocked ? c.red + 'Yes' : c.green + 'No'}${c.reset}`);
    console.log(`${c.bold}2FA Enabled:${c.reset} ${user.twoFactorAuth.isEnabled ? c.green + 'Yes' : c.yellow + 'No'}${c.reset}`);
    
    if (user.statistics.totalOrders > 0) {
        console.log(`${c.bold}Total Orders:${c.reset} ${user.statistics.totalOrders}`);
        console.log(`${c.bold}Total Spent:${c.reset} $${user.statistics.totalSpent.toFixed(2)}`);
        console.log(`${c.bold}Average Order:${c.reset} $${user.statistics.averageOrderValue.toFixed(2)}`);
    }
    
    console.log(`${c.bold}Preferences:${c.reset} ${user.preferences.language}, ${user.preferences.currency}, ${user.preferences.timezone}`);
    
    if (user.socialAuth.isEnabled && user.socialAuth.providers.length > 0) {
        const providers = user.socialAuth.providers.map(p => p.provider).join(', ');
        console.log(`${c.bold}Social Auth:${c.reset} ${providers}`);
    }
    
    console.log(`${c.cyan}==================${c.reset}\n`);
}

// Connect to database
async function connectDB() {
    try {
        const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce';
        await mongoose.connect(uri);
        console.log(`${c.green}✅ Connected to database${c.reset}`);
        return true;
    } catch (error) {
        console.log(`${c.red}❌ Database connection failed: ${error.message}${c.reset}`);
        return false;
    }
}

// Prompt for email input
function askEmail() {
    return new Promise((resolve) => {
        rl.question(`${c.bold}Enter user email (or 'quit' to exit): ${c.reset}`, (email) => {
            resolve(email.trim());
        });
    });
}

// Main function
async function main() {
    console.log(`${c.bold}${c.blue}🔍 User Search Tool${c.reset}`);
    console.log(`${c.yellow}Type 'quit' or 'exit' to stop${c.reset}\n`);
    
    if (!(await connectDB())) {
        process.exit(1);
    }

    while (true) {
        const email = await askEmail();
        
        if (email.toLowerCase() === 'quit' || email.toLowerCase() === 'exit' || email.toLowerCase() === 'q') {
            break;
        }
        
        if (!email || !email.includes('@')) {
            console.log(`${c.red}❌ Please enter a valid email address${c.reset}\n`);
            continue;
        }

        try {
            console.log(`${c.yellow}🔍 Searching for: ${email}${c.reset}`);
            const user = await User.findByEmail(email);
            showUser(user);
        } catch (error) {
            console.log(`${c.red}❌ Error: ${error.message}${c.reset}\n`);
        }
    }

    rl.close();
    await mongoose.connection.close();
    console.log(`${c.green}✅ Database connection closed${c.reset}`);
    console.log(`${c.bold}👋 Goodbye!${c.reset}`);
}

// Handle Ctrl+C gracefully
process.on('SIGINT', async () => {
    console.log(`\n${c.yellow}👋 Exiting...${c.reset}`);
    rl.close();
    if (mongoose.connection.readyState === 1) {
        await mongoose.connection.close();
    }
    process.exit(0);
});

// Run the script
if (require.main === module) {
    main().catch(console.error);
}