const axios = require('axios');

const API_BASE_URL = 'https://multi-vendor-server-1tb9.onrender.com/api';

async function testVendorAnalytics() {
  console.log('🧪 Testing Vendor Analytics API...\n');

  // You need to provide actual vendor credentials here
  const credentials = {
    email: '<EMAIL>', // Replace with actual vendor email
    password: 'password123'      // Replace with actual vendor password
  };

  try {
    // 1. Login as vendor
    console.log('1. 🔐 Logging in as vendor...');
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, credentials);
    
    if (!loginResponse.data.success) {
      throw new Error('Login failed: ' + loginResponse.data.message);
    }

    const token = loginResponse.data.token;
    const user = loginResponse.data.user;
    
    console.log(`   ✅ Login successful - User: ${user.email} (Type: ${user.userType})`);

    if (user.userType !== 'vendor') {
      throw new Error('User is not a vendor');
    }

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // 2. Test dashboard stats endpoint
    console.log('\n2. 📊 Testing dashboard stats endpoint...');
    const statsResponse = await axios.get(`${API_BASE_URL}/vendor/dashboard/stats`, { headers });
    
    if (statsResponse.data.success) {
      console.log('   ✅ Dashboard stats API working');
      console.log('   📈 Data received:');
      const data = statsResponse.data.data;
      console.log(`      - Products: ${data.products?.totalProducts || 0}`);
      console.log(`      - Orders: ${data.orders?.totalOrders || 0}`);
      console.log(`      - Revenue: ₹${data.orders?.totalRevenue || 0}`);
    } else {
      console.log('   ❌ Dashboard stats failed:', statsResponse.data.message);
    }

    // 3. Test analytics endpoint
    console.log('\n3. 📈 Testing analytics endpoint...');
    const analyticsResponse = await axios.get(`${API_BASE_URL}/vendor/analytics`, {
      headers,
      params: { period: '30d', type: 'revenue' }
    });
    
    if (analyticsResponse.data.success) {
      console.log('   ✅ Analytics API working');
      const data = analyticsResponse.data.data;
      console.log(`   📊 Analytics data: ${data.analytics?.length || 0} data points`);
      console.log(`   📅 Period: ${data.period}, Type: ${data.type}`);
      
      if (data.analytics && data.analytics.length > 0) {
        console.log('   💰 Sample data point:', data.analytics[0]);
      }
    } else {
      console.log('   ❌ Analytics failed:', analyticsResponse.data.message);
    }

    console.log('\n✅ All tests completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('   📄 Response data:', error.response.data);
    }
  }
}

// Run the test
testVendorAnalytics();
