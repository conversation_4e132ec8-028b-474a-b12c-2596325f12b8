const mongoose = require('mongoose');
const Category = require('./src/models/Category');
require('dotenv').config();

const categories = [
  {
    name: 'Electronics',
    description: 'Electronic devices and gadgets',
    status: 'active',
    featured: true,
    sortOrder: 1,
    seo: {
      metaTitle: 'Electronics - Latest Gadgets and Devices',
      metaDescription: 'Shop the latest electronics, smartphones, laptops, and gadgets at great prices.',
      keywords: ['electronics', 'gadgets', 'smartphones', 'laptops']
    }
  },
  {
    name: 'Clothing & Fashion',
    description: 'Trendy clothing and fashion accessories',
    status: 'active',
    featured: true,
    sortOrder: 2,
    seo: {
      metaTitle: 'Clothing & Fashion - Latest Trends',
      metaDescription: 'Discover the latest fashion trends, clothing, and accessories for men and women.',
      keywords: ['clothing', 'fashion', 'apparel', 'accessories']
    }
  },
  {
    name: 'Home & Garden',
    description: 'Home improvement and garden supplies',
    status: 'active',
    featured: true,
    sortOrder: 3,
    seo: {
      metaTitle: 'Home & Garden - Improve Your Living Space',
      metaDescription: 'Find everything you need for home improvement and gardening projects.',
      keywords: ['home', 'garden', 'furniture', 'decor']
    }
  },
  {
    name: 'Sports & Outdoors',
    description: 'Sports equipment and outdoor gear',
    status: 'active',
    featured: false,
    sortOrder: 4,
    seo: {
      metaTitle: 'Sports & Outdoors - Gear for Active Lifestyle',
      metaDescription: 'Shop sports equipment, outdoor gear, and fitness accessories.',
      keywords: ['sports', 'outdoors', 'fitness', 'equipment']
    }
  },
  {
    name: 'Books & Media',
    description: 'Books, movies, music, and digital media',
    status: 'active',
    featured: false,
    sortOrder: 5,
    seo: {
      metaTitle: 'Books & Media - Entertainment Collection',
      metaDescription: 'Browse our collection of books, movies, music, and digital media.',
      keywords: ['books', 'media', 'movies', 'music']
    }
  },
  {
    name: 'Health & Beauty',
    description: 'Health products and beauty essentials',
    status: 'active',
    featured: true,
    sortOrder: 6,
    seo: {
      metaTitle: 'Health & Beauty - Wellness Products',
      metaDescription: 'Discover health and beauty products for your wellness journey.',
      keywords: ['health', 'beauty', 'skincare', 'wellness']
    }
  },
  {
    name: 'Toys & Games',
    description: 'Toys, games, and entertainment for all ages',
    status: 'active',
    featured: false,
    sortOrder: 7,
    seo: {
      metaTitle: 'Toys & Games - Fun for Everyone',
      metaDescription: 'Find toys, games, and entertainment products for kids and adults.',
      keywords: ['toys', 'games', 'kids', 'entertainment']
    }
  },
  {
    name: 'Automotive',
    description: 'Car parts, accessories, and automotive supplies',
    status: 'active',
    featured: false,
    sortOrder: 8,
    seo: {
      metaTitle: 'Automotive - Car Parts and Accessories',
      metaDescription: 'Shop automotive parts, accessories, and supplies for your vehicle.',
      keywords: ['automotive', 'car parts', 'accessories', 'vehicle']
    }
  },
  {
    name: 'Food & Beverages',
    description: 'Gourmet food, snacks, and beverages',
    status: 'active',
    featured: false,
    sortOrder: 9,
    seo: {
      metaTitle: 'Food & Beverages - Gourmet Selection',
      metaDescription: 'Explore our selection of gourmet food, snacks, and beverages.',
      keywords: ['food', 'beverages', 'gourmet', 'snacks']
    }
  },
  {
    name: 'Pet Supplies',
    description: 'Everything for your beloved pets',
    status: 'active',
    featured: false,
    sortOrder: 10,
    seo: {
      metaTitle: 'Pet Supplies - Care for Your Pets',
      metaDescription: 'Find quality pet supplies, food, toys, and accessories for your pets.',
      keywords: ['pets', 'pet supplies', 'pet food', 'pet accessories']
    }
  }
];

// Subcategories for Electronics
const electronicsSubcategories = [
  {
    name: 'Smartphones',
    description: 'Latest smartphones and mobile devices',
    status: 'active',
    level: 1,
    sortOrder: 1
  },
  {
    name: 'Laptops & Computers',
    description: 'Laptops, desktops, and computer accessories',
    status: 'active',
    level: 1,
    sortOrder: 2
  },
  {
    name: 'Audio & Headphones',
    description: 'Headphones, speakers, and audio equipment',
    status: 'active',
    level: 1,
    sortOrder: 3
  },
  {
    name: 'Gaming',
    description: 'Gaming consoles, games, and accessories',
    status: 'active',
    level: 1,
    sortOrder: 4
  },
  {
    name: 'Smart Home',
    description: 'Smart home devices and automation',
    status: 'active',
    level: 1,
    sortOrder: 5
  }
];

// Subcategories for Clothing & Fashion
const clothingSubcategories = [
  {
    name: "Men's Clothing",
    description: 'Clothing and accessories for men',
    status: 'active',
    level: 1,
    sortOrder: 1
  },
  {
    name: "Women's Clothing",
    description: 'Clothing and accessories for women',
    status: 'active',
    level: 1,
    sortOrder: 2
  },
  {
    name: 'Shoes',
    description: 'Footwear for all occasions',
    status: 'active',
    level: 1,
    sortOrder: 3
  },
  {
    name: 'Accessories',
    description: 'Fashion accessories and jewelry',
    status: 'active',
    level: 1,
    sortOrder: 4
  },
  {
    name: 'Bags & Luggage',
    description: 'Bags, backpacks, and luggage',
    status: 'active',
    level: 1,
    sortOrder: 5
  }
];

async function seedCategories() {
  try {
    console.log('🌱 Starting category seeding...');

    // Connect to database
    await mongoose.connect(process.env.DATABASE_URL || 'mongodb://localhost:27017/ecommerce');
    console.log('✅ Connected to database');

    // Clear existing categories
    await Category.deleteMany({});
    console.log('🗑️  Cleared existing categories');

    // Create main categories
    const createdCategories = [];
    for (const categoryData of categories) {
      const category = new Category(categoryData);
      await category.save();
      createdCategories.push(category);
      console.log(`✅ Created category: ${category.name}`);
    }

    // Create subcategories for Electronics
    const electronicsCategory = createdCategories.find(cat => cat.name === 'Electronics');
    if (electronicsCategory) {
      for (const subCatData of electronicsSubcategories) {
        const subcategory = new Category({
          ...subCatData,
          parent: electronicsCategory._id,
          path: electronicsCategory._id.toString()
        });
        await subcategory.save();
        console.log(`✅ Created subcategory: ${subcategory.name} under Electronics`);
      }
    }

    // Create subcategories for Clothing & Fashion
    const clothingCategory = createdCategories.find(cat => cat.name === 'Clothing & Fashion');
    if (clothingCategory) {
      for (const subCatData of clothingSubcategories) {
        const subcategory = new Category({
          ...subCatData,
          parent: clothingCategory._id,
          path: clothingCategory._id.toString()
        });
        await subcategory.save();
        console.log(`✅ Created subcategory: ${subcategory.name} under Clothing & Fashion`);
      }
    }

    // Get final count
    const totalCategories = await Category.countDocuments();
    console.log(`\n🎉 Successfully seeded ${totalCategories} categories!`);

    // Display category tree
    console.log('\n📋 Category Structure:');
    const mainCategories = await Category.find({ parent: null }).sort({ sortOrder: 1 });
    for (const mainCat of mainCategories) {
      console.log(`├── ${mainCat.name} (${mainCat.featured ? 'Featured' : 'Regular'})`);
      
      const subcategories = await Category.find({ parent: mainCat._id }).sort({ sortOrder: 1 });
      for (const subCat of subcategories) {
        console.log(`│   └── ${subCat.name}`);
      }
    }

    console.log('\n✨ Categories are ready for product creation!');

  } catch (error) {
    console.error('❌ Error seeding categories:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run the seeder
if (require.main === module) {
  seedCategories();
}

module.exports = seedCategories;