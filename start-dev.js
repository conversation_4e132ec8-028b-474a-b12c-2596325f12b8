/**
 * Development startup script
 * Run with: node start-dev.js
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Multi-Vendor eCommerce Development Environment...\n');

// Function to run a command in a specific directory
function runCommand(command, args, cwd, label, color = '\x1b[36m') {
    const process = spawn(command, args, {
        cwd,
        stdio: 'pipe',
        shell: true
    });

    process.stdout.on('data', (data) => {
        console.log(`${color}[${label}]\x1b[0m ${data.toString().trim()}`);
    });

    process.stderr.on('data', (data) => {
        console.log(`\x1b[31m[${label} ERROR]\x1b[0m ${data.toString().trim()}`);
    });

    process.on('close', (code) => {
        console.log(`\x1b[33m[${label}]\x1b[0m Process exited with code ${code}`);
    });

    return process;
}

// Start backend server
console.log('📡 Starting Backend Server (localhost:5000)...');
const serverProcess = runCommand(
    'npm', 
    ['run', 'dev'], 
    path.join(__dirname, 'server'),
    'SERVER',
    '\x1b[32m' // Green
);

// Wait a bit for server to start, then start frontend
setTimeout(() => {
    console.log('\n🎨 Starting Frontend (localhost:5173)...');
    const clientProcess = runCommand(
        'npm', 
        ['run', 'dev'], 
        path.join(__dirname, 'client'),
        'CLIENT',
        '\x1b[34m' // Blue
    );

    // Handle process termination
    process.on('SIGINT', () => {
        console.log('\n\n🛑 Shutting down development environment...');
        serverProcess.kill();
        clientProcess.kill();
        process.exit(0);
    });

}, 3000);

console.log('\n💡 Development Environment Info:');
console.log('   🌐 Frontend: http://localhost:5173');
console.log('   🔧 Backend:  http://localhost:5000');
console.log('   📡 API:      http://localhost:5000/api');
console.log('   📊 Health:   http://localhost:5000/api/health');
console.log('\n⚡ Press Ctrl+C to stop both servers');