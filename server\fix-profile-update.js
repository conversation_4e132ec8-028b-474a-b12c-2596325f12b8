/**
 * Quick fix for profile update issues
 * This script patches the authController to handle the missing fields and preferences parsing
 */

const fs = require('fs');
const path = require('path');

const authControllerPath = path.join(__dirname, 'src', 'controllers', 'authController.js');

console.log('🔧 Applying profile update fixes...');

// Read the current file
let content = fs.readFileSync(authControllerPath, 'utf8');

// Fix 1: Add missing fields to allowedFields arrays
const oldVendorFields = `? ['businessName', 'businessType', 'contactPerson', 'dateOfBirth', 'gender', 'address', 'city', 'state', 'zipCode', 'country']`;
const newVendorFields = `? ['businessName', 'businessType', 'contactPerson', 'dateOfBirth', 'gender', 'address', 'city', 'state', 'zipCode', 'country', 'phone', 'bio', 'displayName', 'website']`;

const oldUserFields = `: ['firstName', 'lastName', 'dateOfBirth', 'gender', 'address', 'city', 'state', 'zipCode', 'country'];`;
const newUserFields = `: ['firstName', 'lastName', 'dateOfBirth', 'gender', 'address', 'city', 'state', 'zipCode', 'country', 'phone', 'bio', 'displayName', 'website'];`;

content = content.replace(oldVendorFields, newVendorFields);
content = content.replace(oldUserFields, newUserFields);

// Fix 2: Add preferences string parsing and email removal
const insertPoint = `console.log('User found - ID:', user._id, 'Type:', user.userType, 'Email:', user.email);`;
const newCode = `console.log('User found - ID:', user._id, 'Type:', user.userType, 'Email:', user.email);

            // Handle preferences if it's a JSON string (from form data)
            if (updateData.preferences && typeof updateData.preferences === 'string') {
                try {
                    updateData.preferences = JSON.parse(updateData.preferences);
                    console.log('Parsed preferences from string:', updateData.preferences);
                } catch (error) {
                    console.log('Failed to parse preferences JSON:', error.message);
                    delete updateData.preferences; // Remove invalid preferences
                }
            }

            // Remove email field for security (email changes should be handled separately)
            if (updateData.email) {
                console.log('Removing email field from update data for security');
                delete updateData.email;
            }`;

content = content.replace(insertPoint, newCode);

// Fix 3: Update error message to exclude email from received fields
const oldErrorMessage = `receivedFields: Object.keys(updateData),`;
const newErrorMessage = `receivedFields: Object.keys(updateData).filter(field => field !== 'email'), // Exclude email from received fields`;

content = content.replace(oldErrorMessage, newErrorMessage);

const oldHint = `hint: 'Common issue: sending empty object {} or invalid field names',`;
const newHint = `hint: 'Email updates are not allowed for security reasons. Other fields should be valid.',`;

content = content.replace(oldHint, newHint);

// Write the fixed file
fs.writeFileSync(authControllerPath, content);

console.log('✅ Profile update fixes applied successfully!');
console.log('');
console.log('🔧 Changes made:');
console.log('   ✅ Added missing fields: phone, bio, displayName, website');
console.log('   ✅ Added preferences JSON string parsing');
console.log('   ✅ Added email field removal for security');
console.log('   ✅ Updated error messages');
console.log('');
console.log('🚀 The profile update endpoint should now work correctly!');