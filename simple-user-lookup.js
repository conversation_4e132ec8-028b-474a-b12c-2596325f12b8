#!/usr/bin/env node

require('dotenv').config();
const readline = require('readline');
const mongoose = require('mongoose');
const User = require('./server/schema/userSchema');

// Simple colors
const c = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    bold: '\x1b[1m',
    reset: '\x1b[0m'
};

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Simple user display function
function showUser(user) {
    if (!user) {
        console.log(`${c.red}❌ User not found${c.reset}\n`);
        return;
    }

    console.log(`\n${c.bold}${c.cyan}=== USER FOUND ===${c.reset}`);
    console.log(`${c.bold}ID:${c.reset} ${user._id}`);
    console.log(`${c.bold}Email:${c.reset} ${user.email}`);
    console.log(`${c.bold}Type:${c.reset} ${user.userType}`);
    
    if (user.userType === 'user') {
        console.log(`${c.bold}Name:${c.reset} ${user.firstName} ${user.lastName}`);
    } else if (user.userType === 'vendor') {
        console.log(`${c.bold}Business:${c.reset} ${user.businessName}`);
        console.log(`${c.bold}Contact:${c.reset} ${user.contactPerson}`);
    }
    
    console.log(`${c.bold}Status:${c.reset} ${user.isActive ? c.green + 'Active' : c.red + 'Inactive'}${c.reset}`);
    console.log(`${c.bold}Email Verified:${c.reset} ${user.emailVerification.isVerified ? c.green + 'Yes' : c.yellow + 'No'}${c.reset}`);
    console.log(`${c.bold}Created:${c.reset} ${new Date(user.createdAt).toLocaleDateString()}`);
    
    if (user.address) {
        console.log(`${c.bold}Address:${c.reset} ${user.address}`);
        if (user.city) console.log(`${c.bold}City:${c.reset} ${user.city}`);
    }
    
    if (user.userType === 'vendor') {
        console.log(`${c.bold}Vendor Approved:${c.reset} ${user.vendorStatus.isApproved ? c.green + 'Yes' : c.yellow + 'Pending'}${c.reset}`);
    }
    
    if (user.statistics.totalOrders > 0) {
        console.log(`${c.bold}Total Orders:${c.reset} ${user.statistics.totalOrders}`);
        console.log(`${c.bold}Total Spent:${c.reset} $${user.statistics.totalSpent.toFixed(2)}`);
    }
    
    if (user.security.lastLogin) {
        console.log(`${c.bold}Last Login:${c.reset} ${new Date(user.security.lastLogin).toLocaleString()}`);
    }
    
    console.log(`${c.cyan}==================${c.reset}\n`);
}

async function connectDB() {
    try {
        const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce';
        await mongoose.connect(uri);
        console.log(`${c.green}✅ Connected to database${c.reset}`);
        return true;
    } catch (error) {
        console.log(`${c.red}❌ Database connection failed: ${error.message}${c.reset}`);
        return false;
    }
}

function askEmail() {
    return new Promise((resolve) => {
        rl.question(`${c.bold}Enter user email (or 'quit' to exit): ${c.reset}`, (email) => {
            resolve(email.trim());
        });
    });
}

async function main() {
    console.log(`${c.bold}${c.blue}🔍 Quick User Lookup Tool${c.reset}\n`);
    
    if (!(await connectDB())) {
        process.exit(1);
    }

    while (true) {
        const email = await askEmail();
        
        if (email.toLowerCase() === 'quit' || email.toLowerCase() === 'exit') {
            break;
        }
        
        if (!email || !email.includes('@')) {
            console.log(`${c.red}❌ Please enter a valid email${c.reset}\n`);
            continue;
        }

        try {
            console.log(`${c.yellow}🔍 Searching...${c.reset}`);
            const user = await User.findByEmail(email);
            showUser(user);
        } catch (error) {
            console.log(`${c.red}❌ Error: ${error.message}${c.reset}\n`);
        }
    }

    rl.close();
    await mongoose.connection.close();
    console.log(`${c.green}✅ Goodbye!${c.reset}`);
}

// Handle Ctrl+C
process.on('SIGINT', async () => {
    console.log(`\n${c.yellow}👋 Exiting...${c.reset}`);
    rl.close();
    if (mongoose.connection.readyState === 1) {
        await mongoose.connection.close();
    }
    process.exit(0);
});

if (require.main === module) {
    main().catch(console.error);
}