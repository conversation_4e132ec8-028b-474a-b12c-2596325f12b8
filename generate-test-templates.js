#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Test template generators
const generateReactComponentTest = (componentName, componentPath) => {
    return `import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import ${componentName} from '${componentPath}';

// Mock store setup
const mockStore = configureStore({
  reducer: {
    // Add your reducers here
  },
});

// Test wrapper component
const TestWrapper = ({ children }) => (
  <Provider store={mockStore}>
    <BrowserRouter>
      {children}
    </BrowserRouter>
  </Provider>
);

describe('${componentName}', () => {
  beforeEach(() => {
    // Reset any mocks before each test
    jest.clearAllMocks();
  });

  test('renders without crashing', () => {
    render(
      <TestWrapper>
        <${componentName} />
      </TestWrapper>
    );
  });

  test('displays expected content', () => {
    render(
      <TestWrapper>
        <${componentName} />
      </TestWrapper>
    );
    
    // Add assertions for expected content
    // expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });

  test('handles user interactions', async () => {
    render(
      <TestWrapper>
        <${componentName} />
      </TestWrapper>
    );
    
    // Test user interactions
    // const button = screen.getByRole('button', { name: /click me/i });
    // fireEvent.click(button);
    // await waitFor(() => {
    //   expect(screen.getByText('Expected Result')).toBeInTheDocument();
    // });
  });

  test('handles props correctly', () => {
    const mockProps = {
      // Add mock props here
    };

    render(
      <TestWrapper>
        <${componentName} {...mockProps} />
      </TestWrapper>
    );
    
    // Test prop handling
    // expect(screen.getByText(mockProps.expectedText)).toBeInTheDocument();
  });

  test('handles error states', () => {
    // Test error handling
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    render(
      <TestWrapper>
        <${componentName} />
      </TestWrapper>
    );
    
    // Add error state tests
    
    consoleSpy.mockRestore();
  });

  test('handles loading states', () => {
    render(
      <TestWrapper>
        <${componentName} loading={true} />
      </TestWrapper>
    );
    
    // Test loading state
    // expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });
});
`;
};

const generateNodeJSTest = (moduleName, modulePath) => {
    return `const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../../app'); // Adjust path as needed
const ${moduleName} = require('${modulePath}');

// Mock dependencies
jest.mock('../../models'); // Adjust path as needed

describe('${moduleName}', () => {
  beforeAll(async () => {
    // Setup test database connection
    // await mongoose.connect(process.env.TEST_MONGODB_URI);
  });

  afterAll(async () => {
    // Cleanup test database
    // await mongoose.connection.close();
  });

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
  });

  describe('Unit Tests', () => {
    test('should handle valid input', async () => {
      // Test valid input scenarios
      const mockInput = {
        // Add mock input data
      };

      // Add your test logic here
      // const result = await ${moduleName}.someMethod(mockInput);
      // expect(result).toBeDefined();
    });

    test('should handle invalid input', async () => {
      // Test invalid input scenarios
      const invalidInput = {
        // Add invalid input data
      };

      // Add your test logic here
      // await expect(${moduleName}.someMethod(invalidInput)).rejects.toThrow();
    });

    test('should handle edge cases', async () => {
      // Test edge cases
      // Add edge case tests
    });
  });

  describe('Integration Tests', () => {
    test('should handle API requests', async () => {
      const response = await request(app)
        .get('/api/endpoint') // Adjust endpoint
        .expect(200);

      expect(response.body).toBeDefined();
      // Add more assertions
    });

    test('should handle POST requests', async () => {
      const testData = {
        // Add test data
      };

      const response = await request(app)
        .post('/api/endpoint') // Adjust endpoint
        .send(testData)
        .expect(201);

      expect(response.body.success).toBe(true);
      // Add more assertions
    });

    test('should handle authentication', async () => {
      const response = await request(app)
        .get('/api/protected-endpoint') // Adjust endpoint
        .expect(401);

      expect(response.body.message).toContain('authentication');
    });

    test('should handle validation errors', async () => {
      const invalidData = {
        // Add invalid data
      };

      const response = await request(app)
        .post('/api/endpoint') // Adjust endpoint
        .send(invalidData)
        .expect(400);

      expect(response.body.errors).toBeDefined();
    });
  });

  describe('Database Operations', () => {
    test('should create records', async () => {
      // Test database create operations
    });

    test('should read records', async () => {
      // Test database read operations
    });

    test('should update records', async () => {
      // Test database update operations
    });

    test('should delete records', async () => {
      // Test database delete operations
    });
  });

  describe('Error Handling', () => {
    test('should handle database errors', async () => {
      // Test database error scenarios
    });

    test('should handle network errors', async () => {
      // Test network error scenarios
    });

    test('should handle validation errors', async () => {
      // Test validation error scenarios
    });
  });
});
`;
};

const generateJestConfig = () => {
    return `module.exports = {
  testEnvironment: 'node',
  roots: ['<rootDir>/src', '<rootDir>/test'],
  testMatch: [
    '**/__tests__/**/*.js',
    '**/?(*.)+(spec|test).js'
  ],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/**/*.test.js',
    '!src/config/**',
    '!src/migrations/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  setupFilesAfterEnv: ['<rootDir>/test/setup.js'],
  testTimeout: 10000,
  verbose: true,
  forceExit: true,
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true
};
`;
};

const generateVitestConfig = () => {
    return `import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.js'],
    globals: true,
    css: true,
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.test.{js,jsx,ts,tsx}',
        '**/*.spec.{js,jsx,ts,tsx}'
      ]
    }
  }
});
`;
};

const generateTestSetup = (isReact = false) => {
    if (isReact) {
        return `import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';
import { afterEach, vi } from 'vitest';

// Cleanup after each test
afterEach(() => {
  cleanup();
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));
`;
    } else {
        return `const mongoose = require('mongoose');

// Setup test database
beforeAll(async () => {
  if (process.env.NODE_ENV !== 'test') {
    throw new Error('Tests should only run in test environment');
  }
  
  // Connect to test database
  if (process.env.TEST_MONGODB_URI) {
    await mongoose.connect(process.env.TEST_MONGODB_URI);
  }
});

// Cleanup after all tests
afterAll(async () => {
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.close();
  }
});

// Clear database before each test
beforeEach(async () => {
  if (mongoose.connection.readyState !== 0) {
    const collections = mongoose.connection.collections;
    for (const key in collections) {
      const collection = collections[key];
      await collection.deleteMany({});
    }
  }
});
`;
    }
};

function createTestFile(componentPath, componentName, isReact = false) {
    const testDir = path.dirname(componentPath);
    const testFileName = `${componentName}.test.${isReact ? 'jsx' : 'js'}`;
    const testFilePath = path.join(testDir, '__tests__', testFileName);
    
    // Create __tests__ directory if it doesn't exist
    const testsDir = path.join(testDir, '__tests__');
    if (!fs.existsSync(testsDir)) {
        fs.mkdirSync(testsDir, { recursive: true });
    }
    
    // Generate test content
    const relativePath = path.relative(testsDir, componentPath);
    const testContent = isReact 
        ? generateReactComponentTest(componentName, relativePath)
        : generateNodeJSTest(componentName, relativePath);
    
    // Write test file
    fs.writeFileSync(testFilePath, testContent);
    
    return testFilePath;
}

function generateTestsForLargeComponents() {
    console.log('🧪 Generating Test Templates for Large Components...\n');
    
    // Read the analysis results (we'll need to run the analysis first)
    const { execSync } = require('child_process');
    
    try {
        // Run the analysis to get component data
        console.log('📊 Running component analysis...');
        execSync('node large-components-test-analysis.js > analysis-output.txt', { stdio: 'inherit' });
    } catch (error) {
        console.log('⚠️ Could not run analysis, proceeding with manual component detection...');
    }
    
    // Manually find large components
    const largeComponents = [];
    
    // Frontend components
    const clientSrcPath = path.join(process.cwd(), 'client', 'src');
    if (fs.existsSync(clientSrcPath)) {
        findLargeComponents(clientSrcPath, largeComponents, true);
    }
    
    // Backend components
    const serverSrcPath = path.join(process.cwd(), 'server', 'src');
    if (fs.existsSync(serverSrcPath)) {
        findLargeComponents(serverSrcPath, largeComponents, false);
    }
    
    console.log(`\n📋 Found ${largeComponents.length} large components (>200 lines)`);
    
    const createdTests = [];
    
    largeComponents.forEach((component, index) => {
        console.log(`\n${index + 1}. Processing: ${component.name} (${component.lines} lines)`);
        
        try {
            const testFilePath = createTestFile(
                component.fullPath, 
                component.name.replace(path.extname(component.name), ''),
                component.isReact
            );
            
            createdTests.push({
                component: component.name,
                testFile: testFilePath,
                type: component.isReact ? 'React' : 'Node.js'
            });
            
            console.log(`   ✅ Created test: ${path.relative(process.cwd(), testFilePath)}`);
        } catch (error) {
            console.log(`   ❌ Failed to create test: ${error.message}`);
        }
    });
    
    // Generate config files
    console.log('\n⚙️ Generating test configuration files...');
    
    // Jest config for backend
    const serverPath = path.join(process.cwd(), 'server');
    if (fs.existsSync(serverPath)) {
        const jestConfigPath = path.join(serverPath, 'jest.config.js');
        if (!fs.existsSync(jestConfigPath)) {
            fs.writeFileSync(jestConfigPath, generateJestConfig());
            console.log(`   ✅ Created: ${path.relative(process.cwd(), jestConfigPath)}`);
        }
        
        const testSetupPath = path.join(serverPath, 'test', 'setup.js');
        if (!fs.existsSync(path.dirname(testSetupPath))) {
            fs.mkdirSync(path.dirname(testSetupPath), { recursive: true });
        }
        if (!fs.existsSync(testSetupPath)) {
            fs.writeFileSync(testSetupPath, generateTestSetup(false));
            console.log(`   ✅ Created: ${path.relative(process.cwd(), testSetupPath)}`);
        }
    }
    
    // Vitest config for frontend
    const clientPath = path.join(process.cwd(), 'client');
    if (fs.existsSync(clientPath)) {
        const vitestConfigPath = path.join(clientPath, 'vitest.config.js');
        if (!fs.existsSync(vitestConfigPath)) {
            fs.writeFileSync(vitestConfigPath, generateVitestConfig());
            console.log(`   ✅ Created: ${path.relative(process.cwd(), vitestConfigPath)}`);
        }
        
        const testSetupPath = path.join(clientPath, 'src', 'test', 'setup.js');
        if (!fs.existsSync(path.dirname(testSetupPath))) {
            fs.mkdirSync(path.dirname(testSetupPath), { recursive: true });
        }
        if (!fs.existsSync(testSetupPath)) {
            fs.writeFileSync(testSetupPath, generateTestSetup(true));
            console.log(`   ✅ Created: ${path.relative(process.cwd(), testSetupPath)}`);
        }
    }
    
    // Generate summary report
    console.log('\n📊 TEST GENERATION SUMMARY:');
    console.log('='.repeat(80));
    console.log(`Total large components found: ${largeComponents.length}`);
    console.log(`Test files created: ${createdTests.length}`);
    
    const reactTests = createdTests.filter(t => t.type === 'React').length;
    const nodeTests = createdTests.filter(t => t.type === 'Node.js').length;
    
    console.log(`React component tests: ${reactTests}`);
    console.log(`Node.js module tests: ${nodeTests}`);
    
    console.log('\n📝 CREATED TEST FILES:');
    console.log('-'.repeat(80));
    createdTests.forEach((test, index) => {
        console.log(`${index + 1}. ${test.component} (${test.type})`);
        console.log(`   ${path.relative(process.cwd(), test.testFile)}`);
    });
    
    console.log('\n🚀 NEXT STEPS:');
    console.log('-'.repeat(80));
    console.log('1. Install testing dependencies:');
    console.log('   Backend: npm install --save-dev jest supertest');
    console.log('   Frontend: npm install --save-dev vitest @testing-library/react @testing-library/jest-dom');
    console.log('2. Update package.json scripts:');
    console.log('   "test": "jest" (backend) or "test": "vitest" (frontend)');
    console.log('3. Fill in the test templates with actual test cases');
    console.log('4. Run tests: npm test');
    console.log('5. Set up CI/CD to run tests automatically');
    
    console.log('\n✅ Test template generation completed!');
}

function findLargeComponents(dirPath, results, isReact = false) {
    try {
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                if (!['node_modules', '.git', 'dist', 'build', '__tests__', 'tests'].includes(item)) {
                    findLargeComponents(fullPath, results, isReact);
                }
            } else if (stat.isFile()) {
                const ext = path.extname(item);
                const validExtensions = isReact 
                    ? ['.js', '.jsx', '.ts', '.tsx']
                    : ['.js', '.ts'];
                
                if (validExtensions.includes(ext) && !item.includes('.test.') && !item.includes('.spec.')) {
                    try {
                        const content = fs.readFileSync(fullPath, 'utf8');
                        const lines = content.split('\n').length;
                        
                        if (lines > 200) {
                            results.push({
                                name: item,
                                fullPath: fullPath,
                                lines: lines,
                                isReact: isReact
                            });
                        }
                    } catch (error) {
                        // Skip files that can't be read
                    }
                }
            }
        }
    } catch (error) {
        console.error(`Error analyzing directory ${dirPath}:`, error.message);
    }
}

// Run the generator
generateTestsForLargeComponents();