import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Modal,
  Typography,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Input,
  Descriptions,
  Steps,
  notification,
  Badge,
  Tooltip
} from 'antd';
import { ordersApi } from '../../../services/adminApi';
import { useAuth } from '../../../hooks/useAuth';
import {
  ShoppingCartOutlined,
  EyeOutlined,
  SearchOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  TruckOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { vendorOrderApi } from '../../../services/vendorOrderApi';
import ErrorBoundary from '../../common/ErrorBoundary';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Step } = Steps;

const OrdersManagement = ({ 
  userType = 'vendor',
  title = 'Orders Management'
}) => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateRange, setDateRange] = useState(null);
  
  // Pagination state
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 15,
    total: 0
  });

  // Vendor settings for currency
  const [vendorSettings] = useState({
    currency: 'INR',
    commissionRate: 15
  });

  const { user } = useAuth();

  // Initial data fetch
  useEffect(() => {
    fetchOrders();
  }, []);

  // Fetch orders when filters change
  useEffect(() => {
    fetchOrders();
  }, [statusFilter, searchText, dateRange, pagination.current, pagination.pageSize]);


  const getCurrencySymbol = (currency) => {
    const symbols = {
      'USD': '$',
      'EUR': '€',
      'GBP': '£',
      'INR': '₹',
      'CAD': 'C$',
      'AUD': 'A$',
      'JPY': '¥',
      'CNY': '¥'
    };
    return symbols[currency] || currency;
  };

  // Fetch orders with dynamic API selection
  const fetchOrders = async (params = {}) => {
    setLoading(true);
    
    try {
      const queryParams = {
        page: pagination.current,
        limit: pagination.pageSize,
        status: statusFilter === 'all' ? '' : statusFilter,
        search: searchText,
        sortBy: 'createdAt',
        sortOrder: 'desc',
        ...params
      };

      // Add date range if selected
      if (dateRange && dateRange.length === 2) {
        queryParams.dateFrom = dateRange[0].format('YYYY-MM-DD');
        queryParams.dateTo = dateRange[1].format('YYYY-MM-DD');
      }

      let response;
      if (userType === 'admin') {
        response = await ordersApi.getOrders(queryParams);
        response = response.data;
      } else {
        response = await vendorOrderApi.getOrders(queryParams);
      }
      
      if (response.success) {
        const currencySymbol = getCurrencySymbol(vendorSettings.currency);
        const commissionRate = vendorSettings.commissionRate / 100;

        // Map orders based on user type
        const mappedOrders = response.data.orders.map(order => {
          const commonOrderData = {
            id: order._id,
            _id: order._id,
            orderNumber: order.orderNumber,
            customerName: userType === 'admin' 
              ? `${order.customer?.firstName || ''} ${order.customer?.lastName || ''}`.trim()
              : `${order.customer?.firstName || order.customerInfo?.firstName || ''} ${order.customer?.lastName || order.customerInfo?.lastName || ''}`.trim(),
            customerEmail: order.customer?.email || order.customerInfo?.email || '',
            customerPhone: order.customer?.phone || order.customerInfo?.phone || '',
            status: order.status || 'pending',
            paymentStatus: order.payment?.status || 'pending',
            paymentMethod: order.payment?.method || 'cash_on_delivery',
            orderDate: new Date(order.createdAt).toLocaleDateString(),
            shippedDate: order.shipping?.shippedAt ? new Date(order.shipping.shippedAt).toLocaleDateString() : null,
            deliveryDate: order.shipping?.deliveredAt ? new Date(order.shipping.deliveredAt).toLocaleDateString() : null,
            trackingNumber: order.shipping?.trackingNumber || null,
            shippingAddress: formatAddress(order.shipping?.address || order.billing?.address),
            currency: vendorSettings.currency,
            currencySymbol: currencySymbol
          };

          if (userType === 'admin') {
            return {
              ...commonOrderData,
              products: order.items?.map(item => ({
                name: item.name,
                quantity: item.quantity,
                price: item.unitPrice,
                totalPrice: item.totalPrice,
                sku: item.sku
              })) || [],
              totalAmount: order.pricing?.total || 0
            };
          } else {
            const vendorTotal = order.vendorTotal || 0;
            const commission = vendorTotal * commissionRate;
            const netAmount = vendorTotal - commission;
            
            return {
              ...commonOrderData,
              products: order.vendorItems?.map(item => ({
                name: item.name,
                quantity: item.quantity,
                price: item.unitPrice || (item.totalPrice / item.quantity),
                totalPrice: item.totalPrice,
                sku: item.sku
              })) || [],
              totalAmount: vendorTotal,
              netAmount: netAmount,
              commission: commission,
              commissionRate: vendorSettings.commissionRate
            };
          }
        });
        
        setOrders(mappedOrders);
        setPagination(prev => ({
          ...prev,
          total: response.data.pagination?.totalOrders || mappedOrders.length
        }));
      }
    } catch (error) {
      console.error('Fetch orders error:', error);
      notification.error({
        message: 'Error',
        description: error.message || 'Failed to fetch orders'
      });
    } finally {
      setLoading(false);
    }
  };

  // Format address helper
  const formatAddress = (address) => {
    if (!address) return 'N/A';
    const parts = [
      address.street,
      address.city,
      address.state,
      address.zipCode,
      address.country
    ].filter(Boolean);
    return parts.join(', ');
  };

  const handleViewOrder = (order) => {
    setSelectedOrder(order);
    setDetailModalVisible(true);
  };

  // Handle status update
  const handleUpdateOrderStatus = async (orderId, newStatus) => {
    try {
      let response;
      const updateData = {
        status: newStatus,
        note: `Status updated to ${newStatus}`
      };

      if (userType === 'admin') {
        response = await ordersApi.updateOrderStatus(orderId, newStatus, `Status updated to ${newStatus}`);
        response = response.data;
      } else {
        response = await vendorOrderApi.updateOrderStatus(orderId, updateData);
      }
      
      if (response.success) {
        notification.success({
          message: 'Success',
          description: `Order status updated to ${newStatus}`,
          placement: 'topRight'
        });
        
        fetchOrders();
      }
    } catch (error) {
      notification.error({
        message: 'Error',
        description: error.message || 'Failed to update order status'
      });
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'orange';
      case 'processing':
        return 'blue';
      case 'shipped':
        return 'cyan';
      case 'delivered':
        return 'green';
      case 'cancelled':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <ClockCircleOutlined />;
      case 'processing':
        return <ClockCircleOutlined />;
      case 'shipped':
        return <TruckOutlined />;
      case 'delivered':
        return <CheckCircleOutlined />;
      case 'cancelled':
        return <CloseCircleOutlined />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  // Order progress steps
  const getOrderSteps = (order) => {
    const allSteps = [
      { title: 'Order Placed', status: 'finish', key: 'pending' },
      { title: 'Confirmed', status: order.status === 'pending' ? 'wait' : 'finish', key: 'confirmed' },
      { title: 'Processing', status: ['pending', 'confirmed'].includes(order.status) ? 'wait' : 'finish', key: 'processing' },
      { title: 'Shipped', status: ['pending', 'confirmed', 'processing'].includes(order.status) ? 'wait' : 'finish', key: 'shipped' },
      { title: 'Delivered', status: order.status === 'delivered' ? 'finish' : 'wait', key: 'delivered' }
    ];

    if (['cancelled', 'returned', 'refunded'].includes(order.status)) {
      return [
        { title: 'Order Placed', status: 'finish', key: 'pending' },
        { title: order.status.charAt(0).toUpperCase() + order.status.slice(1), status: 'error', key: order.status }
      ];
    }

    return allSteps;
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch = 
      (order.orderNumber && order.orderNumber.toLowerCase().includes(searchText.toLowerCase())) ||
      (order.customerName && order.customerName.toLowerCase().includes(searchText.toLowerCase())) ||
      (order.customerEmail && order.customerEmail.toLowerCase().includes(searchText.toLowerCase()));
    
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const [isLargeScreen, setIsLargeScreen] = useState(window.innerWidth > 1400);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsLargeScreen(window.innerWidth > 1400);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  const columns = [
    {
      title: 'Order #',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: isLargeScreen ? 140 : 80,
      fixed: 'left',
      render: (orderNumber) => (
        <strong style={{ 
          fontSize: isLargeScreen ? '13px' : '11px',
          color: '#1890ff'
        }}>
          {orderNumber}
        </strong>
      ),
    },
    {
      title: 'Customer',
      dataIndex: 'customerName',
      key: 'customerName',
      width: isLargeScreen ? 200 : 150,
      render: (name, record) => (
        <div>
          <div style={{ 
            fontWeight: 500, 
            fontSize: isLargeScreen ? '13px' : '11px',
            marginBottom: '2px'
          }}>
            {name}
          </div>
          <div style={{ 
            color: '#666', 
            fontSize: isLargeScreen ? '11px' : '10px'
          }}>
            {record.customerEmail}
          </div>
        </div>
      ),
      responsive: ['sm', 'md', 'lg', 'xl'],
    },
    {
      title: 'Products',
      dataIndex: 'products',
      key: 'products',
      width: isLargeScreen ? 220 : 180,
      render: (products) => (
        <div>
          {products.slice(0, isLargeScreen ? 2 : 1).map((product, index) => (
            <div key={index} style={{ 
              fontSize: isLargeScreen ? '12px' : '10px',
              marginBottom: '2px'
            }}>
              {product.name} (x{product.quantity})
            </div>
          ))}
          {products.length > (isLargeScreen ? 2 : 1) && (
            <div style={{ 
              fontSize: isLargeScreen ? '10px' : '9px', 
              color: '#999'
            }}>
              +{products.length - (isLargeScreen ? 2 : 1)} more items
            </div>
          )}
        </div>
      ),
      responsive: ['md', 'lg', 'xl'],
    },
    {
      title: 'Amount',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: isLargeScreen ? 120 : 80,
      render: (amount, record) => (
        <span style={{ 
          fontSize: isLargeScreen ? '13px' : '11px', 
          fontWeight: 600,
          color: '#262626'
        }}>
          {record.currencySymbol}{amount.toFixed(2)}
        </span>
      ),
      sorter: (a, b) => a.totalAmount - b.totalAmount,
    },
    {
      title: 'Earnings',
      dataIndex: 'actualEarnings',
      key: 'actualEarnings',
      width: isLargeScreen ? 130 : 80,
      render: (earnings, record) => (
        <Tooltip title={`Commission: ${record.commissionRate}%\nStatus: ${record.status === 'delivered' ? 'Confirmed' : 'Pending delivery'}`}>
          <span style={{ 
            color: record.status === 'delivered' ? '#52c41a' : '#faad14', 
            fontWeight: 600,
            fontSize: isLargeScreen ? '13px' : '11px'
          }}>
            {record.currencySymbol}{record.netAmount.toFixed(2)}
            {record.status === 'delivered' && 
              <Badge status="success" style={{ marginLeft: 4 }} />
            }
          </span>
        </Tooltip>
      ),
      sorter: (a, b) => a.netAmount - b.netAmount,
      responsive: ['sm', 'md', 'lg', 'xl'],
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: isLargeScreen ? 110 : 80,
      render: (status) => (
        <Tag 
          color={getStatusColor(status)} 
          icon={getStatusIcon(status)} 
          style={{ 
            fontSize: isLargeScreen ? '11px' : '9px',
            padding: isLargeScreen ? '4px 8px' : '2px 6px',
            borderRadius: '6px'
          }}
        >
          {status ? status.toUpperCase() : 'PENDING'}
        </Tag>
      ),
    },
    {
      title: 'Payment',
      dataIndex: 'paymentStatus',
      key: 'paymentStatus',
      width: isLargeScreen ? 100 : 80,
      render: (paymentStatus) => {
        const getPaymentStatusColor = (status) => {
          switch (status) {
            case 'completed': return 'green';
            case 'processing': return 'blue';
            case 'failed': return 'red';
            case 'pending': return 'orange';
            default: return 'default';
          }
        };
        return (
          <Tag 
            color={getPaymentStatusColor(paymentStatus)} 
            style={{ 
              fontSize: isLargeScreen ? '11px' : '9px',
              padding: isLargeScreen ? '4px 8px' : '2px 6px',
              borderRadius: '6px'
            }}
          >
            {paymentStatus === 'completed' ? 'PAID' : (paymentStatus ? paymentStatus.toUpperCase() : 'PENDING')}
          </Tag>
        );
      },
      responsive: ['lg', 'xl'],
    },
    {
      title: 'Date',
      dataIndex: 'orderDate',
      key: 'orderDate',
      width: isLargeScreen ? 120 : 90,
      render: (date) => (
        <span style={{ 
          fontSize: isLargeScreen ? '12px' : '10px',
          color: '#595959'
        }}>
          {date}
        </span>
      ),
      sorter: (a, b) => new Date(a.orderDate) - new Date(b.orderDate),
      responsive: ['md', 'lg', 'xl'],
    },
    {
      title: 'Actions',
      key: 'actions',
      width: isLargeScreen ? 140 : 110,
      fixed: 'right',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Button
            type="primary"
            size={isLargeScreen ? "default" : "small"}
            icon={<EyeOutlined />}
            onClick={() => handleViewOrder(record)}
            style={{ 
              fontSize: isLargeScreen ? '12px' : '10px', 
              padding: isLargeScreen ? '4px 12px' : '2px 6px',
              borderRadius: '6px'
            }}
          >
            View Details
          </Button>
          {record.status !== 'delivered' && record.status !== 'cancelled' && (
            <Select
              size={isLargeScreen ? "default" : "small"}
              value={record.status}
              onChange={(value) => handleUpdateOrderStatus(record.id, value)}
              style={{ 
                width: '100%', 
                fontSize: isLargeScreen ? '12px' : '10px'
              }}
            >
              <Option value="pending">Pending</Option>
              <Option value="processing">Processing</Option>
              <Option value="shipped">Shipped</Option>
              <Option value="delivered">Delivered</Option>
            </Select>
          )}
        </Space>
      ),
    },
  ];

  const totalOrders = orders.length;
  const pendingOrders = orders.filter(order => order.status === 'pending').length;
  const deliveredOrders = orders.filter(order => order.status === 'delivered').length;
  const totalEarnings = orders
    .filter(order => order.status === 'delivered')
    .reduce((sum, order) => sum + order.netAmount, 0);

  return (
    <ErrorBoundary>
      <div className='min-h-screen bg-gray-50 p-4 sm:p-6'>
        <Title level={2} className='text-xl sm:text-2xl font-bold text-gray-800 mb-4 sm:mb-6'>Orders Management</Title>
      
        {/* Responsive Statistics Cards - Improved for large screens */}
        <Row gutter={[24, 24]} justify="space-between" className="mb-8">
          <Col xs={24} sm={12} md={6} lg={6} xl={6}>
            <Card 
              size={isLargeScreen ? "default" : "small"}
              hoverable 
              className="rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-indigo-500 to-purple-600 border-0"
            >
              <Statistic
                title="Total Orders"
                value={totalOrders}
                prefix={<ShoppingCartOutlined className="text-white" />}
                valueStyle={{ 
                  color: 'white', 
                  fontSize: window.innerWidth > 1400 ? '28px' : '22px',
                  fontWeight: 'bold'
                }}
                titleStyle={{ 
                  color: 'rgba(255,255,255,0.85)', 
                  fontSize: window.innerWidth > 1400 ? '16px' : '14px' 
                }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6} lg={6} xl={6}>
            <Card 
              size={window.innerWidth > 1400 ? "default" : "small"} 
              hoverable 
              className="rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-pink-400 to-red-500 border-0"
            >
              <Statistic
                title="Pending Orders"
                value={pendingOrders}
                prefix={<ClockCircleOutlined className="text-white" />}
                valueStyle={{ 
                  color: 'white', 
                  fontSize: window.innerWidth > 1400 ? '28px' : '22px',
                  fontWeight: 'bold'
                }}
                titleStyle={{ 
                  color: 'rgba(255,255,255,0.85)', 
                  fontSize: window.innerWidth > 1400 ? '16px' : '14px' 
                }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6} lg={6} xl={6}>
            <Card 
              size={window.innerWidth > 1400 ? "default" : "small"} 
              hoverable 
              className="rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-blue-400 to-cyan-400 border-0"
            >
              <Statistic
                title="Delivered Orders"
                value={deliveredOrders}
                prefix={<CheckCircleOutlined className="text-white" />}
                valueStyle={{ 
                  color: 'white', 
                  fontSize: window.innerWidth > 1400 ? '28px' : '22px',
                  fontWeight: 'bold'
                }}
                titleStyle={{ 
                  color: 'rgba(255,255,255,0.85)', 
                  fontSize: window.innerWidth > 1400 ? '16px' : '14px' 
                }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6} lg={6} xl={6}>
            <Card 
              size={window.innerWidth > 1400 ? "default" : "small"} 
              hoverable 
              className="rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-green-400 to-teal-400 border-0"
            >
              <Statistic
                title="Total Earnings"
                value={totalEarnings}
                prefix={<DollarOutlined className="text-white" />}
                formatter={(value) => `₹${value.toFixed(2)}`}
                valueStyle={{ 
                  color: 'white', 
                  fontSize: window.innerWidth > 1400 ? '28px' : '22px',
                  fontWeight: 'bold'
                }}
                titleStyle={{ 
                  color: 'rgba(255,255,255,0.85)', 
                  fontSize: window.innerWidth > 1400 ? '16px' : '14px' 
                }}
              />
            </Card>
          </Col>
        </Row>

        <Card className="rounded-xl shadow-lg border-0 overflow-hidden">
          {/* Responsive Filters */}
          <div className="mb-6 py-4 px-2 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
            <Row gutter={[16, 16]} justify="center" align="middle">
              <Col xs={24} sm={24} md={8} lg={6} xl={5}>
                <Input
                  placeholder="Search orders..."
                  prefix={<SearchOutlined className="text-gray-400" />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  className="rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200"
                  style={{ width: '100%', height: 38 }}
                  allowClear
                />
              </Col>
              <Col xs={24} sm={12} md={8} lg={6} xl={5}>
                <Select
                  placeholder="Order Status"
                  value={statusFilter}
                  onChange={setStatusFilter}
                  className="rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200"
                  style={{ width: '100%', height: 38 }}
                  allowClear
                >
                  <Option value="all">All</Option>
                  <Option value="pending">Pending</Option>
                  <Option value="processing">Processing</Option>
                  <Option value="shipped">Shipped</Option>
                  <Option value="delivered">Delivered</Option>
                </Select>
              </Col>
              <Col xs={24} sm={12} md={8} lg={6} xl={5}>
                <RangePicker 
                  value={dateRange}
                  onChange={setDateRange}
                  placeholder={['Start', 'End']}
                  className="rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200"
                  style={{ width: '100%', height: 38 }}
                  allowClear
                />
              </Col>
            </Row>
          </div>

          <div className="overflow-hidden rounded-lg">
            <Table
              columns={columns}
              dataSource={filteredOrders}
              rowKey="id"
              loading={loading}
              scroll={{ x: 1200, y: window.innerHeight > 800 ? 500 : 400 }}
              size={isLargeScreen ? 'middle' : 'small'}
              pagination={{
                pageSize: window.innerWidth > 1400 ? 15 : 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} of ${total} orders`,
                responsive: true,
                size: window.innerWidth > 1400 ? 'default' : 'small',
                position: ['bottomCenter'],
                hideOnSinglePage: false,
                pageSizeOptions: ['10', '15', '25', '50'],
                showLessItems: window.innerWidth <= 768
              }}
              className="shadow-md"
              rowClassName={(record, index) => 
                `hover:bg-blue-50 transition-colors duration-200 ${
                  index % 2 === 0 ? 'bg-gray-50' : 'bg-white'
                }`
              }
            />
          </div>
        </Card>

        {/* Order Detail Modal */}
        <Modal
          title={`Order Details - ${selectedOrder?.orderNumber || selectedOrder?.id}`}
          open={detailModalVisible}
          onCancel={() => setDetailModalVisible(false)}
          footer={null}
          width={window.innerWidth > 768 ? 900 : '95vw'}
          style={{ top: window.innerWidth <= 768 ? 10 : 50 }}
          styles={{ body: { padding: window.innerWidth <= 768 ? '16px' : '24px' } }}
        >
          {selectedOrder && (
            <div>
              {/* Order Progress */}
              <Card title="Order Progress" size="small" style={{ marginBottom: 16 }}>
                <Steps 
                  current={getOrderSteps(selectedOrder).findIndex(step => step.status === 'wait')}
                  size={window.innerWidth <= 768 ? 'small' : 'default'}
                  direction={window.innerWidth <= 480 ? 'vertical' : 'horizontal'}
                >
                  {getOrderSteps(selectedOrder).map((step, index) => (
                    <Step key={index} title={step.title} />
                  ))}
                </Steps>
              </Card>

              {/* Order Information */}
              <Descriptions 
                bordered 
                column={window.innerWidth <= 768 ? 1 : 2} 
                size="small"
                style={{ marginBottom: 16 }}
              >
                <Descriptions.Item label="Order ID">{selectedOrder.orderNumber}</Descriptions.Item>
                <Descriptions.Item label="Status">
                  <Tag color={getStatusColor(selectedOrder.status)} icon={getStatusIcon(selectedOrder.status)}>
                    {selectedOrder.status ? selectedOrder.status.toUpperCase() : 'PENDING'}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="Customer">{selectedOrder.customerName}</Descriptions.Item>
                <Descriptions.Item label="Email">{selectedOrder.customerEmail}</Descriptions.Item>
                <Descriptions.Item label="Phone">{selectedOrder.customerPhone}</Descriptions.Item>
                <Descriptions.Item label="Payment">{selectedOrder.paymentMethod}</Descriptions.Item>
                <Descriptions.Item label="Order Date">{selectedOrder.orderDate}</Descriptions.Item>
                <Descriptions.Item label="Shipped">{selectedOrder.shippedDate || 'Not shipped'}</Descriptions.Item>
                <Descriptions.Item label="Delivered">{selectedOrder.deliveryDate || 'Not delivered'}</Descriptions.Item>
                <Descriptions.Item label="Tracking">
                  {selectedOrder.trackingNumber || 'Not assigned'}
                </Descriptions.Item>
                <Descriptions.Item label="Address" span={window.innerWidth <= 768 ? 1 : 2}>
                  {selectedOrder.shippingAddress}
                </Descriptions.Item>
              </Descriptions>

              {/* Products */}
              <Title level={4} style={{ marginTop: 24, marginBottom: 16 }}>Products</Title>
              <Table
                dataSource={selectedOrder.products}
                pagination={false}
                size="small"
                scroll={{ x: 400 }}
                columns={[
                  {
                    title: 'Product',
                    dataIndex: 'name',
                    key: 'name',
                    width: 150,
                    ellipsis: true,
                  },
                  {
                    title: 'SKU',
                    dataIndex: 'sku',
                    key: 'sku',
                    width: 80,
                    responsive: ['md', 'lg', 'xl'],
                  },
                  {
                    title: 'Qty',
                    dataIndex: 'quantity',
                    key: 'quantity',
                    align: 'center',
                    width: 50,
                  },
                  {
                    title: 'Price',
                    dataIndex: 'price',
                    key: 'price',
                    width: 80,
                    render: (price) => `${selectedOrder.currencySymbol}${price.toFixed(2)}`,
                  },
                  {
                    title: 'Total',
                    key: 'total',
                    width: 80,
                    render: (_, record) => `${selectedOrder.currencySymbol}${(record.quantity * record.price).toFixed(2)}`,
                  },
                ]}
                summary={() => (
                  <Table.Summary>
                    <Table.Summary.Row>
                      <Table.Summary.Cell colSpan={window.innerWidth <= 768 ? 3 : 4}><strong>Subtotal</strong></Table.Summary.Cell>
                      <Table.Summary.Cell>
                        <strong>{selectedOrder.currencySymbol}{selectedOrder.totalAmount.toFixed(2)}</strong>
                      </Table.Summary.Cell>
                    </Table.Summary.Row>
                    <Table.Summary.Row>
                      <Table.Summary.Cell colSpan={window.innerWidth <= 768 ? 3 : 4}><strong>Commission ({((selectedOrder.commission / selectedOrder.totalAmount) * 100).toFixed(1)}%)</strong></Table.Summary.Cell>
                      <Table.Summary.Cell>
                        <strong style={{ color: '#f5222d' }}>-{selectedOrder.currencySymbol}{selectedOrder.commission.toFixed(2)}</strong>
                      </Table.Summary.Cell>
                    </Table.Summary.Row>
                    <Table.Summary.Row>
                      <Table.Summary.Cell colSpan={window.innerWidth <= 768 ? 3 : 4}><strong>Your Earnings</strong></Table.Summary.Cell>
                      <Table.Summary.Cell>
                        <strong style={{ color: '#52c41a' }}>{selectedOrder.currencySymbol}{selectedOrder.netAmount.toFixed(2)}</strong>
                      </Table.Summary.Cell>
                    </Table.Summary.Row>
                  </Table.Summary>
                )}
              />
            </div>
          )}
        </Modal>
      </div>
    </ErrorBoundary>
  );
};

export default OrdersManagement;