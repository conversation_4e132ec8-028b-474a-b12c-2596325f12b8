<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Carousel Upload Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
        .info {
            color: #17a2b8;
        }
        .warning {
            color: #ffc107;
        }
        .file-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Carousel Upload Debug Tool</h1>
        <p>This tool helps debug carousel image upload issues by testing different scenarios and providing detailed logging.</p>
        
        <form id="carouselForm">
            <div class="form-group">
                <label for="imageFile">Image File (Required):</label>
                <input type="file" id="imageFile" name="image" accept="image/*" required>
                <div id="fileInfo" class="file-info" style="display: none;"></div>
            </div>
            
            <div class="form-group">
                <label for="title">Title:</label>
                <input type="text" id="title" name="title" value="Test Carousel Image" required>
            </div>
            
            <div class="form-group">
                <label for="description">Description:</label>
                <textarea id="description" name="description" rows="3">Test description for debugging carousel upload</textarea>
            </div>
            
            <div class="form-group">
                <label for="linkUrl">Link URL:</label>
                <input type="url" id="linkUrl" name="linkUrl" value="https://example.com" placeholder="https://example.com">
            </div>
            
            <div class="form-group">
                <label for="authToken">Admin Token (Optional):</label>
                <input type="text" id="authToken" name="authToken" placeholder="Bearer token for authentication">
                <button type="button" onclick="getAdminToken()" style="margin-top: 5px; padding: 5px 10px; font-size: 12px;">🔑 Get Admin Token</button>
            </div>
            
            <button type="button" onclick="testUpload()">🚀 Test Upload</button>
            <button type="button" onclick="testWithoutFile()">❌ Test Without File</button>
            <button type="button" onclick="clearLog()">🧹 Clear Log</button>
        </form>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Show file information when selected
        document.getElementById('imageFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const fileInfo = document.getElementById('fileInfo');
            
            if (file) {
                fileInfo.style.display = 'block';
                fileInfo.innerHTML = `
                    <strong>File Details:</strong><br>
                    Name: ${file.name}<br>
                    Size: ${(file.size / 1024).toFixed(2)} KB<br>
                    Type: ${file.type}<br>
                    Last Modified: ${new Date(file.lastModified).toLocaleString()}
                `;
                log(`📁 File selected: ${file.name} (${file.type}, ${(file.size / 1024).toFixed(2)} KB)`, 'info');
            } else {
                fileInfo.style.display = 'none';
            }
        });
        
        async function testUpload() {
            log('🚀 Starting carousel upload test...', 'info');
            
            const form = document.getElementById('carouselForm');
            const formData = new FormData();
            
            // Get form values
            const imageFile = document.getElementById('imageFile').files[0];
            const title = document.getElementById('title').value;
            const description = document.getElementById('description').value;
            const linkUrl = document.getElementById('linkUrl').value;
            const authToken = document.getElementById('authToken').value;
            
            // Validate file
            if (!imageFile) {
                log('❌ No file selected! Please select an image file.', 'error');
                return;
            }
            
            // Build FormData
            log('📦 Building FormData...', 'info');
            formData.append('image', imageFile);
            formData.append('title', title);
            formData.append('description', description);
            formData.append('linkUrl', linkUrl);
            
            // Log FormData contents
            log('📋 FormData contents:', 'info');
            for (let [key, value] of formData.entries()) {
                if (value instanceof File) {
                    log(`  ${key}: File(${value.name}, ${value.type}, ${value.size} bytes)`, 'info');
                } else {
                    log(`  ${key}: "${value}"`, 'info');
                }
            }
            
            // Prepare headers
            const headers = {};
            if (authToken) {
                headers['Authorization'] = authToken.startsWith('Bearer ') ? authToken : `Bearer ${authToken}`;
                log('🔐 Added authorization header', 'info');
            }
            
            try {
                log('📤 Sending request to /api/admin/homepage-settings/carousel...', 'info');
                
                const response = await fetch(`${API_BASE}/api/admin/homepage-settings/carousel`, {
                    method: 'POST',
                    headers: headers,
                    body: formData
                });
                
                log(`📥 Response received: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                
                const responseData = await response.json();
                log('📄 Response data:', 'info');
                log(JSON.stringify(responseData, null, 2), response.ok ? 'success' : 'error');
                
                if (response.ok) {
                    log('✅ Upload successful!', 'success');
                } else {
                    log('❌ Upload failed!', 'error');
                    
                    // Provide specific debugging info
                    if (responseData.message === 'Image file is required') {
                        log('🔍 DIAGNOSIS: Server did not receive the image file', 'warning');
                        log('   Possible causes:', 'warning');
                        log('   - Form field name mismatch (should be "image")', 'warning');
                        log('   - File not properly attached to FormData', 'warning');
                        log('   - Multer middleware issue', 'warning');
                    }
                }
                
            } catch (error) {
                log(`❌ Network error: ${error.message}`, 'error');
                
                if (error.message.includes('Failed to fetch')) {
                    log('🔍 DIAGNOSIS: Cannot connect to server', 'warning');
                    log('   - Check if server is running on port 5000', 'warning');
                    log('   - Check for CORS issues', 'warning');
                }
            }
        }
        
        async function testWithoutFile() {
            log('🧪 Testing upload without file (should fail)...', 'info');
            
            const formData = new FormData();
            formData.append('title', 'test');
            formData.append('description', 'test');
            formData.append('linkUrl', '');
            
            const authToken = document.getElementById('authToken').value;
            const headers = {};
            if (authToken) {
                headers['Authorization'] = authToken.startsWith('Bearer ') ? authToken : `Bearer ${authToken}`;
            }
            
            try {
                const response = await fetch(`${API_BASE}/api/admin/homepage-settings/carousel`, {
                    method: 'POST',
                    headers: headers,
                    body: formData
                });
                
                const responseData = await response.json();
                
                if (response.status === 400 && responseData.message === 'Image file is required') {
                    log('✅ Server correctly rejected request without file', 'success');
                } else {
                    log(`❌ Unexpected response: ${response.status}`, 'error');
                    log(JSON.stringify(responseData, null, 2), 'error');
                }
                
            } catch (error) {
                log(`❌ Network error: ${error.message}`, 'error');
            }
        }
        
        // Initialize
        log('🔧 Carousel Upload Debug Tool initialized', 'info');
        log('📝 Instructions:', 'info');
        log('1. Select an image file', 'info');
        log('2. Fill in the form fields', 'info');
        log('3. Add admin token if available', 'info');
        log('4. Click "Test Upload" to debug the issue', 'info');
        log('', 'info');
    </script>
</body>
</html>