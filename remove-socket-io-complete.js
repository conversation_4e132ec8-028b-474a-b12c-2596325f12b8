#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 Removing Socket.IO from complete codebase...\n');

// Files to remove or replace
const filesToRemove = [
  'server/src/socket/orderSocket.js',
  'server/src/services/socketService.js',
  'client/src/services/socketService.js',
  'client/src/services/socketService-disabled.js'
];

// Remove socket.io files
console.log('📁 Removing Socket.IO files...');
filesToRemove.forEach(file => {
  const fullPath = path.join(process.cwd(), file);
  if (fs.existsSync(fullPath)) {
    fs.unlinkSync(fullPath);
    console.log(`   ✅ Removed: ${file}`);
  } else {
    console.log(`   ⚠️ Not found: ${file}`);
  }
});

// Update package.json files
console.log('\n📦 Updating package.json files...');

// Server package.json
const serverPackagePath = path.join(process.cwd(), 'server', 'package.json');
if (fs.existsSync(serverPackagePath)) {
  const serverPackage = JSON.parse(fs.readFileSync(serverPackagePath, 'utf8'));
  
  // Remove socket.io dependency
  if (serverPackage.dependencies && serverPackage.dependencies['socket.io']) {
    delete serverPackage.dependencies['socket.io'];
    console.log('   ✅ Removed socket.io from server dependencies');
  }
  
  fs.writeFileSync(serverPackagePath, JSON.stringify(serverPackage, null, 2));
}

// Client package.json
const clientPackagePath = path.join(process.cwd(), 'client', 'package.json');
if (fs.existsSync(clientPackagePath)) {
  const clientPackage = JSON.parse(fs.readFileSync(clientPackagePath, 'utf8'));
  
  // Remove socket.io-client dependency
  if (clientPackage.dependencies && clientPackage.dependencies['socket.io-client']) {
    delete clientPackage.dependencies['socket.io-client'];
    console.log('   ✅ Removed socket.io-client from client dependencies');
  }
  
  fs.writeFileSync(clientPackagePath, JSON.stringify(clientPackage, null, 2));
}

console.log('\n✅ Socket.IO removal completed!');
console.log('\n📋 Next steps:');
console.log('   1. Run: cd server && npm install');
console.log('   2. Run: cd client && npm install');
console.log('   3. Review the replacement implementations');
console.log('   4. Test the application without real-time features');