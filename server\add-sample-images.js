const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Product = require('./src/models/Product');

// Connect to database
mongoose.connect(process.env.MONGODB_URI)
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => console.error('MongoDB connection error:', err));

// Sample product images
const sampleImages = [
  'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1484704849700-f032a568e944?w=400&h=400&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1487215078519-e21cc028cb29?w=400&h=400&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=400&h=400&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1572635196237-14b3f281503f?w=400&h=400&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1526170375885-4d8ecf77b99f?w=400&h=400&auto=format&fit=crop',
  'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&auto=format&fit=crop',
];

async function addSampleImages() {
  try {
    // Find all products that don't have images or have empty images array
    const products = await Product.find({
      $or: [
        { images: { $exists: false } },
        { images: { $size: 0 } },
        { images: null }
      ]
    });

    console.log(`Found ${products.length} products without images`);

    for (let i = 0; i < products.length; i++) {
      const product = products[i];
      
      // Assign random images (1-3 images per product)
      const numImages = Math.floor(Math.random() * 3) + 1;
      const productImages = [];
      
      for (let j = 0; j < numImages; j++) {
        const randomIndex = Math.floor(Math.random() * sampleImages.length);
        const image = sampleImages[randomIndex];
        if (!productImages.includes(image)) {
          productImages.push(image);
        }
      }

      // Update the product with images
      await Product.findByIdAndUpdate(product._id, {
        $set: { images: productImages }
      });

      console.log(`✅ Updated product: ${product.name} with ${productImages.length} images`);
    }

    console.log('✨ All products updated with sample images!');
    
  } catch (error) {
    console.error('Error adding sample images:', error);
  } finally {
    mongoose.disconnect();
  }
}

addSampleImages();
