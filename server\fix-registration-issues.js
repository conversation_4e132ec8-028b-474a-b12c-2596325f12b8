const mongoose = require('mongoose');
const User = require('./src/models/User');
require('dotenv').config();

// Fix common registration issues
const fixRegistrationIssues = async () => {
    console.log('🔧 FIXING REGISTRATION ISSUES\n');
    console.log('='.repeat(50));
    
    try {
        // Connect to database
        console.log('1️⃣ Connecting to database...');
        const DB_URL = process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/multi-vendor-ecommerce';
        await mongoose.connect(DB_URL, {
            serverSelectionTimeoutMS: 10000,
        });
        console.log('✅ Database connected successfully');
        
        // Check database collections
        console.log('\n2️⃣ Checking database collections...');
        const collections = await mongoose.connection.db.listCollections().toArray();
        console.log('📋 Available collections:', collections.map(c => c.name).join(', ') || 'None');
        
        // Check users collection
        console.log('\n3️⃣ Checking users collection...');
        const userCount = await User.countDocuments();
        console.log('📋 Total users in database:', userCount);
        
        // Check for duplicate emails (common cause of 400 errors)
        console.log('\n4️⃣ Checking for duplicate emails...');
        const duplicateEmails = await User.aggregate([
            {
                $group: {
                    _id: '$email',
                    count: { $sum: 1 }
                }
            },
            {
                $match: {
                    count: { $gt: 1 }
                }
            }
        ]);
        
        if (duplicateEmails.length > 0) {
            console.log('⚠️ Found duplicate emails:');
            duplicateEmails.forEach(dup => {
                console.log(`   - ${dup._id} (${dup.count} occurrences)`);
            });
            
            // Optionally clean up duplicates
            console.log('\n🧹 Cleaning up duplicate emails...');
            for (const dup of duplicateEmails) {
                const users = await User.find({ email: dup._id }).sort({ createdAt: 1 });
                // Keep the first user, remove the rest
                for (let i = 1; i < users.length; i++) {
                    await User.deleteOne({ _id: users[i]._id });
                    console.log(`   ✅ Removed duplicate user: ${users[i]._id}`);
                }
            }
        } else {
            console.log('✅ No duplicate emails found');
        }
        
        // Check indexes
        console.log('\n5️⃣ Checking database indexes...');
        const indexes = await User.collection.getIndexes();
        console.log('📋 Current indexes:', Object.keys(indexes).join(', '));
        
        // Ensure email index exists
        if (!indexes.email_1) {
            console.log('🔧 Creating email index...');
            await User.collection.createIndex({ email: 1 }, { unique: true });
            console.log('✅ Email index created');
        } else {
            console.log('✅ Email index already exists');
        }
        
        // Test user creation
        console.log('\n6️⃣ Testing user creation...');
        const testEmail = '<EMAIL>';
        
        // Remove test user if exists
        await User.deleteOne({ email: testEmail });
        
        const testUser = new User({
            firstName: 'Test',
            lastName: 'Fix',
            email: testEmail,
            password: 'password123',
            userType: 'customer'
        });
        
        await testUser.save();
        console.log('✅ Test user created successfully');
        
        // Clean up test user
        await User.deleteOne({ email: testEmail });
        console.log('✅ Test user cleaned up');
        
        // Check environment variables
        console.log('\n7️⃣ Checking environment variables...');
        const requiredEnvVars = ['JWT_SECRET', 'MONGODB_URI'];
        const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);
        
        if (missingEnvVars.length > 0) {
            console.log('⚠️ Missing environment variables:', missingEnvVars.join(', '));
        } else {
            console.log('✅ All required environment variables are set');
        }
        
        console.log('\n' + '='.repeat(50));
        console.log('🎉 REGISTRATION ISSUES FIXED');
        console.log('\n📋 SUMMARY:');
        console.log(`   - Database connection: ✅ Working`);
        console.log(`   - User collection: ✅ Accessible`);
        console.log(`   - Duplicate emails: ✅ Cleaned up`);
        console.log(`   - Database indexes: ✅ Verified`);
        console.log(`   - User creation: ✅ Working`);
        console.log(`   - Environment variables: ✅ Checked`);
        
    } catch (error) {
        console.error('❌ Fix process failed:', error.message);
        console.error('📋 Error details:', error);
        
        if (error.name === 'MongoNetworkError') {
            console.log('\n💡 SOLUTION: Start MongoDB service');
            console.log('   - Windows: net start MongoDB');
            console.log('   - macOS/Linux: sudo systemctl start mongod');
            console.log('   - Or use MongoDB Atlas cloud database');
        }
        
        if (error.name === 'ValidationError') {
            console.log('\n💡 SOLUTION: Check User model validation rules');
        }
        
    } finally {
        if (mongoose.connection.readyState === 1) {
            await mongoose.connection.close();
            console.log('\n📴 Database connection closed');
        }
    }
};

// Run the fix
fixRegistrationIssues();