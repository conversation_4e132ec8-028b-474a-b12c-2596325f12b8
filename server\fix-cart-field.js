/**
 * Quick fix for cart field in customerOrderController.js
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'src/controllers/customerOrderController.js');

// Read the file
let content = fs.readFileSync(filePath, 'utf8');

// Replace the incorrect field
content = content.replace(
  'await Cart.findOneAndDelete({ user: req.user._id });',
  'await Cart.findOneAndDelete({ customer: req.user._id });'
);

// Write back to file
fs.writeFileSync(filePath, content);

console.log('✅ Fixed cart field in customerOrderController.js');
console.log('Changed: { user: req.user._id } → { customer: req.user._id }');