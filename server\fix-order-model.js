/**
 * Fix the Order model pre-save middleware
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'src/models/Order.js');

// Read the file
let content = fs.readFileSync(filePath, 'utf8');

// Replace the problematic pre-save middleware
const oldMiddleware = `// Pre-save middleware to generate order number
orderSchema.pre('save', async function(next) {
  if (this.isNew && !this.orderNumber) {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    // Find the last order of the day
    const lastOrder = await this.constructor.findOne({
      orderNumber: new RegExp(\`^ORD\${year}\${month}\${day}\`)
    }).sort({ orderNumber: -1 });
    
    let sequence = 1;
    if (lastOrder) {
      const lastSequence = parseInt(lastOrder.orderNumber.slice(-4));
      sequence = lastSequence + 1;
    }
    
    this.orderNumber = \`ORD\${year}\${month}\${day}\${sequence.toString().padStart(4, '0')}\`;
  }
  
  // Add status change to timeline
  if (this.isModified('status') && !this.isNew) {
    this.timeline.push({
      status: this.status,
      timestamp: new Date()
    });
  }
  
  next();
});`;

const newMiddleware = `// Pre-save middleware to generate order number
orderSchema.pre('save', async function(next) {
  try {
    if (this.isNew && !this.orderNumber) {
      const date = new Date();
      const year = date.getFullYear().toString().slice(-2);
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      
      // Generate a simple order number with timestamp
      const timestamp = Date.now().toString().slice(-6);
      this.orderNumber = \`ORD\${year}\${month}\${day}\${timestamp}\`;
    }
    
    // Add status change to timeline
    if (this.isModified('status') && !this.isNew) {
      this.timeline.push({
        status: this.status,
        timestamp: new Date()
      });
    }
    
    next();
  } catch (error) {
    next(error);
  }
});`;

// Replace the middleware
content = content.replace(oldMiddleware, newMiddleware);

// Write back to file
fs.writeFileSync(filePath, content);

console.log('✅ Fixed Order model pre-save middleware');
console.log('Changed: Complex sequence generation → Simple timestamp-based generation');