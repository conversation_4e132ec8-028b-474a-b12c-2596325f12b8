#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Running Component Analysis for Large Components...\n');

// Check if analysis scripts exist
const basicAnalysisScript = path.join(__dirname, 'analyze-code-size.js');
const detailedAnalysisScript = path.join(__dirname, 'server', 'detailed-component-analysis.js');

console.log('📊 Running Basic Code Size Analysis...');
console.log('='.repeat(80));

try {
  if (fs.existsSync(basicAnalysisScript)) {
    execSync(`node "${basicAnalysisScript}"`, { stdio: 'inherit' });
  } else {
    console.log('❌ Basic analysis script not found');
  }
} catch (error) {
  console.error('❌ Error running basic analysis:', error.message);
}

console.log('\n📋 Running Detailed Component Analysis...');
console.log('='.repeat(80));

try {
  if (fs.existsSync(detailedAnalysisScript)) {
    execSync(`node "${detailedAnalysisScript}"`, { stdio: 'inherit' });
  } else {
    console.log('❌ Detailed analysis script not found');
  }
} catch (error) {
  console.error('❌ Error running detailed analysis:', error.message);
}

console.log('\n✅ Component analysis completed!');
console.log('\n💡 Next steps:');
console.log('   1. Review the components with >200 lines for refactoring opportunities');
console.log('   2. Check if test scripts exist for large components');
console.log('   3. Create unit tests for complex components');
console.log('   4. Consider breaking down large components into smaller modules');