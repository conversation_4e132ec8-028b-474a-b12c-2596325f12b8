/**
 * API-based debug script for vendor extraction issue
 * Product ID: 6881fd806652576061a0be95
 */

const axios = require('axios');
require('dotenv').config();

const API_BASE_URL = process.env.API_URL || 'http://localhost:5000/api';

// Test credentials
const TEST_CUSTOMER = {
  email: '<EMAIL>',
  password: 'Free@009'
};

let authToken = null;

// Helper function to make authenticated API calls
const apiCall = async (method, endpoint, data = null) => {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...(authToken && { Authorization: `Bearer ${authToken}` })
      }
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, {
      status: error.response?.status,
      message: error.response?.data?.message || error.message,
      data: error.response?.data
    });
    throw error;
  }
};

// Login to get auth token
const login = async () => {
  console.log('🔐 Logging in...');
  try {
    const response = await apiCall('POST', '/auth/login', TEST_CUSTOMER);
    
    // Debug the response structure
    console.log('Login response structure:', {
      hasToken: !!response.token,
      hasData: !!response.data,
      hasUser: !!response.user,
      responseKeys: Object.keys(response)
    });
    
    // Try different possible token locations
    authToken = response.token || response.data?.token || response.accessToken;
    
    if (authToken) {
      console.log('✅ Login successful, token acquired');
      console.log('Token preview:', authToken.substring(0, 20) + '...');
    } else {
      console.log('❌ Login response received but no token found');
      console.log('Full response:', response);
    }
    
    return response;
  } catch (error) {
    console.log('❌ Login failed:', error.message);
    return null;
  }
};

// Get product details via API
const getProductDetails = async (productId) => {
  console.log(`\n🔍 Fetching product details for: ${productId}`);
  try {
    const response = await apiCall('GET', `/public/products/${productId}`);
    console.log('✅ Product found:', {
      id: response.data._id,
      name: response.data.name,
      status: response.data.status,
      hasVendor: !!response.data.vendor,
      vendorType: typeof response.data.vendor,
      vendorData: response.data.vendor
    });
    return response.data;
  } catch (error) {
    console.log('❌ Failed to fetch product details');
    return null;
  }
};

// Add product to cart
const addToCart = async (productId, quantity = 1) => {
  console.log(`\n🛒 Adding product to cart: ${productId}`);
  try {
    const response = await apiCall('POST', '/customer/cart/add', {
      productId,
      quantity
    });
    console.log('✅ Product added to cart successfully');
    return response.data;
  } catch (error) {
    console.log('❌ Failed to add product to cart');
    return null;
  }
};

// Get cart details
const getCartDetails = async () => {
  console.log('\n🛒 Fetching cart details...');
  try {
    const response = await apiCall('GET', '/customer/cart');
    console.log('✅ Cart fetched successfully');
    
    if (response.data && response.data.items) {
      console.log(`Cart contains ${response.data.items.length} items:`);
      
      response.data.items.forEach((item, index) => {
        console.log(`\nItem ${index + 1}:`, {
          productId: item.product?._id,
          productName: item.product?.name,
          quantity: item.quantity,
          priceAtAdd: item.priceAtAdd,
          hasVendorField: !!item.vendor,
          vendorType: typeof item.vendor,
          vendorValue: item.vendor,
          productHasVendor: !!item.product?.vendor,
          productVendorType: typeof item.product?.vendor,
          productVendorValue: item.product?.vendor
        });
        
        // Test vendor extraction strategies
        console.log('\n  🧪 Testing vendor extraction:');
        
        let vendorId = null;
        let strategy = 'none';
        
        // Strategy 1: Direct vendor from cart item
        if (item.vendor) {
          if (typeof item.vendor === 'object' && item.vendor._id) {
            vendorId = item.vendor._id.toString();
            strategy = 'item.vendor._id';
          } else if (typeof item.vendor === 'string' && item.vendor.length === 24) {
            vendorId = item.vendor;
            strategy = 'item.vendor (string)';
          }
        }
        
        console.log(`  Strategy 1 (item.vendor): ${strategy} -> ${vendorId || 'FAILED'}`);
        
        // Strategy 2: Vendor from populated product
        if (!vendorId && item.product?.vendor) {
          if (typeof item.product.vendor === 'object' && item.product.vendor._id) {
            vendorId = item.product.vendor._id.toString();
            strategy = 'item.product.vendor._id';
          } else if (typeof item.product.vendor === 'string' && item.product.vendor.length === 24) {
            vendorId = item.product.vendor;
            strategy = 'item.product.vendor (string)';
          }
        }
        
        console.log(`  Strategy 2 (product.vendor): ${strategy} -> ${vendorId || 'FAILED'}`);
        
        if (vendorId) {
          console.log(`  ✅ Vendor extraction successful: ${vendorId} (via ${strategy})`);
        } else {
          console.log('  ❌ Vendor extraction FAILED!');
        }
      });
    }
    
    return response.data;
  } catch (error) {
    console.log('❌ Failed to fetch cart details');
    return null;
  }
};

// Get user profile
const getUserProfile = async () => {
  console.log('\n👤 Fetching user profile...');
  try {
    const response = await apiCall('GET', '/customer/profile');
    console.log('✅ User profile fetched successfully');
    return response.data;
  } catch (error) {
    console.log('❌ Failed to fetch user profile');
    return null;
  }
};

// Test checkout process
const testCheckout = async () => {
  console.log('\n🧪 Testing checkout process...');
  try {
    // Get cart first
    const cart = await getCartDetails();
    if (!cart || !cart.items || cart.items.length === 0) {
      console.log('❌ Cart is empty, cannot test checkout');
      return;
    }
    
    // Simulate the checkout vendor extraction logic
    console.log('\n🔧 Simulating checkout vendor extraction...');
    
    const orderItems = cart.items.map((item, index) => {
      console.log(`\nProcessing item ${index + 1}: ${item.product?.name}`);
      
      // FIXED: Enhanced vendor ID extraction with comprehensive fallback strategies
      let vendorId = null;
      let vendorExtractionStrategy = 'none';
      
      // Strategy 1: Check direct vendor reference from cart item (most reliable)
      if (item.vendor) {
        if (typeof item.vendor === 'object' && item.vendor._id) {
          vendorId = item.vendor._id.toString();
          vendorExtractionStrategy = 'item.vendor._id';
        } else if (typeof item.vendor === 'string' && item.vendor.length === 24) {
          // Valid ObjectId string
          vendorId = item.vendor;
          vendorExtractionStrategy = 'item.vendor (string)';
        }
      }
      
      // Strategy 2: Check vendor from populated product (fallback)
      if (!vendorId && item.product?.vendor) {
        if (typeof item.product.vendor === 'object' && item.product.vendor._id) {
          vendorId = item.product.vendor._id.toString();
          vendorExtractionStrategy = 'item.product.vendor._id';
        } else if (typeof item.product.vendor === 'string' && item.product.vendor.length === 24) {
          // Valid ObjectId string
          vendorId = item.product.vendor;
          vendorExtractionStrategy = 'item.product.vendor (string)';
        }
      }
      
      // Strategy 3: Log detailed debug info if still no vendor found
      if (!vendorId) {
        console.error('❌ Detailed vendor extraction debug:', {
          productId: item.product?._id,
          productName: item.product?.name,
          strategy: vendorExtractionStrategy,
          itemVendor: {
            exists: !!item.vendor,
            type: typeof item.vendor,
            value: item.vendor,
            hasId: !!(item.vendor?._id),
            id: item.vendor?._id
          },
          productVendor: {
            exists: !!(item.product?.vendor),
            type: typeof item.product?.vendor,
            value: item.product?.vendor,
            hasId: !!(item.product?.vendor?._id),
            id: item.product?.vendor?._id
          }
        });
        
        throw new Error(`Vendor information is missing for product: ${item.product?.name || 'Unknown Product'}. Please refresh your cart and try again.`);
      }
      
      console.log(`✅ Vendor ID extracted: ${vendorId} (via ${vendorExtractionStrategy})`);
      
      return {
        product: item.product._id,
        vendor: vendorId,
        name: item.product.name,
        quantity: item.quantity,
        unitPrice: item.priceAtAdd,
        totalPrice: item.priceAtAdd * item.quantity
      };
    });
    
    console.log('\n✅ All items processed successfully for checkout!');
    console.log('Order items:', orderItems.map(item => ({
      product: item.product,
      vendor: item.vendor,
      name: item.name,
      quantity: item.quantity
    })));
    
    return orderItems;
    
  } catch (error) {
    console.log('❌ Checkout simulation failed:', error.message);
    throw error;
  }
};

// Place actual order
const placeOrder = async () => {
  console.log('\n🛍️ Attempting to place order...');
  try {
    // Get user profile first
    const user = await getUserProfile();
    if (!user) {
      console.log('❌ Cannot place order without user profile');
      return null;
    }
    
    // Get cart details
    const cart = await getCartDetails();
    if (!cart || !cart.items || cart.items.length === 0) {
      console.log('❌ Cart is empty, cannot place order');
      return null;
    }
    
    // Test vendor extraction first
    const orderItems = await testCheckout();
    if (!orderItems) {
      console.log('❌ Vendor extraction failed, cannot place order');
      return null;
    }
    
    // Calculate totals
    const subtotal = cart.totalAmount || 0;
    const protectPromiseFee = 28;
    const total = subtotal + protectPromiseFee;
    
    // Prepare order data
    const orderData = {
      items: orderItems,
      billing: {
        firstName: user.firstName || 'Test',
        lastName: user.lastName || 'User',
        email: user.email || TEST_CUSTOMER.email,
        phone: user.phone || '1234567890',
        address: {
          street: user.address || 'Test Address',
          city: user.city || 'Test City',
          state: user.state || 'Test State',
          zipCode: user.zipCode || '12345',
          country: user.country || 'India'
        }
      },
      shipping: {
        firstName: user.firstName || 'Test',
        lastName: user.lastName || 'User',
        address: {
          street: user.address || 'Test Address',
          city: user.city || 'Test City',
          state: user.state || 'Test State',
          zipCode: user.zipCode || '12345',
          country: user.country || 'India'
        },
        method: 'standard',
        cost: 0
      },
      payment: {
        method: 'cod',
        status: 'pending'
      },
      pricing: {
        subtotal: subtotal,
        tax: 0,
        shipping: protectPromiseFee,
        discount: 0,
        total: total
      },
      customerNotes: 'Test order from debug script',
      estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
    };
    
    console.log('\n📦 Order data prepared:', {
      itemCount: orderData.items.length,
      total: orderData.pricing.total,
      paymentMethod: orderData.payment.method
    });
    
    // Place the order
    const response = await apiCall('POST', '/customer/orders', orderData);
    
    if (response.success) {
      console.log('🎉 Order placed successfully!');
      console.log('Order details:', {
        orderId: response.data.order.orderNumber,
        total: response.data.order.pricing.total,
        status: response.data.order.status,
        trackingNumbers: response.data.trackings?.map(t => t.trackingNumber) || []
      });
      return response.data;
    } else {
      console.log('❌ Order placement failed:', response.message);
      return null;
    }
    
  } catch (error) {
    console.log('❌ Order placement error:', error.response?.data?.message || error.message);
    
    // Log detailed error for debugging
    if (error.response?.data) {
      console.log('Error details:', error.response.data);
    }
    
    return null;
  }
};

// Main debug function
const main = async () => {
  console.log('🚀 Starting API-based vendor extraction debug and order placement');
  console.log('='.repeat(60));
  
  const productId = '6881fd806652576061a0be95';
  
  try {
    // 1. Try to login first
    const loginResult = await login();
    
    if (!authToken) {
      console.log('❌ Cannot test cart operations without authentication');
      console.log('Please ensure you have a test customer account or update the credentials');
      return;
    }
    
    // 2. Check if cart already has items
    let cart = await getCartDetails();
    
    if (!cart || !cart.items || cart.items.length === 0) {
      console.log('\n📦 Cart is empty, adding test product...');
      
      // 3. Get product details (public endpoint)
      const product = await getProductDetails(productId);
      
      if (!product) {
        console.log('❌ Cannot continue without product data');
        return;
      }
      
      // 4. Add product to cart
      const cartResult = await addToCart(productId);
      
      if (!cartResult) {
        console.log('❌ Cannot continue without adding product to cart');
        return;
      }
      
      // 5. Get updated cart details
      cart = await getCartDetails();
    } else {
      console.log('\n📦 Cart already has items, proceeding with existing cart...');
    }
    
    // 6. Test checkout process (vendor extraction)
    console.log('\n🧪 Testing vendor extraction...');
    try {
      await testCheckout();
      console.log('✅ Vendor extraction test passed!');
    } catch (error) {
      console.log('❌ Vendor extraction test failed:', error.message);
      console.log('This is the root cause of the checkout issue!');
      
      // Try to identify and fix the issue
      console.log('\n🔧 Attempting to diagnose and fix vendor extraction issue...');
      
      // Check if we need to re-add items to cart with proper vendor data
      if (cart && cart.items && cart.items.length > 0) {
        for (const item of cart.items) {
          if (!item.vendor || (typeof item.vendor === 'object' && !item.vendor._id)) {
            console.log(`⚠️ Item "${item.product?.name}" has missing/invalid vendor data`);
            console.log('Vendor data:', item.vendor);
            console.log('Product vendor data:', item.product?.vendor);
          }
        }
      }
      
      // Don't proceed with order if vendor extraction fails
      console.log('❌ Cannot place order due to vendor extraction failure');
      return;
    }
    
    // 7. Place the actual order
    console.log('\n🛍️ Proceeding to place order...');
    const orderResult = await placeOrder();
    
    if (orderResult) {
      console.log('\n🎉 SUCCESS! Order placed successfully!');
      console.log('This proves that the vendor extraction issue has been resolved.');
    } else {
      console.log('\n❌ Order placement failed');
      console.log('The vendor extraction may be working, but there might be other issues.');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    
    // Log stack trace for debugging
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  }
  
  console.log('\n🏁 Debug and order placement test complete');
};

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  getProductDetails,
  addToCart,
  getCartDetails,
  testCheckout
};