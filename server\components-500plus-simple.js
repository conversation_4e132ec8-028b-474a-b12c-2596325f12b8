#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// File extensions to analyze
const FRONTEND_EXTENSIONS = ['.js', '.jsx', '.ts', '.tsx', '.vue', '.css', '.scss', '.sass'];
const BACKEND_EXTENSIONS = ['.js', '.ts', '.py', '.java', '.php', '.rb', '.go', '.cs'];

// Directories to exclude
const EXCLUDE_DIRS = [
    'node_modules', 
    '.git', 
    'dist', 
    'build', 
    'coverage', 
    '.next', 
    '.nuxt',
    '__pycache__',
    'vendor',
    'uploads',
    '.vscode',
    '.augment',
    '.qodo'
];

// Files to exclude
const EXCLUDE_FILES = [
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',
    '.DS_Store',
    'Thumbs.db'
];

function countLines(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        return content.split('\n').length;
    } catch (error) {
        return 0;
    }
}

function shouldExcludeDir(dirName) {
    return EXCLUDE_DIRS.some(exclude => dirName.includes(exclude));
}

function shouldExcludeFile(fileName) {
    return EXCLUDE_FILES.includes(fileName);
}

function getFileType(filePath, baseDir) {
    const ext = path.extname(filePath).toLowerCase();
    
    if (baseDir.includes('client') || baseDir.includes('frontend')) {
        if (FRONTEND_EXTENSIONS.includes(ext)) return 'Frontend';
    }
    
    if (baseDir.includes('server') || baseDir.includes('backend') || baseDir.includes('api')) {
        if (BACKEND_EXTENSIONS.includes(ext)) return 'Backend';
    }
    
    // Fallback based on extension
    if (FRONTEND_EXTENSIONS.includes(ext)) return 'Frontend';
    if (BACKEND_EXTENSIONS.includes(ext)) return 'Backend';
    
    return 'Other';
}

function analyzeDirectory(dirPath, results = [], baseDir = '') {
    try {
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                if (!shouldExcludeDir(item)) {
                    analyzeDirectory(fullPath, results, baseDir || item);
                }
            } else if (stat.isFile()) {
                if (!shouldExcludeFile(item)) {
                    const lines = countLines(fullPath);
                    
                    // Only include files with 500+ lines
                    if (lines >= 500) {
                        const fileType = getFileType(fullPath, baseDir);
                        
                        results.push({
                            name: item,
                            lines: lines,
                            type: fileType
                        });
                    }
                }
            }
        }
    } catch (error) {
        console.error(`Error analyzing directory ${dirPath}:`, error.message);
    }
    
    return results;
}

// Main execution
console.log('Finding components with 500+ lines of code...\n');

const results = [];
const projectRoot = path.join(__dirname, '..');

// Analyze client directory
const clientPath = path.join(projectRoot, 'client');
if (fs.existsSync(clientPath)) {
    analyzeDirectory(clientPath, results, 'client');
}

// Analyze server directory
const serverPath = path.join(projectRoot, 'server');
if (fs.existsSync(serverPath)) {
    analyzeDirectory(serverPath, results, 'server');
}

// Analyze root level files
const rootItems = fs.readdirSync(projectRoot);
rootItems.forEach(item => {
    const fullPath = path.join(projectRoot, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isFile() && !shouldExcludeFile(item)) {
        const lines = countLines(fullPath);
        
        if (lines >= 500) {
            const fileType = getFileType(fullPath, '');
            
            results.push({
                name: item,
                lines: lines,
                type: fileType
            });
        }
    }
});

// Sort by lines (descending)
const sortedResults = results.sort((a, b) => b.lines - a.lines);

// Display results
console.log('COMPONENTS WITH 500+ LINES OF CODE:');
console.log('=====================================\n');

if (sortedResults.length === 0) {
    console.log('No components found with 500+ lines of code.');
} else {
    // Frontend components
    const frontendComponents = sortedResults.filter(f => f.type === 'Frontend');
    if (frontendComponents.length > 0) {
        console.log('FRONTEND COMPONENTS:');
        frontendComponents.forEach(file => {
            console.log(`${file.name} - ${file.lines} lines`);
        });
        console.log('');
    }
    
    // Backend components
    const backendComponents = sortedResults.filter(f => f.type === 'Backend');
    if (backendComponents.length > 0) {
        console.log('BACKEND COMPONENTS:');
        backendComponents.forEach(file => {
            console.log(`${file.name} - ${file.lines} lines`);
        });
        console.log('');
    }
    
    // Other components
    const otherComponents = sortedResults.filter(f => f.type === 'Other');
    if (otherComponents.length > 0) {
        console.log('OTHER COMPONENTS:');
        otherComponents.forEach(file => {
            console.log(`${file.name} - ${file.lines} lines`);
        });
        console.log('');
    }
    
    console.log('ALL COMPONENTS (sorted by line count):');
    sortedResults.forEach(file => {
        console.log(`${file.name} - ${file.lines} lines`);
    });
    
    console.log(`\nTotal: ${sortedResults.length} components with 500+ lines`);
}