const mongoose = require('mongoose');
const { User, Vendor } = require('./src/models');
require('dotenv').config();

async function debugVendorProfile() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find all vendor users
    const vendorUsers = await User.find({ userType: 'vendor' });
    console.log(`📋 Found ${vendorUsers.length} vendor users`);

    for (const user of vendorUsers) {
      console.log(`\n👤 Vendor User: ${user.email}`);
      console.log(`   - ID: ${user._id}`);
      console.log(`   - Name: ${user.firstName} ${user.lastName}`);
      console.log(`   - Status: ${user.status}`);
      console.log(`   - Approved: ${user.isVendorApproved}`);

      // Check if vendor profile exists
      const vendor = await Vendor.findOne({ user: user._id });
      if (vendor) {
        console.log(`   ✅ Vendor profile exists: ${vendor.businessName}`);
        console.log(`   - Vendor ID: ${vendor._id}`);
        console.log(`   - Status: ${vendor.status}`);
        console.log(`   - Verification: ${vendor.verification?.status}`);
      } else {
        console.log(`   ❌ No vendor profile found`);
        
        // Create a vendor profile for this user
        console.log(`   🔧 Creating vendor profile...`);
        
        const newVendor = new Vendor({
          user: user._id,
          businessName: `${user.firstName}'s Business`,
          businessDescription: 'A new business on our platform',
          businessType: 'individual',
          businessAddress: {
            street: '123 Business Street',
            city: 'Business City',
            state: 'Business State',
            zipCode: '12345',
            country: 'India'
          },
          contactInfo: {
            businessPhone: user.phone || '+*********0',
            businessEmail: user.email,
            website: ''
          },
          bankDetails: {
            accountHolderName: `${user.firstName} ${user.lastName}`,
            bankName: 'Sample Bank',
            accountNumber: '*********0',
            routingNumber: '*********',
            accountType: 'checking'
          },
          settings: {
            autoAcceptOrders: false,
            processingTime: 2,
            currency: 'INR',
            returnPolicy: 'Standard return policy',
            shippingPolicy: 'Standard shipping policy'
          }
        });

        try {
          const savedVendor = await newVendor.save();
          console.log(`   ✅ Vendor profile created: ${savedVendor.businessName}`);
          console.log(`   - New Vendor ID: ${savedVendor._id}`);
        } catch (error) {
          console.log(`   ❌ Failed to create vendor profile:`, error.message);
          if (error.errors) {
            Object.keys(error.errors).forEach(key => {
              console.log(`     - ${key}: ${error.errors[key].message}`);
            });
          }
        }
      }
    }

    // Now test the vendor settings update functionality
    console.log('\n🧪 Testing vendor settings update...');
    
    const testUser = await User.findOne({ userType: 'vendor' });
    if (testUser) {
      const testVendor = await Vendor.findOne({ user: testUser._id });
      if (testVendor) {
        console.log(`\n📋 Testing with vendor: ${testVendor.businessName}`);
        
        // Test profile update
        const profileUpdateData = {
          businessName: 'Updated Test Business',
          businessDescription: 'Updated business description',
          contactInfo: {
            ...testVendor.contactInfo,
            businessPhone: '+9876543210',
            website: 'https://updated-website.com'
          },
          settings: {
            ...testVendor.settings,
            returnPolicy: 'Updated return policy',
            processingTime: 3
          }
        };

        const updatedVendor = await Vendor.findByIdAndUpdate(
          testVendor._id,
          { $set: profileUpdateData },
          { new: true, runValidators: true }
        );

        if (updatedVendor) {
          console.log('✅ Vendor profile update test successful');
          console.log(`   - Business Name: ${updatedVendor.businessName}`);
          console.log(`   - Processing Time: ${updatedVendor.settings?.processingTime}`);
        } else {
          console.log('❌ Vendor profile update test failed');
        }

        // Test user profile update
        const userUpdateData = {
          firstName: 'Updated First',
          lastName: 'Updated Last',
          phone: '+1111111111'
        };

        const updatedUser = await User.findByIdAndUpdate(
          testUser._id,
          { $set: userUpdateData },
          { new: true, runValidators: true }
        );

        if (updatedUser) {
          console.log('✅ User profile update test successful');
          console.log(`   - Name: ${updatedUser.firstName} ${updatedUser.lastName}`);
          console.log(`   - Phone: ${updatedUser.phone}`);
        } else {
          console.log('❌ User profile update test failed');
        }

        // Test image upload simulation
        const imageUpdateData = {
          logo: 'https://res.cloudinary.com/alicartify/image/upload/v*********0/test-logo.jpg',
          banner: 'https://res.cloudinary.com/alicartify/image/upload/v*********0/test-banner.jpg'
        };

        const imageUpdatedVendor = await Vendor.findByIdAndUpdate(
          testVendor._id,
          { $set: imageUpdateData },
          { new: true, runValidators: true }
        );

        if (imageUpdatedVendor) {
          console.log('✅ Image upload simulation test successful');
          console.log(`   - Logo: ${imageUpdatedVendor.logo}`);
          console.log(`   - Banner: ${imageUpdatedVendor.banner}`);
        } else {
          console.log('❌ Image upload simulation test failed');
        }
      }
    }

  } catch (error) {
    console.error('❌ Error during debugging:', error);
    console.error('Error details:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the debug
debugVendorProfile();