#!/usr/bin/env node

/**
 * Debug Script: Vendor Reviews Visibility Issue
 * 
 * This script checks why reviews are not appearing on the vendor's reviews page
 */

const mongoose = require('mongoose');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '.env') });

// Import models
const Review = require('./src/models/Review');
const Product = require('./src/models/Product');
const User = require('./src/models/User');

// Configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce';
const PRODUCT_ID = '6881fd806652576061a0be95';
const CUSTOMER_ID = '68808203c91dee5d57dc1106';
const VENDOR_ID = '6881fd066652576061a0be6e';

/**
 * Connect to MongoDB
 */
async function connectDB() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

/**
 * Debug vendor reviews visibility
 */
async function debugVendorReviews() {
  console.log('\n🔍 Debugging vendor reviews visibility...\n');
  
  try {
    // 1. Check if vendor exists
    const vendor = await User.findById(VENDOR_ID);
    if (!vendor) {
      console.error('❌ Vendor not found');
      return;
    }
    console.log(`✅ Vendor found: ${vendor.businessName} (${vendor.email})`);
    console.log(`   - User Type: ${vendor.userType}`);
    console.log(`   - Vendor ID: ${vendor._id}`);

    // 2. Check if product exists and belongs to vendor
    const product = await Product.findById(PRODUCT_ID).populate('vendor');
    if (!product) {
      console.error('❌ Product not found');
      return;
    }
    console.log(`\n✅ Product found: ${product.name}`);
    console.log(`   - Product Vendor ID: ${product.vendor._id}`);
    console.log(`   - Product Vendor Name: ${product.vendor.businessName}`);
    console.log(`   - Vendor Match: ${product.vendor._id.toString() === VENDOR_ID ? '✅ YES' : '❌ NO'}`);

    // 3. Check all reviews for this product
    const allReviews = await Review.find({ product: PRODUCT_ID })
      .populate('customer', 'firstName lastName email')
      .populate('vendor', 'businessName email')
      .populate('product', 'name');

    console.log(`\n📊 All reviews for product ${PRODUCT_ID}:`);
    console.log(`   - Total reviews found: ${allReviews.length}`);
    
    allReviews.forEach((review, index) => {
      console.log(`\n   Review ${index + 1}:`);
      console.log(`     - Review ID: ${review._id}`);
      console.log(`     - Customer: ${review.customer?.firstName} ${review.customer?.lastName} (${review.customer?.email})`);
      console.log(`     - Customer ID: ${review.customer?._id}`);
      console.log(`     - Vendor ID in Review: ${review.vendor}`);
      console.log(`     - Product Name: ${review.product?.name}`);
      console.log(`     - Rating: ${review.rating}/5`);
      console.log(`     - Comment: "${review.comment}"`);
      console.log(`     - Status: ${review.status}`);
      console.log(`     - Created: ${review.createdAt}`);
      console.log(`     - Vendor Match: ${review.vendor?.toString() === VENDOR_ID ? '✅ YES' : '❌ NO'}`);
    });

    // 4. Test vendor reviews query directly
    console.log(`\n🧪 Testing vendor reviews query for vendor ${VENDOR_ID}:`);
    
    const vendorReviews = await Review.find({ vendor: VENDOR_ID, status: 'active' })
      .populate('customer', 'firstName lastName avatar')
      .populate('product', 'name images')
      .populate('replies')
      .sort({ createdAt: -1 });

    console.log(`   - Reviews found by vendor query: ${vendorReviews.length}`);
    
    vendorReviews.forEach((review, index) => {
      console.log(`\n   Vendor Review ${index + 1}:`);
      console.log(`     - Review ID: ${review._id}`);
      console.log(`     - Customer: ${review.customer?.firstName} ${review.customer?.lastName}`);
      console.log(`     - Product: ${review.product?.name}`);
      console.log(`     - Rating: ${review.rating}/5`);
      console.log(`     - Comment: "${review.comment}"`);
      console.log(`     - Status: ${review.status}`);
      console.log(`     - Replies: ${review.replies?.length || 0}`);
    });

    // 5. Test the getVendorReviews static method
    console.log(`\n🔬 Testing Review.getVendorReviews() method:`);
    
    const vendorReviewsMethod = await Review.getVendorReviews(VENDOR_ID, {
      page: 1,
      limit: 10,
      status: 'active'
    });

    console.log(`   - Reviews found by getVendorReviews method: ${vendorReviewsMethod.length}`);

    // 6. Check vendor reviews statistics
    const totalVendorReviews = await Review.countDocuments({ 
      vendor: VENDOR_ID, 
      status: 'active' 
    });

    console.log(`\n📈 Vendor Reviews Statistics:`);
    console.log(`   - Total active reviews for vendor: ${totalVendorReviews}`);
    console.log(`   - Total reviews (all statuses): ${await Review.countDocuments({ vendor: VENDOR_ID })}`);

    // 7. Check if there are issues with vendor reference
    console.log(`\n🔍 Checking vendor reference consistency:`);
    
    const reviewsWithWrongVendor = await Review.find({ 
      product: PRODUCT_ID,
      $or: [
        { vendor: { $ne: VENDOR_ID } },
        { vendor: null },
        { vendor: { $exists: false } }
      ]
    });

    console.log(`   - Reviews with incorrect/missing vendor: ${reviewsWithWrongVendor.length}`);
    
    if (reviewsWithWrongVendor.length > 0) {
      console.log('   ❌ Found reviews with incorrect vendor assignment:');
      reviewsWithWrongVendor.forEach((review, index) => {
        console.log(`     Review ${index + 1}: vendor = ${review.vendor}, should be = ${VENDOR_ID}`);
      });
    }

  } catch (error) {
    console.error('❌ Error debugging vendor reviews:', error);
  }
}

/**
 * Fix vendor reviews if needed
 */
async function fixVendorReviews() {
  console.log('\n🔧 Checking for vendor review fixes needed...');
  
  try {
    // Find reviews for the product that don't have correct vendor assignment
    const product = await Product.findById(PRODUCT_ID);
    if (!product) {
      console.log('❌ Product not found for fixing');
      return;
    }

    const correctVendorId = product.vendor;
    console.log(`   - Correct vendor ID should be: ${correctVendorId}`);

    const reviewsToFix = await Review.find({ 
      product: PRODUCT_ID,
      $or: [
        { vendor: { $ne: correctVendorId } },
        { vendor: null },
        { vendor: { $exists: false } }
      ]
    });

    if (reviewsToFix.length > 0) {
      console.log(`   - Found ${reviewsToFix.length} reviews to fix`);
      
      const readline = require('readline').createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      const answer = await new Promise(resolve => {
        readline.question('   ❓ Do you want to fix the vendor assignments? (y/N): ', resolve);
      });
      readline.close();
      
      if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
        const result = await Review.updateMany(
          { 
            product: PRODUCT_ID,
            $or: [
              { vendor: { $ne: correctVendorId } },
              { vendor: null },
              { vendor: { $exists: false } }
            ]
          },
          { 
            $set: { vendor: correctVendorId }
          }
        );
        
        console.log(`   ✅ Fixed ${result.modifiedCount} reviews`);
      }
    } else {
      console.log('   ✅ No reviews need fixing');
    }

  } catch (error) {
    console.error('❌ Error fixing vendor reviews:', error);
  }
}

/**
 * Test vendor reviews API endpoint
 */
async function testVendorReviewsAPI() {
  console.log('\n🌐 Testing vendor reviews API simulation...');
  
  try {
    // Simulate the API call that the frontend makes
    const mockUser = { _id: VENDOR_ID, userId: VENDOR_ID };
    const mockReq = {
      user: mockUser,
      query: { page: 1, limit: 10, status: 'active' }
    };

    console.log('   - Simulating API request with user:', mockUser);
    
    // Check if user is a vendor
    const vendor = await User.findById(VENDOR_ID);
    if (!vendor || vendor.userType !== 'vendor') {
      console.log('   ❌ User is not a vendor or not found');
      return;
    }
    
    console.log('   ✅ User is a valid vendor');

    // Get reviews using the same logic as the controller    
    const reviews = await Review.getVendorReviews(VENDOR_ID, {
      page: parseInt(mockReq.query.page),
      limit: parseInt(mockReq.query.limit),
      status: mockReq.query.status
    });

    const totalReviews = await Review.countDocuments({ 
      vendor: VENDOR_ID, 
      status: mockReq.query.status
    });

    console.log(`   - API would return ${reviews.length} reviews`);
    console.log(`   - Total reviews count: ${totalReviews}`);
    
    const mockResponse = {
      success: true,
      data: {
        reviews,
        pagination: {
          currentPage: parseInt(mockReq.query.page),
          totalPages: Math.ceil(totalReviews / mockReq.query.limit),
          totalReviews,
          hasNextPage: mockReq.query.page * mockReq.query.limit < totalReviews,
          hasPrevPage: mockReq.query.page > 1
        }
      }
    };

    console.log('   - Mock API Response:', JSON.stringify(mockResponse, null, 2));

  } catch (error) {
    console.error('❌ Error testing vendor reviews API:', error);
  }
}

/**
 * Main function
 */
async function main() {
  console.log('🔧 Vendor Reviews Debug Script Starting...\n');
  
  try {
    await connectDB();
    await debugVendorReviews();
    await fixVendorReviews();
    await testVendorReviewsAPI();
    
    console.log('\n✅ Debug script completed successfully!');
  } catch (error) {
    console.error('\n❌ Debug script failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main };
