import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic,
  Upload,
  InputNumber,
  Switch,
  Image,
  Spin
} from 'antd';
import {
  ShoppingOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  SearchOutlined,
  UploadOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  StockOutlined,
  SendOutlined,
  ClockCircleOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { productsApi, categoriesApi } from '../../../services/vendorApi';
import MultiCurrencyPriceInput from '../../common/MultiCurrencyPriceInput';
import ColorSelector from '../../common/ColorSelector';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const ProductsManagement = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [stockModalVisible, setStockModalVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  
  // Categories state - will be fetched from database
  const [categories, setCategories] = useState([]);
  
  const [stats, setStats] = useState({});
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [fileList, setFileList] = useState([]);
  const [form] = Form.useForm();
  const [stockForm] = Form.useForm();

  useEffect(() => {
    fetchProducts();
    fetchStats();
    fetchCategories();
  }, []);

  useEffect(() => {
    fetchProducts();
  }, [searchText, statusFilter, pagination.current, pagination.pageSize]);

  const fetchProducts = async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        search: searchText || undefined,
        status: statusFilter || undefined
      };

      const response = await productsApi.getProducts(params);
      if (response.data.success) {
        setProducts(response.data.data.products);
        setPagination(prev => ({
          ...prev,
          total: response.data.data.pagination.totalProducts
        }));
      }
    } catch (error) {
      message.error('Failed to fetch products');
      console.error('Fetch products error:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await productsApi.getStats();
      if (response.data.success) {
        setStats(response.data.data.stats);
      }
    } catch (error) {
      console.error('Fetch stats error:', error);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await categoriesApi.getCategories();
      if (response.data.success) {
        setCategories(response.data.data.categories);
      }
    } catch (error) {
      console.error('Fetch categories error:', error);
      message.error('Failed to fetch categories');
      // Set fallback categories if API fails
      setCategories([
        { _id: 'fallback-1', name: 'Electronics' },
        { _id: 'fallback-2', name: 'Clothing & Fashion' },
        { _id: 'fallback-3', name: 'Home & Garden' },
        { _id: 'fallback-4', name: 'Sports & Outdoors' },
        { _id: 'fallback-5', name: 'Books & Media' },
        { _id: 'fallback-6', name: 'Health & Beauty' }
      ]);
    }
  };

  const handleAddProduct = () => {
    setEditingProduct(null);
    form.resetFields();
    setFileList([]);
    setModalVisible(true);
  };

  const handleEditProduct = (product) => {
    setEditingProduct(product);
    // Prepare multi-currency pricing data for editing
    const pricingData = {
      basePrice: product.pricing?.basePrice,
      salePrice: product.pricing?.salePrice,
      currency: product.pricing?.currency || 'INR',
      multiCurrency: product.pricing?.multiCurrency || {}
    };

    form.setFieldsValue({
      name: product.name,
      sku: product.sku,
      description: product.description,
      shortDescription: product.shortDescription,
      category: product.category?._id,
      brand: product.brand,
      pricing: pricingData,
      quantity: product.inventory?.quantity,
      status: product.status,
      visibility: product.visibility,
      featured: product.featured
    });
    
    // Set existing images
    if (product.images && product.images.length > 0) {
      const existingFiles = product.images.map((img, index) => ({
        uid: `-${index}`,
        name: `image-${index}`,
        status: 'done',
        url: img.url,
        thumbUrl: img.url
      }));
      setFileList(existingFiles);
    }
    
    setModalVisible(true);
  };

  const handleDeleteProduct = async (productId) => {
    try {
      await productsApi.deleteProduct(productId);
      message.success('Product deleted successfully');
      fetchProducts();
      fetchStats();
    } catch (error) {
      message.error('Failed to delete product');
      console.error('Delete product error:', error);
    }
  };

  const handleToggleStatus = async (productId, currentStatus) => {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    try {
      const formData = new FormData();
      formData.append('status', newStatus);
      
      await productsApi.updateProduct(productId, formData);
      message.success(`Product ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`);
      fetchProducts();
      fetchStats();
    } catch (error) {
      message.error('Failed to update product status');
      console.error('Toggle status error:', error);
    }
  };

  const handleStockUpdate = (product) => {
    setSelectedProduct(product);
    stockForm.setFieldsValue({
      quantity: product.inventory?.quantity || 0
    });
    setStockModalVisible(true);
  };

  const handleStockModalOk = async () => {
    try {
      const values = await stockForm.validateFields();
      await productsApi.updateStock(selectedProduct._id, values.quantity, 'set');
      message.success('Stock updated successfully');
      setStockModalVisible(false);
      fetchProducts();
      fetchStats();
    } catch (error) {
      message.error('Failed to update stock');
      console.error('Update stock error:', error);
    }
  };

  const handleSubmitForApproval = async (productId) => {
    try {
      await productsApi.submitForApproval(productId);
      message.success('Product submitted for approval successfully');
      fetchProducts();
      fetchStats();
    } catch (error) {
      message.error(error.response?.data?.message || 'Failed to submit product for approval');
      console.error('Submit for approval error:', error);
    }
  };

  const handleViewApprovalHistory = async (productId) => {
    try {
      const response = await productsApi.getApprovalHistory(productId);
      if (response.data.success) {
        Modal.info({
          title: 'Approval History',
          width: 600,
          content: (
            <div>
              <div style={{ marginBottom: '16px' }}>
                <strong>Current Status:</strong>
                <Tag color={getApprovalStatusColor(response.data.data.approval.status)} style={{ marginLeft: '8px' }}>
                  {response.data.data.approval.status?.replace('_', ' ')}
                </Tag>
              </div>

              {response.data.data.approval.rejectionReason && (
                <div style={{ marginBottom: '16px' }}>
                  <strong>Rejection Reason:</strong>
                  <div style={{ marginTop: '4px', padding: '8px', backgroundColor: '#fff2f0', borderRadius: '4px' }}>
                    {response.data.data.approval.rejectionReason}
                  </div>
                </div>
              )}

              {response.data.data.approval.adminNotes && (
                <div style={{ marginBottom: '16px' }}>
                  <strong>Admin Notes:</strong>
                  <div style={{ marginTop: '4px', padding: '8px', backgroundColor: '#f6ffed', borderRadius: '4px' }}>
                    {response.data.data.approval.adminNotes}
                  </div>
                </div>
              )}

              {response.data.data.approval.changesRequested && response.data.data.approval.changesRequested.length > 0 && (
                <div style={{ marginBottom: '16px' }}>
                  <strong>Changes Requested:</strong>
                  <div style={{ marginTop: '8px' }}>
                    {response.data.data.approval.changesRequested.map((change, index) => (
                      <div key={index} style={{
                        marginBottom: '8px',
                        padding: '8px',
                        backgroundColor: change.resolved ? '#f6ffed' : '#fffbe6',
                        borderRadius: '4px',
                        borderLeft: `3px solid ${change.resolved ? '#52c41a' : '#faad14'}`
                      }}>
                        <div style={{ fontWeight: 500 }}>{change.field}</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>{change.message}</div>
                        <Tag color={change.resolved ? 'success' : 'warning'} size="small" style={{ marginTop: '4px' }}>
                          {change.resolved ? 'Resolved' : 'Pending'}
                        </Tag>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div>
                <strong>History:</strong>
                <div style={{ marginTop: '8px' }}>
                  {response.data.data.approval.history.map((entry, index) => (
                    <div key={index} style={{
                      marginBottom: '8px',
                      padding: '8px',
                      backgroundColor: '#fafafa',
                      borderRadius: '4px'
                    }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Tag color="blue">{entry.action?.replace('_', ' ')}</Tag>
                        <span style={{ fontSize: '12px', color: '#666' }}>
                          {new Date(entry.timestamp).toLocaleString()}
                        </span>
                      </div>
                      {entry.reason && (
                        <div style={{ fontSize: '12px', marginTop: '4px' }}>
                          Reason: {entry.reason}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ),
        });
      }
    } catch (error) {
      message.error('Failed to fetch approval history');
      console.error('Get approval history error:', error);
    }
  };

  const getApprovalStatusColor = (status) => {
    const colors = {
      pending: 'processing',
      approved: 'success',
      rejected: 'error',
      requires_changes: 'warning'
    };
    return colors[status] || 'default';
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      // Check if we have any images to upload
      const hasImages = fileList.some(file => file.originFileObj);
      
      if (editingProduct) {
        // For updates, always use FormData to support image uploads
        const formData = new FormData();
        
        // Add form fields
        Object.keys(values).forEach(key => {
          if (values[key] !== undefined && values[key] !== null) {
            if (key === 'pricing') {
              // Handle multi-currency pricing
              if (values.pricing.multiCurrency) {
                Object.keys(values.pricing.multiCurrency).forEach(currency => {
                  const currencyPricing = values.pricing.multiCurrency[currency];
                  if (currencyPricing.basePrice) {
                    formData.append(`pricing[multiCurrency][${currency}][basePrice]`, currencyPricing.basePrice);
                  }
                  if (currencyPricing.salePrice) {
                    formData.append(`pricing[multiCurrency][${currency}][salePrice]`, currencyPricing.salePrice);
                  }
                });
              }
              // Also set default pricing for backward compatibility
              if (values.pricing.basePrice) {
                formData.append('pricing[basePrice]', values.pricing.basePrice);
              }
              if (values.pricing.salePrice) {
                formData.append('pricing[salePrice]', values.pricing.salePrice);
              }
              formData.append('pricing[currency]', values.pricing.currency || 'INR');
            } else if (key === 'quantity') {
              formData.append('inventory[quantity]', values[key]);
              formData.append('inventory[trackQuantity]', true);
            } else {
              formData.append(key, values[key]);
            }
          }
        });

        // Add images
        fileList.forEach(file => {
          if (file.originFileObj) {
            formData.append('productImages', file.originFileObj);
          }
        });

        await productsApi.updateProduct(editingProduct._id, formData);
        message.success('Product updated successfully');
      } else {
        // For new products, use simplified API if no images, otherwise use full API
        if (!hasImages) {
          // Use simplified API for products without images
          const productData = {
            name: values.name,
            description: values.description,
            shortDescription: values.shortDescription,
            category: values.category,
            brand: values.brand,
            // Multi-currency pricing
            pricing: {
              basePrice: values.pricing?.basePrice,
              salePrice: values.pricing?.salePrice,
              currency: values.pricing?.currency || 'INR',
              multiCurrency: values.pricing?.multiCurrency || {}
            },
            quantity: values.quantity,
            status: values.status || 'active',
            visibility: values.visibility || 'public',
            featured: values.featured || false,
            sku: values.sku // Optional - will be auto-generated if not provided
          };
          
          await productsApi.createSimpleProduct(productData);
          message.success('Product created successfully');
        } else {
          // Use full API with FormData for products with images
          const formData = new FormData();
          
          // Add form fields
          Object.keys(values).forEach(key => {
            if (values[key] !== undefined && values[key] !== null) {
              if (key === 'pricing') {
                // Handle multi-currency pricing
                if (values.pricing.multiCurrency) {
                  Object.keys(values.pricing.multiCurrency).forEach(currency => {
                    const currencyPricing = values.pricing.multiCurrency[currency];
                    if (currencyPricing.basePrice) {
                      formData.append(`pricing[multiCurrency][${currency}][basePrice]`, currencyPricing.basePrice);
                    }
                    if (currencyPricing.salePrice) {
                      formData.append(`pricing[multiCurrency][${currency}][salePrice]`, currencyPricing.salePrice);
                    }
                  });
                }
                // Also set default pricing for backward compatibility
                if (values.pricing.basePrice) {
                  formData.append('pricing[basePrice]', values.pricing.basePrice);
                }
                if (values.pricing.salePrice) {
                  formData.append('pricing[salePrice]', values.pricing.salePrice);
                }
                formData.append('pricing[currency]', values.pricing.currency || 'INR');
              } else if (key === 'quantity') {
                formData.append('inventory[quantity]', values[key]);
                formData.append('inventory[trackQuantity]', true);
              } else {
                formData.append(key, values[key]);
              }
            }
          });

          // Add images
          fileList.forEach(file => {
            if (file.originFileObj) {
              formData.append('productImages', file.originFileObj);
            }
          });

          await productsApi.createProduct(formData);
          message.success('Product created successfully');
        }
      }
      
      setModalVisible(false);
      form.resetFields();
      setFileList([]);
      fetchProducts();
      fetchStats();
    } catch (error) {
      message.error(error.response?.data?.message || 'Failed to save product');
      console.error('Save product error:', error);
    }
  };

  const handleUploadChange = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };

  const beforeUpload = (file) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('You can only upload image files!');
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('Image must be smaller than 2MB!');
      return false;
    }
    return false; // Prevent auto upload
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'inactive':
        return 'red';
      case 'draft':
        return 'orange';
      case 'archived':
        return 'gray';
      default:
        return 'default';
    }
  };

  const getStockStatus = (stock) => {
    if (stock === 0) return { color: 'red', text: 'Out of Stock' };
    if (stock < 10) return { color: 'orange', text: 'Low Stock' };
    return { color: 'green', text: 'In Stock' };
  };

  const columns = [
    {
      title: 'Product',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <Image
            width={50}
            height={50}
            src={record.images?.[0]?.url || '/placeholder.jpg'}
            style={{ borderRadius: 4 }}
            fallback="/placeholder.jpg"
          />
          <div>
            <div style={{ fontWeight: 500 }}>{text}</div>
            <div style={{ color: '#666', fontSize: '12px' }}>SKU: {record.sku}</div>
          </div>
        </Space>
      ),
    },
    {
      title: 'Category',
      dataIndex: ['category', 'name'],
      key: 'category',
      render: (text) => text || 'Uncategorized',
    },
    {
      title: 'Price',
      key: 'price',
      render: (_, record) => {
        // Show INR price primarily, with indicator if multi-currency is available
        const basePriceINR = record.pricing?.multiCurrency?.INR?.basePrice || record.pricing?.basePrice || 0;
        const salePriceINR = record.pricing?.multiCurrency?.INR?.salePrice || record.pricing?.salePrice;
        const hasManycurrencies = record.pricing?.multiCurrency && Object.keys(record.pricing.multiCurrency).length > 1;
        
        return (
          <div>
            <div>₹{basePriceINR.toFixed(2)}</div>
            {salePriceINR && (
              <div style={{ color: '#f5222d', fontSize: '12px' }}>
                Sale: ₹{salePriceINR.toFixed(2)}
              </div>
            )}
            {hasManycurrencies && (
              <Tag color="blue" size="small" style={{ marginTop: '2px' }}>
                Multi-Currency
              </Tag>
            )}
          </div>
        );
      },
      sorter: (a, b) => (a.pricing?.basePrice || 0) - (b.pricing?.basePrice || 0),
    },
    {
      title: 'Stock',
      key: 'stock',
      render: (_, record) => {
        const stock = record.inventory?.quantity || 0;
        const stockStatus = getStockStatus(stock);
        return (
          <div>
            <div>{stock}</div>
            <Tag color={stockStatus.color} style={{ fontSize: '10px' }}>
              {stockStatus.text}
            </Tag>
          </div>
        );
      },
      sorter: (a, b) => (a.inventory?.quantity || 0) - (b.inventory?.quantity || 0),
    },
    {
      title: 'Sales',
      key: 'sales',
      align: 'center',
      render: (_, record) => (
        <div>
          <div>{record.sales?.totalSold || 0}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            ${(record.sales?.totalRevenue || 0).toFixed(2)}
          </div>
        </div>
      ),
      sorter: (a, b) => (a.sales?.totalSold || 0) - (b.sales?.totalSold || 0),
    },
    {
      title: 'Rating',
      key: 'rating',
      render: (_, record) => {
        const rating = record.reviews?.averageRating || 0;
        const totalReviews = record.reviews?.totalReviews || 0;
        return rating > 0 ? (
          <div>
            <div>⭐ {rating.toFixed(1)}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              ({totalReviews} reviews)
            </div>
          </div>
        ) : 'No ratings';
      },
      sorter: (a, b) => (a.reviews?.averageRating || 0) - (b.reviews?.averageRating || 0),
    },
    {
      title: 'Status',
      key: 'status',
      render: (_, record) => (
        <div>
          <Tag color={getStatusColor(record.status)}>
            {record.status?.replace('_', ' ').toUpperCase()}
          </Tag>
          {record.approval?.status && (
            <div style={{ marginTop: '4px' }}>
              <Tag color={getApprovalStatusColor(record.approval.status)} size="small">
                {record.approval.status?.replace('_', ' ')}
              </Tag>
            </div>
          )}
          {record.status === 'pending_approval' && (
            <div style={{ marginTop: '4px' }}>
              <Tag color="processing" size="small" icon={<ClockCircleOutlined />}>
                Awaiting Review
              </Tag>
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Space>
            <Button
              type="primary"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditProduct(record)}
            >
              Edit
            </Button>
            <Button
              size="small"
              icon={<StockOutlined />}
              onClick={() => handleStockUpdate(record)}
            >
              Stock
            </Button>
          </Space>
          <Space>
            {record.status === 'draft' && (
              <Button
                type="primary"
                size="small"
                icon={<SendOutlined />}
                onClick={() => handleSubmitForApproval(record._id)}
                style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
              >
                Submit for Approval
              </Button>
            )}

            {(record.approval?.status === 'pending' || record.approval?.status === 'rejected' || record.approval?.status === 'requires_changes') && (
              <Button
                size="small"
                icon={<FileTextOutlined />}
                onClick={() => handleViewApprovalHistory(record._id)}
              >
                View History
              </Button>
            )}

            <Button
              size="small"
              icon={record.status === 'active' ? <CloseCircleOutlined /> : <CheckCircleOutlined />}
              onClick={() => handleToggleStatus(record._id, record.status)}
              style={{
                color: record.status === 'active' ? '#ff4d4f' : '#52c41a',
                borderColor: record.status === 'active' ? '#ff4d4f' : '#52c41a'
              }}
            >
              {record.status === 'active' ? 'Deactivate' : 'Activate'}
            </Button>
          </Space>

          <Space>
            <Popconfirm
              title="Are you sure you want to delete this product?"
              onConfirm={() => handleDeleteProduct(record._id)}
              okText="Yes"
              cancelText="No"
            >
              <Button
                type="primary"
                danger
                size="small"
                icon={<DeleteOutlined />}
              >
                Delete
              </Button>
            </Popconfirm>
          </Space>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>Products Management</Title>
      
      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Products"
              value={stats.totalProducts || 0}
              prefix={<ShoppingOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Products"
              value={stats.activeProducts || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Low Stock"
              value={stats.lowStockProducts || 0}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Revenue"
              value={stats.totalRevenue || 0}
              prefix="$"
              precision={2}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', gap: 16 }}>
          <Space>
            <Input
              placeholder="Search products..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 250 }}
            />
            <Select
              placeholder="Filter by status"
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: 150 }}
              allowClear
            >
              <Option value="active">Active</Option>
              <Option value="draft">Draft</Option>
              <Option value="inactive">Inactive</Option>
              <Option value="archived">Archived</Option>
            </Select>
          </Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddProduct}
          >
            Add Product
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={products}
          rowKey="_id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} products`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({
                ...prev,
                current: page,
                pageSize: pageSize
              }));
            }
          }}
        />
      </Card>

      {/* Add/Edit Product Modal */}
      <Modal
        title={editingProduct ? 'Edit Product' : 'Add New Product'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        width={800}
        confirmLoading={loading}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'active',
            visibility: 'public',
            featured: false
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="Product Name"
                rules={[
                  { required: true, message: 'Please enter product name' },
                  { min: 2, max: 200, message: 'Product name must be between 2 and 200 characters' }
                ]}
              >
                <Input placeholder="Enter product name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="sku"
                label="SKU"
                rules={[
                  { required: true, message: 'Please enter SKU' },
                  { min: 3, max: 50, message: 'SKU must be between 3 and 50 characters' },
                  { pattern: /^[A-Z0-9\-_]+$/, message: 'SKU can only contain uppercase letters, numbers, hyphens, and underscores' }
                ]}
              >
                <Input 
                  placeholder="Enter product SKU (e.g., PROD-001)" 
                  style={{ textTransform: 'uppercase' }}
                  onChange={(e) => {
                    // Auto-convert to uppercase and filter invalid characters
                    const value = e.target.value.toUpperCase().replace(/[^A-Z0-9\-_]/g, '');
                    form.setFieldsValue({ sku: value });
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="category"
                label="Category (Optional)"
              >
                <Select placeholder="Select category (optional)" showSearch allowClear>
                  {categories.map(category => (
                    <Option key={category._id} value={category._id}>
                      {category.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="brand"
                label="Brand"
              >
                <Input placeholder="Enter brand name" />
              </Form.Item>
            </Col>
          </Row>

          {/* Multi-Currency Pricing Section */}
          <Form.Item
            name="pricing"
            label="Product Pricing"
            rules={[
              {
                validator: (_, value) => {
                  if (!value || !value.basePrice) {
                    return Promise.reject(new Error('Base price in INR is required'));
                  }
                  return Promise.resolve();
                }
              }
            ]}
          >
            <MultiCurrencyPriceInput
              required={true}
              showAdvanced={true}
              disabled={loading}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="quantity"
                label="Stock Quantity"
                rules={[{ required: true, message: 'Please enter stock quantity' }]}
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="Enter stock quantity"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="Status"
                rules={[{ required: true, message: 'Please select status' }]}
              >
                <Select placeholder="Select status">
                  <Option value="draft">Draft</Option>
                  <Option value="active">Active</Option>
                  <Option value="inactive">Inactive</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="Description"
            rules={[
              { required: true, message: 'Please enter description' },
              { min: 10, max: 2000, message: 'Description must be between 10 and 2000 characters' }
            ]}
          >
            <TextArea 
              rows={3} 
              placeholder="Enter product description (minimum 10 characters)" 
              showCount
              maxLength={2000}
            />
          </Form.Item>

          <Form.Item
            name="shortDescription"
            label="Short Description"
          >
            <TextArea rows={2} placeholder="Enter short description (optional)" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="visibility"
                label="Visibility"
              >
                <Select>
                  <Option value="public">Public</Option>
                  <Option value="private">Private</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="featured"
                valuePropName="checked"
              >
                <Switch checkedChildren="Featured" unCheckedChildren="Regular" />
              </Form.Item>
            </Col>
          </Row>

          {/* Colors Section */}
          <Form.Item
            name="colors"
            label="Available Colors"
          >
            <ColorSelector />
          </Form.Item>

          {/* Shipping Options Section */}
          <Card title="Shipping Information" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name={['shippingOptions', 'processingDays']}
                  label="Processing Days"
                  rules={[{ required: true, message: 'Please enter processing days' }]}
                  initialValue={1}
                >
                  <InputNumber
                    min={0}
                    max={30}
                    style={{ width: '100%' }}
                    placeholder="Days to process order"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['shippingOptions', 'shippingDays']}
                  label="Shipping Days"
                  rules={[{ required: true, message: 'Please enter shipping days' }]}
                  initialValue={3}
                >
                  <InputNumber
                    min={1}
                    max={30}
                    style={{ width: '100%' }}
                    placeholder="Days to ship"
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name={['shippingOptions', 'expeditedShipping', 'available']}
                  valuePropName="checked"
                >
                  <Switch checkedChildren="Expedited Available" unCheckedChildren="Standard Only" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['shippingOptions', 'freeShippingThreshold']}
                  label="Free Shipping Threshold"
                  initialValue={0}
                >
                  <InputNumber
                    min={0}
                    style={{ width: '100%' }}
                    placeholder="Minimum order for free shipping"
                    prefix="₹"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* Return Policy Section */}
          <Card title="Return Policy" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name={['returnPolicy', 'returnsAccepted']}
                  valuePropName="checked"
                  initialValue={true}
                >
                  <Switch checkedChildren="Returns Accepted" unCheckedChildren="No Returns" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['returnPolicy', 'returnWindow']}
                  label="Return Window (Days)"
                  initialValue={30}
                >
                  <InputNumber
                    min={0}
                    max={365}
                    style={{ width: '100%' }}
                    placeholder="Days to return"
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name={['returnPolicy', 'returnShippingCost']}
                  label="Return Shipping Cost"
                  initialValue="buyer_pays"
                >
                  <Select>
                    <Option value="buyer_pays">Buyer Pays</Option>
                    <Option value="seller_pays">Seller Pays</Option>
                    <Option value="shared">Shared</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['returnPolicy', 'restockingFee']}
                  label="Restocking Fee (%)"
                  initialValue={0}
                >
                  <InputNumber
                    min={0}
                    max={100}
                    style={{ width: '100%' }}
                    placeholder="Restocking fee percentage"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* Warranty Section */}
          <Card title="Warranty Information" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name={['warranty', 'hasWarranty']}
                  valuePropName="checked"
                  initialValue={false}
                >
                  <Switch checkedChildren="Has Warranty" unCheckedChildren="No Warranty" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['warranty', 'warrantyPeriod']}
                  label="Warranty Period (Months)"
                >
                  <InputNumber
                    min={0}
                    max={120}
                    style={{ width: '100%' }}
                    placeholder="Warranty period in months"
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name={['warranty', 'warrantyType']}
                  label="Warranty Type"
                >
                  <Select placeholder="Select warranty type">
                    <Option value="manufacturer">Manufacturer</Option>
                    <Option value="seller">Seller</Option>
                    <Option value="extended">Extended</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name={['warranty', 'warrantyContact', 'email']}
                  label="Warranty Contact Email"
                >
                  <Input placeholder="<EMAIL>" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name={['warranty', 'warrantyTerms']}
              label="Warranty Terms"
            >
              <TextArea
                rows={2}
                placeholder="Describe warranty terms and conditions"
                maxLength={1000}
                showCount
              />
            </Form.Item>
          </Card>

          <Form.Item
            label="Product Images"
          >
            <Upload
              listType="picture-card"
              fileList={fileList}
              onChange={handleUploadChange}
              beforeUpload={beforeUpload}
              maxCount={8}
            >
              {fileList.length >= 8 ? null : (
                <div>
                  <UploadOutlined />
                  <div style={{ marginTop: 8 }}>Upload</div>
                </div>
              )}
            </Upload>
          </Form.Item>
        </Form>
      </Modal>

      {/* Stock Update Modal */}
      <Modal
        title="Update Stock"
        open={stockModalVisible}
        onOk={handleStockModalOk}
        onCancel={() => setStockModalVisible(false)}
        width={400}
      >
        <Form form={stockForm} layout="vertical">
          <Form.Item label="Product">
            <Input value={selectedProduct?.name} disabled />
          </Form.Item>
          <Form.Item label="Current Stock">
            <Input value={selectedProduct?.inventory?.quantity || 0} disabled />
          </Form.Item>
          <Form.Item
            name="quantity"
            label="New Quantity"
            rules={[{ required: true, message: 'Please enter quantity' }]}
          >
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              placeholder="Enter new quantity"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ProductsManagement;