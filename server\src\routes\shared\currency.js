const express = require('express');
const router = express.Router();
const { authenticate, authorize } = require('../../middleware/auth');
const {
  getSupportedCurrenciesList,
  updateUserCurrency,
  getUserCurrency
} = require('../../controllers/shared/currencyController');

// Public routes
router.get('/supported', getSupportedCurrenciesList);

// Protected routes (require authentication)
router.use(authenticate);

// Get user's current currency preference
router.get('/user-preference', getUserCurrency);

// Update user's currency preference
router.put('/user-preference', updateUserCurrency);

module.exports = router;
