/**
 * VENDOR DASHBOARD DEBUG UTILITY
 * 
 * Copy and paste this entire code into your browser console while on the vendor dashboard page.
 * This will help diagnose why the loading spinner is showing continuously.
 * 
 * Usage:
 * 1. Open browser Developer Tools (F12)
 * 2. Go to Console tab
 * 3. Copy and paste this entire script
 * 4. Press Enter to run
 */

(function() {
  console.log('🔍 VENDOR DASHBOARD DEBUG UTILITY STARTED');
  console.log('='.repeat(50));

  // Check 1: Authentication Status
  function checkAuthentication() {
    console.log('\n1. 🔐 CHECKING AUTHENTICATION...');
    
    const authToken = localStorage.getItem('authToken') || localStorage.getItem('token');
    const authUser = localStorage.getItem('authUser') || localStorage.getItem('user');
    const authUserType = localStorage.getItem('authUserType') || localStorage.getItem('userType');
    
    console.log('   Auth Token:', authToken ? '✅ Present' : '❌ Missing');
    console.log('   Auth User:', authUser ? '✅ Present' : '❌ Missing');
    console.log('   User Type:', authUserType || 'Not set');
    
    if (authUser) {
      try {
        const user = JSON.parse(authUser);
        console.log('   User Details:', {
          email: user.email,
          userType: user.userType,
          firstName: user.firstName,
          lastName: user.lastName
        });
        
        if (user.userType !== 'vendor') {
          console.log('   ⚠️  WARNING: User type is not "vendor"');
        }
      } catch (error) {
        console.log('   ❌ ERROR: Cannot parse user data');
      }
    }
  }

  // Check 2: Environment Configuration
  function checkEnvironment() {
    console.log('\n2. 🌐 CHECKING ENVIRONMENT...');
    
    const currentUrl = window.location.href;
    console.log('   Current URL:', currentUrl);
    
    // Try to detect the API URL being used
    const scripts = document.querySelectorAll('script');
    let viteEnvFound = false;
    
    scripts.forEach(script => {
      if (script.src && script.src.includes('vite')) {
        viteEnvFound = true;
      }
    });
    
    console.log('   Using Vite:', viteEnvFound ? '✅ Yes' : '❌ No');
    
    // Check if we can access import.meta.env (only works in modules)
    try {
      if (typeof import !== 'undefined' && import.meta && import.meta.env) {
        console.log('   API URL:', import.meta.env.VITE_API_URL || 'Not set');
      } else {
        console.log('   API URL: Cannot access from console (using default)');
      }
    } catch (error) {
      console.log('   API URL: Cannot access import.meta.env');
    }
  }

  // Check 3: Network Requests
  function checkNetworkRequests() {
    console.log('\n3. 🌐 TESTING NETWORK REQUESTS...');
    
    const API_BASE_URL = 'https://multi-vendor-server-1tb9.onrender.com/api';
    const authToken = localStorage.getItem('authToken') || localStorage.getItem('token');
    
    if (!authToken) {
      console.log('   ❌ Cannot test authenticated endpoints - no token found');
      return;
    }
    
    const headers = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    };
    
    // Test dashboard stats endpoint
    console.log('   Testing dashboard stats endpoint...');
    
    fetch(`${API_BASE_URL}/vendor/dashboard/stats`, {
      method: 'GET',
      headers: headers,
      timeout: 10000
    })
    .then(response => {
      console.log(`   Dashboard Stats Response: ${response.status} ${response.statusText}`);
      if (response.ok) {
        return response.json();
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    })
    .then(data => {
      console.log('   ✅ Dashboard stats data received:');
      console.log('      Products:', data.data?.products?.totalProducts || 0);
      console.log('      Orders:', data.data?.orders?.totalOrders || 0);
      console.log('      Revenue:', data.data?.orders?.totalRevenue || 0);
    })
    .catch(error => {
      console.log(`   ❌ Dashboard stats error: ${error.message}`);
      
      if (error.message.includes('401')) {
        console.log('   💡 Suggestion: Token might be expired or invalid. Try logging in again.');
      } else if (error.message.includes('404')) {
        console.log('   💡 Suggestion: Vendor record might not exist in database.');
      } else if (error.message.includes('500')) {
        console.log('   💡 Suggestion: Server error. Check backend logs.');
      } else if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
        console.log('   💡 Suggestion: Cannot connect to server. Check internet connection.');
      }
    });
    
    // Test analytics endpoint
    console.log('   Testing analytics endpoint...');
    
    fetch(`${API_BASE_URL}/vendor/analytics?period=30d&type=revenue`, {
      method: 'GET',
      headers: headers,
      timeout: 10000
    })
    .then(response => {
      console.log(`   Analytics Response: ${response.status} ${response.statusText}`);
      if (response.ok) {
        return response.json();
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    })
    .then(data => {
      console.log('   ✅ Analytics data received:');
      console.log('      Data points:', data.data?.analytics?.length || 0);
      console.log('      Period:', data.data?.period || 'unknown');
    })
    .catch(error => {
      console.log(`   ❌ Analytics error: ${error.message}`);
    });
  }

  // Check 4: Component State
  function checkComponentState() {
    console.log('\n4. 🧩 CHECKING COMPONENT STATE...');
    
    // Look for loading spinners
    const spinners = document.querySelectorAll('.ant-spin, [class*="spin"], [class*="loading"]');
    console.log('   Loading spinners found:', spinners.length);
    
    spinners.forEach((spinner, index) => {
      console.log(`   Spinner ${index + 1}:`, {
        visible: spinner.offsetParent !== null,
        className: spinner.className,
        innerHTML: spinner.innerHTML.substring(0, 100) + '...'
      });
    });
    
    // Look for error messages
    const errorElements = document.querySelectorAll('[class*="error"], .ant-alert-error, .ant-alert-warning');
    console.log('   Error elements found:', errorElements.length);
    
    errorElements.forEach((error, index) => {
      console.log(`   Error ${index + 1}:`, {
        visible: error.offsetParent !== null,
        text: error.textContent?.substring(0, 100) + '...'
      });
    });
  }

  // Check 5: JavaScript Errors
  function checkJavaScriptErrors() {
    console.log('\n5. 🐛 MONITORING JAVASCRIPT ERRORS...');
    
    // Set up error listener
    const originalError = window.onerror;
    const originalUnhandledRejection = window.onunhandledrejection;
    
    let errorCount = 0;
    
    window.onerror = function(message, source, lineno, colno, error) {
      errorCount++;
      console.log(`   ❌ JavaScript Error ${errorCount}:`, {
        message,
        source,
        line: lineno,
        column: colno,
        error: error?.stack || error
      });
      
      if (originalError) {
        return originalError.apply(this, arguments);
      }
    };
    
    window.onunhandledrejection = function(event) {
      errorCount++;
      console.log(`   ❌ Unhandled Promise Rejection ${errorCount}:`, {
        reason: event.reason,
        promise: event.promise
      });
      
      if (originalUnhandledRejection) {
        return originalUnhandledRejection.apply(this, arguments);
      }
    };
    
    console.log('   Error monitoring enabled. Errors will be logged here.');
    
    // Restore original handlers after 30 seconds
    setTimeout(() => {
      window.onerror = originalError;
      window.onunhandledrejection = originalUnhandledRejection;
      console.log(`   Error monitoring stopped. Total errors captured: ${errorCount}`);
    }, 30000);
  }

  // Check 6: Performance Metrics
  function checkPerformance() {
    console.log('\n6. ⚡ CHECKING PERFORMANCE...');
    
    if (performance && performance.timing) {
      const timing = performance.timing;
      const loadTime = timing.loadEventEnd - timing.navigationStart;
      const domReady = timing.domContentLoadedEventEnd - timing.navigationStart;
      
      console.log('   Page Load Time:', loadTime, 'ms');
      console.log('   DOM Ready Time:', domReady, 'ms');
      
      if (loadTime > 5000) {
        console.log('   ⚠️  Slow page load detected');
      }
    }
    
    // Check memory usage if available
    if (performance && performance.memory) {
      const memory = performance.memory;
      console.log('   Memory Usage:', {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + ' MB',
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + ' MB',
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + ' MB'
      });
    }
  }

  // Provide recommendations
  function provideRecommendations() {
    console.log('\n💡 RECOMMENDATIONS:');
    console.log('='.repeat(50));
    
    const authToken = localStorage.getItem('authToken') || localStorage.getItem('token');
    const authUser = localStorage.getItem('authUser') || localStorage.getItem('user');
    
    if (!authToken) {
      console.log('1. 🔑 Authentication token is missing:');
      console.log('   - Clear localStorage: localStorage.clear()');
      console.log('   - Navigate to login page and login again');
      console.log('   - Ensure you are logging in as a vendor');
    }
    
    if (authUser) {
      try {
        const user = JSON.parse(authUser);
        if (user.userType !== 'vendor') {
          console.log('2. 👤 User type is not vendor:');
          console.log('   - Check if the account is set up as a vendor');
          console.log('   - Contact admin to change user type to "vendor"');
        }
      } catch (error) {
        console.log('2. 📄 User data is corrupted:');
        console.log('   - Clear localStorage and login again');
      }
    }
    
    console.log('3. 🔄 General troubleshooting:');
    console.log('   - Hard refresh the page (Ctrl+F5)');
    console.log('   - Check browser console for more errors');
    console.log('   - Try in incognito/private mode');
    console.log('   - Test with different browser');
    
    console.log('4. 🌐 If network issues persist:');
    console.log('   - Check internet connection');
    console.log('   - Verify server is running');
    console.log('   - Test API endpoints directly');
  }

  // Run all checks
  console.log('Starting comprehensive diagnosis...\n');
  
  checkAuthentication();
  checkEnvironment();
  checkComponentState();
  checkJavaScriptErrors();
  checkPerformance();
  
  // Run network tests after a short delay
  setTimeout(() => {
    checkNetworkRequests();
    
    setTimeout(() => {
      provideRecommendations();
      console.log('\n✅ DIAGNOSIS COMPLETE');
      console.log('Check the output above for specific issues and recommendations.');
    }, 3000);
  }, 1000);

})();

// Additional utility functions you can run separately:

// Quick fix: Clear all authentication data
window.clearAuth = function() {
  localStorage.clear();
  sessionStorage.clear();
  console.log('✅ All authentication data cleared. Please refresh and login again.');
};

// Quick fix: Force reload dashboard
window.reloadDashboard = function() {
  window.location.reload();
};

// Test API connectivity
window.testAPI = function() {
  const API_BASE_URL = 'https://multi-vendor-server-1tb9.onrender.com/api';
  
  fetch(`${API_BASE_URL}/health`)
    .then(response => response.json())
    .then(data => console.log('✅ API Health:', data))
    .catch(error => console.log('❌ API Health Error:', error));
};

console.log('\n🔧 UTILITY FUNCTIONS AVAILABLE:');
console.log('- clearAuth() - Clear all auth data');
console.log('- reloadDashboard() - Force reload page');
console.log('- testAPI() - Test API connectivity');
