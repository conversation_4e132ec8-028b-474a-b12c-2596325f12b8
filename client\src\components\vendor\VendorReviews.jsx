import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Rate,
  Avatar,
  Button,
  Input,
  Form,
  message,
  Pagination,
  Empty,
  Statistic,
  Row,
  Col,
  Tag,
  Modal,
  Spin
} from 'antd';
import {
  UserOutlined,
  MessageOutlined,
  StarFilled,
  SendOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import './VendorReviews.css';

const { TextArea } = Input;

const VendorReviews = ({ 
  reviews = [], 
  loading = false,
  pagination = null,
  onPageChange,
  onReplySubmit,
  onReplyUpdate,
  onReplyDelete
}) => {
  const [replyForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [replyingTo, setReplyingTo] = useState(null);
  const [editingReply, setEditingReply] = useState(null);
  const [submitting, setSubmitting] = useState(false);

  const handleReply = async (values) => {
    if (!replyingTo) return;

    setSubmitting(true);
    try {
      await onReplySubmit(replyingTo, values.message);
      setReplyingTo(null);
      replyForm.resetFields();
      message.success('Reply posted successfully!');
    } catch (error) {
      message.error(error.message || 'Failed to post reply');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditReply = async (values) => {
    if (!editingReply) return;

    setSubmitting(true);
    try {
      await onReplyUpdate(editingReply, values.message);
      setEditingReply(null);
      editForm.resetFields();
      message.success('Reply updated successfully!');
    } catch (error) {
      message.error(error.message || 'Failed to update reply');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteReply = (replyId) => {
    Modal.confirm({
      title: 'Delete Reply',
      content: 'Are you sure you want to delete this reply?',
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          await onReplyDelete(replyId);
          message.success('Reply deleted successfully!');
        } catch (error) {
          message.error(error.message || 'Failed to delete reply');
        }
      }
    });
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const renderReviewStats = () => {
    if (!reviews.length) return null;

    const totalReviews = reviews.length;
    const averageRating = reviews.reduce((acc, review) => acc + review.rating, 0) / totalReviews;
    const repliedCount = reviews.filter(review => review.replies && review.replies.length > 0).length;
    const replyRate = (repliedCount / totalReviews) * 100;

    return (
      <Row gutter={16} className="review-stats">
        <Col xs={12} sm={6}>
          <Statistic
            title="Total Reviews"
            value={totalReviews}
            prefix={<MessageOutlined />}
          />
        </Col>
        <Col xs={12} sm={6}>
          <Statistic
            title="Average Rating"
            value={averageRating.toFixed(1)}
            prefix={<StarFilled style={{ color: '#faad14' }} />}
          />
        </Col>
        <Col xs={12} sm={6}>
          <Statistic
            title="Replied"
            value={repliedCount}
            suffix={`/ ${totalReviews}`}
          />
        </Col>
        <Col xs={12} sm={6}>
          <Statistic
            title="Reply Rate"
            value={replyRate.toFixed(0)}
            suffix="%"
          />
        </Col>
      </Row>
    );
  };

  const renderReplyActions = (review) => {
    const hasReply = review.replies && review.replies.length > 0;
    const reply = hasReply ? review.replies[0] : null;

    if (hasReply) {
      return (
        <div className="reply-actions">
          <div className="existing-reply">
            <div className="reply-content">
              <strong>Your Reply:</strong>
              <p>{reply.message}</p>
              <div className="reply-meta">
                <span>Posted on {formatDate(reply.createdAt)}</span>
                {reply.isEdited && <Tag color="orange" size="small">Edited</Tag>}
              </div>
            </div>
            <div className="reply-buttons">
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => {
                  setEditingReply(reply._id);
                  editForm.setFieldValue('message', reply.message);
                }}
                size="small"
              >
                Edit
              </Button>
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDeleteReply(reply._id)}
                size="small"
              >
                Delete
              </Button>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="reply-actions">
        <Button
          type="primary"
          icon={<SendOutlined />}
          onClick={() => setReplyingTo(review._id)}
          size="small"
          className="reply-btn"
        >
          Reply to Review
        </Button>
      </div>
    );
  };

  const renderReplyForm = (reviewId) => {
    if (replyingTo !== reviewId) return null;

    return (
      <div className="reply-form">
        <Form
          form={replyForm}
          onFinish={handleReply}
          layout="vertical"
        >
          <Form.Item
            name="message"
            label="Your Reply"
            rules={[
              { required: true, message: 'Please enter your reply' },
              { min: 10, message: 'Reply must be at least 10 characters' },
              { max: 500, message: 'Reply cannot exceed 500 characters' }
            ]}
          >
            <TextArea
              rows={3}
              placeholder="Write a professional response to this review..."
              showCount
              maxLength={500}
            />
          </Form.Item>
          <Form.Item className="reply-form-actions">
            <Button
              type="primary"
              htmlType="submit"
              loading={submitting}
              size="small"
            >
              Post Reply
            </Button>
            <Button
              onClick={() => {
                setReplyingTo(null);
                replyForm.resetFields();
              }}
              size="small"
              className="cancel-btn"
            >
              Cancel
            </Button>
          </Form.Item>
        </Form>
      </div>
    );
  };

  const renderEditForm = (reply) => {
    if (editingReply !== reply._id) return null;

    return (
      <div className="edit-form">
        <Form
          form={editForm}
          onFinish={handleEditReply}
          layout="vertical"
        >
          <Form.Item
            name="message"
            label="Edit Reply"
            rules={[
              { required: true, message: 'Please enter your reply' },
              { min: 10, message: 'Reply must be at least 10 characters' },
              { max: 500, message: 'Reply cannot exceed 500 characters' }
            ]}
          >
            <TextArea
              rows={3}
              showCount
              maxLength={500}
            />
          </Form.Item>
          <Form.Item className="edit-form-actions">
            <Button
              type="primary"
              htmlType="submit"
              loading={submitting}
              size="small"
            >
              Update Reply
            </Button>
            <Button
              onClick={() => {
                setEditingReply(null);
                editForm.resetFields();
              }}
              size="small"
              className="cancel-btn"
            >
              Cancel
            </Button>
          </Form.Item>
        </Form>
      </div>
    );
  };

  const renderReviewItem = (review) => (
    <List.Item key={review._id} className="vendor-review-item">
      <div className="review-content">
        <div className="review-header">
          <div className="customer-info">
            <Avatar
              src={review.customer?.avatar}
              icon={<UserOutlined />}
              size="default"
            />
            <div className="customer-details">
              <span className="customer-name">
                {review.customer?.firstName} {review.customer?.lastName}
              </span>
              <div className="review-meta">
                <Rate disabled value={review.rating} size="small" />
                <span className="review-date">{formatDate(review.createdAt)}</span>
              </div>
            </div>
          </div>
          <div className="product-info">
            <span className="product-name">{review.product?.name}</span>
          </div>
        </div>

        <div className="review-comment">
          <p>"{review.comment}"</p>
        </div>

        {renderReplyActions(review)}
        {renderReplyForm(review._id)}
        
        {review.replies && review.replies.length > 0 && (
          <>
            {renderEditForm(review.replies[0])}
          </>
        )}
      </div>
    </List.Item>
  );

  if (loading) {
    return (
      <Card className="vendor-reviews-card">
        <div className="loading-container">
          <Spin size="large" />
          <p>Loading reviews...</p>
        </div>
      </Card>
    );
  }

  return (
    <Card
      title="Customer Reviews"
      className="vendor-reviews-card"
    >
      {renderReviewStats()}

      {reviews.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="No reviews yet"
          className="empty-reviews"
        >
          <p>Your products haven't received any reviews yet.</p>
        </Empty>
      ) : (
        <>
          <List
            dataSource={reviews}
            renderItem={renderReviewItem}
            className="vendor-reviews-list"
          />

          {pagination && pagination.totalPages > 1 && (
            <div className="pagination-container">
              <Pagination
                current={pagination.currentPage}
                total={pagination.totalReviews}
                pageSize={10}
                showSizeChanger={false}
                showQuickJumper
                showTotal={(total, range) =>
                  `${range[0]}-${range[1]} of ${total} reviews`
                }
                onChange={onPageChange}
              />
            </div>
          )}
        </>
      )}
    </Card>
  );
};

export default VendorReviews;
