// Fixed updateProfile method for authController.js

// Update user profile
async updateProfile(req, res) {
    try {
        const { userId } = req.user;
        const updateData = req.body || {};

        console.log('=== PROFILE UPDATE DEBUG ===');
        console.log('- User ID:', userId);
        console.log('- Request body:', JSON.stringify(req.body, null, 2));
        console.log('- Update data:', JSON.stringify(updateData, null, 2));
        console.log('- Content-Type:', req.headers['content-type']);
        console.log('- Request method:', req.method);

        // Validate that we have update data
        if (!updateData || Object.keys(updateData).length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No update data provided'
            });
        }

        // Find user
        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        console.log('User found - ID:', user._id, 'Type:', user.userType, 'Email:', user.email);

        // Handle preferences if it's a JSON string (from form data)
        if (updateData.preferences && typeof updateData.preferences === 'string') {
            try {
                updateData.preferences = JSON.parse(updateData.preferences);
                console.log('Parsed preferences from string:', updateData.preferences);
            } catch (error) {
                console.log('Failed to parse preferences JSON:', error.message);
                delete updateData.preferences; // Remove invalid preferences
            }
        }

        // Remove email field for security (email changes should be handled separately)
        if (updateData.email) {
            console.log('Removing email field from update data for security');
            delete updateData.email;
        }

        // Update allowed fields based on user type (added missing fields)
        const allowedFields = user.userType === 'vendor' 
            ? ['businessName', 'businessType', 'contactPerson', 'dateOfBirth', 'gender', 'address', 'city', 'state', 'zipCode', 'country', 'phone', 'bio', 'displayName', 'website']
            : ['firstName', 'lastName', 'dateOfBirth', 'gender', 'address', 'city', 'state', 'zipCode', 'country', 'phone', 'bio', 'displayName', 'website'];

        console.log('Allowed fields for', user.userType + ':', allowedFields);

        // Filter update data to only include allowed fields
        const filteredUpdateData = {};

        allowedFields.forEach(field => {
            if (updateData.hasOwnProperty(field) && updateData[field] !== undefined) {
                // Handle empty strings - convert to null for optional fields
                filteredUpdateData[field] = updateData[field] === '' ? null : updateData[field];
            }
        });

        // Handle preferences separately
        if (updateData.preferences && typeof updateData.preferences === 'object') {
            console.log('Processing preferences:', updateData.preferences);
            
            // Validate and set individual preference fields
            if (updateData.preferences.language !== undefined) {
                const validLanguages = ['en', 'es', 'fr', 'de'];
                if (validLanguages.includes(updateData.preferences.language)) {
                    filteredUpdateData['preferences.language'] = updateData.preferences.language;
                } else {
                    console.log('Invalid language:', updateData.preferences.language);
                }
            }
            
            if (updateData.preferences.currency !== undefined) {
                const validCurrencies = ['USD', 'EUR', 'GBP', 'CAD'];
                if (validCurrencies.includes(updateData.preferences.currency)) {
                    filteredUpdateData['preferences.currency'] = updateData.preferences.currency;
                } else {
                    console.log('Invalid currency:', updateData.preferences.currency);
                }
            }
            
            if (updateData.preferences.timezone !== undefined) {
                if (typeof updateData.preferences.timezone === 'string' && updateData.preferences.timezone.length <= 50) {
                    filteredUpdateData['preferences.timezone'] = updateData.preferences.timezone;
                } else {
                    console.log('Invalid timezone:', updateData.preferences.timezone);
                }
            }
            
            if (updateData.preferences.notifications !== undefined && typeof updateData.preferences.notifications === 'object') {
                filteredUpdateData['preferences.notifications'] = updateData.preferences.notifications;
            }
        }

        console.log('Filtered update data:', JSON.stringify(filteredUpdateData, null, 2));

        // Check if there are any fields to update
        if (Object.keys(filteredUpdateData).length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No valid fields to update. Please provide at least one valid field with a non-empty value.',
                allowedFields: allowedFields,
                receivedFields: Object.keys(updateData).filter(field => field !== 'email'), // Exclude email from received fields
                hint: 'Email updates are not allowed for security reasons. Other fields should be valid.',
                examples: {
                    user: { address: '123 Main St', city: 'New York' },
                    vendor: { businessName: 'My Business', address: '456 Oak Ave' }
                }
            });
        }

        // Update user using $set to handle nested fields properly
        console.log('Updating user with data:', filteredUpdateData);
        const updateResult = await User.findByIdAndUpdate(
            userId, 
            { $set: filteredUpdateData }, 
            { 
                new: true,
                runValidators: true // Ensure validation runs on update
            }
        );

        if (!updateResult) {
            return res.status(404).json({
                success: false,
                message: 'Failed to update user'
            });
        }

        // Fetch updated user to ensure we have the latest data
        const updatedUser = await User.findById(userId);

        // Remove sensitive data from response
        const userResponse = updatedUser.toObject();
        delete userResponse.password;
        delete userResponse.emailVerification.verificationToken;
        delete userResponse.passwordReset;
        delete userResponse.twoFactorAuth.secret;

        console.log('Profile updated successfully for user:', userId);

        res.json({
            success: true,
            message: 'Profile updated successfully',
            data: { 
                user: userResponse,
                updatedFields: Object.keys(filteredUpdateData)
            }
        });

    } catch (error) {
        console.error('Update profile error:', error);
        
        if (error.name === 'ValidationError') {
            const errors = Object.values(error.errors).map(err => ({
                field: err.path,
                message: err.message,
                value: err.value
            }));
            return res.status(400).json({
                success: false,
                message: 'Validation error',
                errors
            });
        }

        if (error.name === 'CastError') {
            return res.status(400).json({
                success: false,
                message: `Invalid value for field: ${error.path}`,
                field: error.path,
                value: error.value
            });
        }

        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
}