const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

async function testCarouselUpload() {
  console.log('🔍 Testing Carousel Upload with Proper Authentication...\n');
  
  const BASE_URL = 'http://localhost:5000';
  const ADMIN_CREDENTIALS = {
    email: '<EMAIL>',
    password: 'password@admin123'
  };
  
  try {
    // Step 1: Login as admin
    console.log('1. Logging in as admin...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, ADMIN_CREDENTIALS);
    
    // Extract token from the response structure we saw
    let adminToken = null;
    if (loginResponse.data.success) {
      if (loginResponse.data.data?.token) {
        adminToken = loginResponse.data.data.token;
      } else if (loginResponse.data.token) {
        adminToken = loginResponse.data.token;
      }
    }
    
    if (!adminToken) {
      console.log('❌ Could not extract token from login response');
      console.log('Response:', JSON.stringify(loginResponse.data, null, 2));
      return;
    }
    
    console.log('✅ Successfully logged in as admin');
    console.log(`🔑 Token: ${adminToken.substring(0, 20)}...`);
    
    // Step 2: Create test image
    console.log('\n2. Creating test image...');
    const testImagePath = path.join(__dirname, 'test-carousel-upload.jpg');
    
    // Create a proper JPEG file
    const jpegHeader = Buffer.from([
      0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
      0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
      0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
      0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
      0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
      0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
      0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
      0x3C, 0x2E, 0x33, 0x34, 0x32
    ]);
    const jpegFooter = Buffer.from([0xFF, 0xD9]);
    const jpegBody = Buffer.alloc(2000, 0x00); // 2KB body
    
    const testImageData = Buffer.concat([jpegHeader, jpegBody, jpegFooter]);
    fs.writeFileSync(testImagePath, testImageData);
    console.log(`✅ Created test image: ${testImagePath} (${testImageData.length} bytes)`);
    
    // Step 3: Test carousel upload
    console.log('\n3. Testing carousel upload...');
    
    const form = new FormData();
    form.append('image', fs.createReadStream(testImagePath), {
      filename: 'test-carousel.jpg',
      contentType: 'image/jpeg'
    });
    form.append('title', 'Debug Test Carousel');
    form.append('description', 'Testing carousel upload functionality');
    form.append('linkUrl', 'https://example.com/test');
    
    console.log('📦 FormData prepared with:');
    console.log('  - image: test-carousel.jpg (image/jpeg)');
    console.log('  - title: "Debug Test Carousel"');
    console.log('  - description: "Testing carousel upload functionality"');
    console.log('  - linkUrl: "https://example.com/test"');
    
    try {
      console.log('\n📤 Sending upload request...');
      const uploadResponse = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, form, {
        headers: {
          ...form.getHeaders(),
          'Authorization': `Bearer ${adminToken}`
        },
        timeout: 60000, // 60 second timeout
        maxContentLength: Infinity,
        maxBodyLength: Infinity
      });
      
      console.log('✅ UPLOAD SUCCESSFUL!');
      console.log(`📥 Status: ${uploadResponse.status}`);
      console.log('📄 Response:');
      console.log(JSON.stringify(uploadResponse.data, null, 2));
      
    } catch (uploadError) {
      console.log('❌ UPLOAD FAILED!');
      console.log(`📥 Status: ${uploadError.response?.status}`);
      console.log('📄 Error Response:');
      console.log(JSON.stringify(uploadError.response?.data, null, 2));
      
      // Detailed error analysis
      console.log('\n🔍 ERROR ANALYSIS:');
      if (uploadError.response?.data?.message === 'Image file is required') {
        console.log('❌ ISSUE: Server did not receive the image file');
        console.log('   This means:');
        console.log('   - FormData construction is correct (we can see the form data)');
        console.log('   - Authentication is working (we got past auth middleware)');
        console.log('   - The issue is in the multer middleware or file processing');
        console.log('   - Check server logs for multer errors');
        console.log('   - Verify Cloudinary configuration');
      } else if (uploadError.response?.status === 401) {
        console.log('❌ ISSUE: Authentication failed');
        console.log('   - Token may be invalid or expired');
        console.log('   - Check admin middleware configuration');
      } else if (uploadError.response?.status === 413) {
        console.log('❌ ISSUE: File too large');
        console.log('   - Check file size limits in multer configuration');
      } else if (uploadError.code === 'ECONNABORTED') {
        console.log('❌ ISSUE: Request timeout');
        console.log('   - Upload is taking too long');
        console.log('   - Check Cloudinary upload performance');
      } else {
        console.log('❌ ISSUE: Unknown error');
        console.log(`   Error code: ${uploadError.code}`);
        console.log(`   Error message: ${uploadError.message}`);
      }
    }
    
    // Step 4: Test without file (validation check)
    console.log('\n4. Testing validation (upload without file)...');
    
    const formNoFile = new FormData();
    formNoFile.append('title', 'test');
    formNoFile.append('description', 'test');
    formNoFile.append('linkUrl', '');
    
    try {
      const validationResponse = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, formNoFile, {
        headers: {
          ...formNoFile.getHeaders(),
          'Authorization': `Bearer ${adminToken}`
        },
        timeout: 10000
      });
      
      console.log('❌ Validation failed - should have rejected request without file');
      
    } catch (validationError) {
      if (validationError.response?.data?.message === 'Image file is required') {
        console.log('✅ Validation working - correctly rejected request without file');
      } else {
        console.log('⚠️ Unexpected validation error:', validationError.response?.data);
      }
    }
    
    // Step 5: Get current homepage settings
    console.log('\n5. Checking current homepage settings...');
    
    try {
      const settingsResponse = await axios.get(`${BASE_URL}/api/admin/homepage-settings`, {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });
      
      console.log('✅ Retrieved homepage settings');
      const carouselImages = settingsResponse.data.data.carouselImages || [];
      console.log(`📊 Current carousel images: ${carouselImages.length}`);
      
      if (carouselImages.length > 0) {
        console.log('📋 Existing carousel images:');
        carouselImages.forEach((img, index) => {
          console.log(`  ${index + 1}. "${img.title}" - ${img.imageUrl}`);
        });
      }
      
    } catch (settingsError) {
      console.log('❌ Failed to get homepage settings:', settingsError.response?.data);
    }
    
    // Cleanup
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
      console.log('\n🧹 Cleaned up test files');
    }
    
    console.log('\n📋 SUMMARY:');
    console.log('✅ Authentication: Working');
    console.log('✅ Server: Accessible');
    console.log('✅ Validation: Working');
    console.log('');
    console.log('If upload failed with "Image file is required":');
    console.log('- Check server console for multer/cloudinary errors');
    console.log('- Verify CLOUDINARY_* environment variables');
    console.log('- Check multer middleware configuration');
    console.log('- Ensure file field name is exactly "image"');
    
  } catch (error) {
    console.error('❌ Test script failed:', error.message);
    if (error.response) {
      console.log('Response data:', error.response.data);
    }
  }
}

// Run the test
testCarouselUpload().catch(console.error);