const express = require('express');
const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Import models and middleware
const { User, Vendor } = require('./src/models');
const vendorStoreRoutes = require('./src/routes/vendor/store');
const { verifyToken, requireUserType } = require('./src/middleware/auth/authMiddleware');

async function testVendorUploadEndpoints() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find a vendor user for testing
    const vendorUser = await User.findOne({ userType: 'vendor' });
    if (!vendorUser) {
      console.log('❌ No vendor user found');
      return;
    }

    console.log('📋 Testing vendor upload endpoints with user:', vendorUser.email);

    // Create Express app for testing
    const app = express();
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));

    // Mock authentication middleware
    const mockAuth = (req, res, next) => {
      req.user = {
        userId: vendorUser._id,
        userType: 'vendor'
      };
      next();
    };

    // Apply mock auth and vendor routes
    app.use('/api/vendor/store', mockAuth);
    app.use('/api/vendor/store', vendorStoreRoutes);

    // Start server
    const server = app.listen(3001, () => {
      console.log('🚀 Test server running on port 3001');
    });

    console.log('\n🧪 Testing Vendor Upload Endpoints...');

    // Test 1: Check if upload endpoints are accessible
    console.log('\n📋 Test 1: Checking upload endpoint accessibility');
    
    const axios = require('axios');
    const FormData = require('form-data');

    // Create a test image file (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00,
      0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xE2, 0x21, 0xBC, 0x33,
      0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    // Test logo upload
    try {
      const logoFormData = new FormData();
      logoFormData.append('logo', testImageBuffer, {
        filename: 'test-logo.png',
        contentType: 'image/png'
      });

      const logoResponse = await axios.post('http://localhost:3001/api/vendor/store/logo', logoFormData, {
        headers: {
          ...logoFormData.getHeaders()
        }
      });

      if (logoResponse.data.success) {
        console.log('✅ Logo upload endpoint working');
        console.log('   - Response:', logoResponse.data.message);
      } else {
        console.log('❌ Logo upload failed:', logoResponse.data.message);
      }
    } catch (error) {
      console.log('❌ Logo upload error:', error.response?.data?.message || error.message);
    }

    // Test avatar upload
    try {
      const avatarFormData = new FormData();
      avatarFormData.append('avatar', testImageBuffer, {
        filename: 'test-avatar.png',
        contentType: 'image/png'
      });

      const avatarResponse = await axios.post('http://localhost:3001/api/vendor/store/avatar', avatarFormData, {
        headers: {
          ...avatarFormData.getHeaders()
        }
      });

      if (avatarResponse.data.success) {
        console.log('✅ Avatar upload endpoint working');
        console.log('   - Response:', avatarResponse.data.message);
      } else {
        console.log('❌ Avatar upload failed:', avatarResponse.data.message);
      }
    } catch (error) {
      console.log('❌ Avatar upload error:', error.response?.data?.message || error.message);
    }

    // Test 2: Check Cloudinary configuration
    console.log('\n📋 Test 2: Checking Cloudinary configuration');
    const { validateCloudinaryConfig } = require('./src/config/cloudinary');
    
    if (validateCloudinaryConfig()) {
      console.log('✅ Cloudinary is properly configured');
      console.log('   - Cloud Name:', process.env.CLOUDINARY_CLOUD_NAME);
      console.log('   - API Key:', process.env.CLOUDINARY_API_KEY ? 'Set' : 'Not set');
      console.log('   - API Secret:', process.env.CLOUDINARY_API_SECRET ? 'Set' : 'Not set');
    } else {
      console.log('❌ Cloudinary configuration missing');
      console.log('   - Will fallback to local storage');
    }

    // Test 3: Check upload directories
    console.log('\n📋 Test 3: Checking upload directories');
    const uploadDirs = [
      './uploads',
      './uploads/images',
      './uploads/images/vendors',
      './uploads/images/users'
    ];

    for (const dir of uploadDirs) {
      const fullPath = path.resolve(__dirname, dir);
      if (fs.existsSync(fullPath)) {
        console.log(`✅ Directory exists: ${dir}`);
      } else {
        console.log(`❌ Directory missing: ${dir}`);
        // Create directory if it doesn't exist
        try {
          fs.mkdirSync(fullPath, { recursive: true });
          console.log(`✅ Created directory: ${dir}`);
        } catch (createError) {
          console.log(`❌ Failed to create directory: ${dir}`, createError.message);
        }
      }
    }

    // Test 4: Check vendor profile data
    console.log('\n📋 Test 4: Checking vendor profile data');
    const vendor = await Vendor.findOne({ user: vendorUser._id })
      .populate('user', 'firstName lastName email avatar');

    if (vendor) {
      console.log('✅ Vendor profile found');
      console.log('   - Business Name:', vendor.businessName || 'Not set');
      console.log('   - Logo:', vendor.logo || 'Not set');
      console.log('   - User Avatar:', vendor.user?.avatar || 'Not set');
    } else {
      console.log('❌ Vendor profile not found');
    }

    console.log('\n🎉 Upload Endpoint Test Completed!');
    
    console.log('\n📝 Test Summary:');
    console.log('   - Upload endpoints are accessible');
    console.log('   - Cloudinary configuration checked');
    console.log('   - Upload directories verified');
    console.log('   - Vendor profile data checked');
    
    console.log('\n🔧 Next Steps:');
    console.log('   1. Test actual file uploads from frontend');
    console.log('   2. Verify image URLs are properly saved to database');
    console.log('   3. Check if images are accessible via browser');

    // Close server
    server.close();

  } catch (error) {
    console.error('❌ Error during upload endpoint testing:', error);
    console.error('Error details:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testVendorUploadEndpoints();