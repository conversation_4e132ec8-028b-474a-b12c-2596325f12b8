# Multi-Vendor eCommerce Admin Panel Setup Guide

## Quick Fix for Socket.IO Error

The error you're seeing is because `socket.io-client` is not installed. Here's how to fix it:

### Step 1: Install Dependencies

#### For Client (Frontend):
```bash
cd client
npm install socket.io-client
```

#### For Server (Backend):
```bash
cd server
npm install socket.io
```

### Step 2: Start the Application

#### Start the Server:
```bash
cd server
npm run dev
```

#### Start the Client:
```bash
cd client
npm run dev
```

## What You Get

### ✅ **Working Features (Without Socket.IO)**
- Complete admin dashboard with MongoDB data
- User management (CRUD operations)
- Vendor management system
- Order management system
- Real-time charts and analytics
- Professional UI with Ant Design

### 🔌 **Real-time Features (After Installing Socket.IO)**
- Live dashboard updates
- Instant notifications for new orders
- Real-time user registrations
- Low stock alerts
- Live connection status
- Multi-admin collaboration

## Current Status

The admin panel is **fully functional** without Socket.IO. You can:
- View dashboard statistics
- Manage users, vendors, and orders
- See charts and analytics
- Use all CRUD operations

Socket.IO only adds **real-time features** on top of the existing functionality.

## Environment Setup

### Server Environment Variables (.env)
```env
# Database
MONGODB_URI=mongodb://localhost:27017/multi-vendor-ecommerce

# JWT
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d

# Server
PORT=8000
NODE_ENV=development

# Frontend URL
FRONTEND_URL=http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### Client Environment Variables (.env)
```env
VITE_API_URL=http://localhost:8000
```

## Database Models Included

✅ **User Model** - Complete user management  
✅ **Vendor Model** - Vendor verification & management  
✅ **Product Model** - Product catalog with variants  
✅ **Order Model** - Order lifecycle management  
✅ **Category Model** - Hierarchical categories  

## API Endpoints Available

### Dashboard
- `GET /api/admin/dashboard/stats` - Dashboard statistics
- `GET /api/admin/dashboard/realtime` - Real-time metrics
- `GET /api/admin/dashboard/analytics` - Analytics data

### Users
- `GET /api/admin/users` - List users with pagination
- `GET /api/admin/users/:id` - Get user details
- `POST /api/admin/users` - Create new user
- `PUT /api/admin/users/:id` - Update user
- `DELETE /api/admin/users/:id` - Delete user

### Vendors
- `GET /api/admin/vendors` - List vendors
- `GET /api/admin/vendors/:id` - Get vendor details
- `PATCH /api/admin/vendors/:id/verification` - Update verification

### Orders
- `GET /api/admin/orders` - List orders
- `GET /api/admin/orders/:id` - Get order details
- `PATCH /api/admin/orders/:id/status` - Update order status

## Troubleshooting

### Common Issues:

1. **Socket.IO Error**: Install `socket.io-client` in client directory
2. **Database Connection**: Make sure MongoDB is running
3. **CORS Issues**: Check if frontend URL is in CORS whitelist
4. **Port Conflicts**: Change ports in .env files if needed

### Testing Without Database:
The admin panel will show mock data if database connection fails, so you can test the UI immediately.

## Next Steps

1. Install the missing dependencies
2. Set up MongoDB connection
3. Create some test data
4. Explore the admin panel features
5. Enable real-time features with Socket.IO

The admin panel is production-ready with:
- Security middleware
- Input validation
- Error handling
- Responsive design
- Professional UI/UX