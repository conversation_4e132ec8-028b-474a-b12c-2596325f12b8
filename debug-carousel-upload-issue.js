const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

async function debugCarouselUpload() {
  console.log('🔍 Debugging Carousel Upload Issue...\n');
  
  const BASE_URL = 'http://localhost:5000';
  
  try {
    // Step 1: Check if server is running
    console.log('1. Checking server status...');
    try {
      const healthCheck = await axios.get(`${BASE_URL}/api/health`);
      console.log('✅ Server is running');
    } catch (error) {
      console.log('⚠️ Server health check failed, but continuing...');
    }
    
    // Step 2: Create a proper test image
    console.log('\n2. Creating test image...');
    const testImagePath = path.join(__dirname, 'test-carousel-image.jpg');
    
    // Create a minimal but valid JPEG file
    const jpegHeader = Buffer.from([
      0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
      0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
      0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
      0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
      0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
      0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
      0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
      0x3C, 0x2E, 0x33, 0x34, 0x32
    ]);
    const jpegFooter = Buffer.from([0xFF, 0xD9]);
    const jpegBody = Buffer.alloc(500, 0x00); // Padding
    
    const testImageData = Buffer.concat([jpegHeader, jpegBody, jpegFooter]);
    fs.writeFileSync(testImagePath, testImageData);
    console.log(`✅ Created test image: ${testImagePath} (${testImageData.length} bytes)`);
    
    // Step 3: Test without authentication (should fail with 401)
    console.log('\n3. Testing upload without authentication...');
    const form1 = new FormData();
    form1.append('image', fs.createReadStream(testImagePath), {
      filename: 'test-carousel.jpg',
      contentType: 'image/jpeg'
    });
    form1.append('title', 'Test Carousel Image');
    form1.append('description', 'Test description for carousel');
    form1.append('linkUrl', 'https://example.com');
    
    try {
      const response1 = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, form1, {
        headers: {
          ...form1.getHeaders(),
        },
        timeout: 30000
      });
      console.log('❌ Unexpected success without auth:', response1.status);
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Correctly rejected without authentication (401)');
      } else {
        console.log('❌ Unexpected error:', error.response?.status, error.response?.data);
      }
    }
    
    // Step 4: Try to get admin token (this would need to be implemented based on your auth system)
    console.log('\n4. Attempting to get admin token...');
    let adminToken = null;
    
    // Try to login as admin (adjust credentials as needed)
    try {
      const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
        email: '<EMAIL>', // Adjust as needed
        password: 'admin123' // Adjust as needed
      });
      
      if (loginResponse.data.token) {
        adminToken = loginResponse.data.token;
        console.log('�� Got admin token');
      }
    } catch (error) {
      console.log('⚠️ Could not get admin token:', error.response?.data?.message || error.message);
      console.log('   You may need to create an admin user or adjust credentials');
    }
    
    // Step 5: Test with authentication (if we have a token)
    if (adminToken) {
      console.log('\n5. Testing upload with authentication...');
      const form2 = new FormData();
      form2.append('image', fs.createReadStream(testImagePath), {
        filename: 'test-carousel-auth.jpg',
        contentType: 'image/jpeg'
      });
      form2.append('title', 'Authenticated Test Carousel');
      form2.append('description', 'Test with proper authentication');
      form2.append('linkUrl', 'https://example.com/auth-test');
      
      try {
        const response2 = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, form2, {
          headers: {
            ...form2.getHeaders(),
            'Authorization': `Bearer ${adminToken}`
          },
          timeout: 30000
        });
        console.log('✅ Upload successful with auth:', response2.status);
        console.log('Response:', response2.data);
      } catch (error) {
        console.log('❌ Upload failed with auth:', error.response?.status);
        console.log('Error details:', error.response?.data);
        
        // Check if it's the "no file" error we're debugging
        if (error.response?.data?.message === 'Image file is required') {
          console.log('\n🔍 FOUND THE ISSUE: File is not being received by the server');
          console.log('This suggests a problem with:');
          console.log('- Frontend form data construction');
          console.log('- Multer middleware configuration');
          console.log('- File field name mismatch');
        }
      }
    } else {
      console.log('\n5. Skipping authenticated test (no token available)');
    }
    
    // Step 6: Test the exact scenario from your error
    console.log('\n6. Testing the exact failing scenario...');
    const form3 = new FormData();
    // Note: NOT adding image file to simulate the issue
    form3.append('title', 'test');
    form3.append('description', 'test');
    form3.append('linkUrl', '');
    
    try {
      const response3 = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, form3, {
        headers: {
          ...form3.getHeaders(),
          ...(adminToken ? { 'Authorization': `Bearer ${adminToken}` } : {})
        },
        timeout: 30000
      });
      console.log('❌ Unexpected success without image:', response3.status);
    } catch (error) {
      if (error.response?.data?.message === 'Image file is required') {
        console.log('✅ Correctly rejected request without image file');
        console.log('This confirms the server-side validation is working');
      } else {
        console.log('❌ Different error:', error.response?.status, error.response?.data);
      }
    }
    
    // Step 7: Provide debugging recommendations
    console.log('\n7. 🔧 DEBUGGING RECOMMENDATIONS:');
    console.log('');
    console.log('Based on the error logs, the issue is that no file is being uploaded.');
    console.log('Here are the most likely causes:');
    console.log('');
    console.log('A. Frontend Issues:');
    console.log('   - Form field name mismatch (should be "image")');
    console.log('   - Missing enctype="multipart/form-data" on form');
    console.log('   - File input not properly bound to form data');
    console.log('   - JavaScript FormData not constructed correctly');
    console.log('');
    console.log('B. Network Issues:');
    console.log('   - Request timeout during file upload');
    console.log('   - File size exceeding limits');
    console.log('   - CORS issues preventing file upload');
    console.log('');
    console.log('C. Server Configuration:');
    console.log('   - Multer middleware not properly configured');
    console.log('   - Cloudinary configuration issues');
    console.log('   - File size limits too restrictive');
    console.log('');
    console.log('To fix this, check:');
    console.log('1. Frontend form uses field name "image"');
    console.log('2. Form has proper multipart encoding');
    console.log('3. File is actually selected before submission');
    console.log('4. Network tab shows file in request payload');
    console.log('5. Server logs show file being received by multer');
    
    // Cleanup
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
      console.log('\n🧹 Cleaned up test files');
    }
    
  } catch (error) {
    console.error('❌ Debug script failed:', error.message);
  }
}

// Run the debug script
debugCarouselUpload().catch(console.error);