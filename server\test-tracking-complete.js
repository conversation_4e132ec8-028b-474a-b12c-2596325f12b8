const mongoose = require('mongoose');
const axios = require('axios');
require('dotenv').config();

// Import models
const OrderTracking = require('./src/models/OrderTracking');
const Order = require('./src/models/Order');
const User = require('./src/models/User');

// Server URL
const SERVER_URL = process.env.SERVER_URL || 'http://localhost:5000';

async function testTrackingFeature() {
  console.log('🧪 Comprehensive Order Tracking Feature Test');
  console.log('===========================================\n');

  try {
    // Connect to database
    const dbUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce';
    await mongoose.connect(dbUri);
    console.log('✅ Connected to database\n');

    let testResults = {
      modelTests: {},
      apiTests: {},
      overallPass: true
    };

    // ============ PART 1: MODEL TESTS ============
    console.log('📋 PART 1: Testing OrderTracking Model');
    console.log('=====================================');

    try {
      // Test 1: Create tracking
      console.log('\n1️⃣ Testing tracking creation...');
      const trackingData = {
        orderId: new mongoose.Types.ObjectId(),
        vendorId: new mongoose.Types.ObjectId(),
        deliveryAddress: {
          street: '123 Test Street',
          city: 'Test City',
          state: 'Test State',
          zipCode: '12345',
          country: 'Test Country'
        },
        recipient: {
          name: 'John Doe',
          phone: '1234567890',
          email: '<EMAIL>'
        }
      };

      const tracking = await OrderTracking.createTracking(trackingData);
      testResults.modelTests.createTracking = tracking ? 'PASS' : 'FAIL';
      
      if (tracking) {
        console.log('✅ Tracking created successfully');
        console.log(`   Tracking Number: ${tracking.trackingNumber}`);
        console.log(`   Status: ${tracking.currentStatus}`);
        console.log(`   Progress: ${tracking.progressPercentage}%`);
      } else {
        console.log('❌ Failed to create tracking');
        testResults.overallPass = false;
      }

      // Test 2: Status updates
      console.log('\n2️⃣ Testing status updates...');
      const statusUpdates = [
        { status: 'processing', reason: 'Order is being prepared' },
        { status: 'shipped', reason: 'Package shipped from warehouse' },
        { status: 'out_for_delivery', reason: 'Package out for delivery' },
        { status: 'delivered', reason: 'Package delivered successfully' }
      ];

      let statusUpdateSuccess = true;
      for (const update of statusUpdates) {
        try {
          await tracking.updateStatus(update.status, { description: update.reason });
          console.log(`✅ Status updated to "${update.status}" (${tracking.progressPercentage}%)`);
        } catch (error) {
          console.log(`❌ Failed to update status to "${update.status}": ${error.message}`);
          statusUpdateSuccess = false;
          testResults.overallPass = false;
        }
      }
      testResults.modelTests.statusUpdates = statusUpdateSuccess ? 'PASS' : 'FAIL';

      // Test 3: Tracking lookup
      console.log('\n3️⃣ Testing tracking lookup...');
      try {
        const foundTracking = await OrderTracking.getByTrackingNumber(tracking.trackingNumber);
        if (foundTracking) {
          console.log('✅ Tracking found by tracking number');
          console.log(`   Status: ${foundTracking.currentStatus}`);
          console.log(`   Steps: ${foundTracking.trackingSteps.length} tracking steps`);
          testResults.modelTests.trackingLookup = 'PASS';
        } else {
          console.log('❌ Tracking not found');
          testResults.modelTests.trackingLookup = 'FAIL';
          testResults.overallPass = false;
        }
      } catch (error) {
        console.log(`❌ Error looking up tracking: ${error.message}`);
        testResults.modelTests.trackingLookup = 'FAIL';
        testResults.overallPass = false;
      }

      // Test 4: Tracking number uniqueness
      console.log('\n4️⃣ Testing tracking number uniqueness...');
      const trackingNumbers = new Set();
      trackingNumbers.add(tracking.trackingNumber);

      let uniquenessTest = true;
      for (let i = 0; i < 5; i++) {
        try {
          const testData = {
            orderId: new mongoose.Types.ObjectId(),
            vendorId: new mongoose.Types.ObjectId(),
            deliveryAddress: trackingData.deliveryAddress,
            recipient: trackingData.recipient
          };
          
          const testTracking = await OrderTracking.createTracking(testData);
          if (trackingNumbers.has(testTracking.trackingNumber)) {
            console.log(`❌ Duplicate tracking number found: ${testTracking.trackingNumber}`);
            uniquenessTest = false;
            testResults.overallPass = false;
          } else {
            trackingNumbers.add(testTracking.trackingNumber);
          }
        } catch (error) {
          console.log(`❌ Error creating test tracking: ${error.message}`);
          uniquenessTest = false;
          testResults.overallPass = false;
        }
      }

      if (uniquenessTest && trackingNumbers.size === 6) {
        console.log('✅ All tracking numbers are unique');
        console.log(`   Generated ${trackingNumbers.size} unique numbers`);
      }
      testResults.modelTests.uniqueness = uniquenessTest ? 'PASS' : 'FAIL';

      // Test 5: Tracking number format
      console.log('\n5️⃣ Testing tracking number format...');
      const trackingPattern = /^TRK\d+[A-Z0-9]{4}$/;
      let formatTest = true;
      
      for (const number of trackingNumbers) {
        if (!trackingPattern.test(number)) {
          console.log(`❌ Invalid format: ${number}`);
          formatTest = false;
          testResults.overallPass = false;
          break;
        }
      }

      if (formatTest) {
        console.log('✅ All tracking numbers follow correct format');
        console.log('   Format: TRK{timestamp}{4-character-code}');
      }
      testResults.modelTests.format = formatTest ? 'PASS' : 'FAIL';

      // Clean up test tracking records
      console.log('\n🧹 Cleaning up model test data...');
      await OrderTracking.deleteMany({
        trackingNumber: { $in: Array.from(trackingNumbers) }
      });
      console.log('✅ Model test data cleaned up');

    } catch (error) {
      console.log(`❌ Model test failed: ${error.message}`);
      testResults.overallPass = false;
    }

    // ============ PART 2: API TESTS ============
    console.log('\n\n📋 PART 2: Testing API Endpoints');
    console.log('===============================');

    try {
      // Check if server is running
      console.log('🔍 Checking if server is available...');
      try {
        const healthCheck = await axios.get(`${SERVER_URL}/api/health`, { timeout: 5000 });
        console.log('✅ Server is running and accessible');
      } catch (error) {
        console.log('⚠️  Server not accessible - API tests will be skipped');
        console.log(`   Make sure the server is running on ${SERVER_URL}`);
        testResults.apiTests.serverAvailable = 'SKIP';
        
        // Still mark overall as pass if model tests passed
        if (Object.values(testResults.modelTests).every(result => result === 'PASS')) {
          console.log('\n🎉 Model tests all passed - tracking feature is functional!');
        }
        
        displayTestResults(testResults);
        return;
      }

      // Create test tracking for API tests
      console.log('\n1️⃣ Creating test tracking for API tests...');
      const apiTrackingData = {
        orderId: new mongoose.Types.ObjectId(),
        vendorId: new mongoose.Types.ObjectId(),
        deliveryAddress: {
          street: '456 API Test St',
          city: 'API City',
          state: 'API State',
          zipCode: '54321',
          country: 'API Country'
        },
        recipient: {
          name: 'API Test User',
          phone: '9876543210',
          email: '<EMAIL>'
        }
      };

      const apiTracking = await OrderTracking.createTracking(apiTrackingData);
      console.log(`✅ API test tracking created: ${apiTracking.trackingNumber}`);

      // Test API endpoint: Get tracking by number (public)
      console.log('\n2️⃣ Testing public tracking lookup API...');
      try {
        const response = await axios.get(`${SERVER_URL}/api/order-tracking/${apiTracking.trackingNumber}`);
        
        if (response.status === 200 && response.data.success) {
          console.log('✅ Public tracking API works');
          console.log(`   Retrieved tracking: ${response.data.data.trackingNumber}`);
          console.log(`   Status: ${response.data.data.currentStatus}`);
          testResults.apiTests.publicLookup = 'PASS';
        } else {
          console.log('❌ Public tracking API failed');
          testResults.apiTests.publicLookup = 'FAIL';
          testResults.overallPass = false;
        }
      } catch (error) {
        console.log(`❌ Public tracking API error: ${error.response?.status || error.message}`);
        testResults.apiTests.publicLookup = 'FAIL';
        testResults.overallPass = false;
      }

      // Test invalid tracking number
      console.log('\n3️⃣ Testing invalid tracking number...');
      try {
        const response = await axios.get(`${SERVER_URL}/api/order-tracking/INVALID123`);
        console.log('❌ Should have returned 404 for invalid tracking number');
        testResults.apiTests.invalidLookup = 'FAIL';
        testResults.overallPass = false;
      } catch (error) {
        if (error.response?.status === 404) {
          console.log('✅ Correctly returned 404 for invalid tracking number');
          testResults.apiTests.invalidLookup = 'PASS';
        } else {
          console.log(`❌ Unexpected error: ${error.response?.status || error.message}`);
          testResults.apiTests.invalidLookup = 'FAIL';
          testResults.overallPass = false;
        }
      }

      // Clean up API test data
      console.log('\n🧹 Cleaning up API test data...');
      await OrderTracking.deleteOne({ _id: apiTracking._id });
      console.log('✅ API test data cleaned up');

    } catch (error) {
      console.log(`❌ API test failed: ${error.message}`);
      testResults.overallPass = false;
    }

    // Display results
    displayTestResults(testResults);

  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  } finally {
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.close();
      console.log('\n🔌 Database connection closed');
    }
  }
}

function displayTestResults(results) {
  console.log('\n\n🎯 TEST RESULTS SUMMARY');
  console.log('======================');
  
  console.log('\n📊 Model Tests:');
  Object.entries(results.modelTests).forEach(([test, result]) => {
    const icon = result === 'PASS' ? '✅' : result === 'FAIL' ? '❌' : '⚠️';
    console.log(`   ${icon} ${test}: ${result}`);
  });
  
  console.log('\n🌐 API Tests:');
  Object.entries(results.apiTests).forEach(([test, result]) => {
    const icon = result === 'PASS' ? '✅' : result === 'FAIL' ? '❌' : '⚠️';
    console.log(`   ${icon} ${test}: ${result}`);
  });
  
  console.log('\n' + '='.repeat(50));
  
  if (results.overallPass) {
    console.log('🎉 OVERALL RESULT: ALL TESTS PASSED');
    console.log('🚀 The tracking feature is working correctly!');
  } else {
    console.log('⚠️  OVERALL RESULT: SOME TESTS FAILED');
    console.log('🔧 Please check the failed tests above');
  }
  
  console.log('='.repeat(50));
}

// Check if we need to test with a running server
const args = process.argv.slice(2);
const skipApiTests = args.includes('--model-only');

if (skipApiTests) {
  console.log('Running model tests only (--model-only flag detected)');
}

testTrackingFeature().catch(console.error);
