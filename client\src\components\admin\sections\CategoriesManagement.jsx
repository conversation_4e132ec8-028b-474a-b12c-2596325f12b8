import React, { useState, useEffect } from 'react';
import {
  Tabs, Card, Button, Form, Input, message, Upload, Modal,
  Table, Switch, InputNumber, Select, Space, Popconfirm,
  Image, Tag, Tooltip
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, UploadOutlined,
  PictureOutlined, SettingOutlined, BankOutlined, AppstoreOutlined
} from '@ant-design/icons';
import { homepageSettingsApi, categoriesApi } from '../../../services/adminApi';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;


const HomepageSettings = () => {
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('carousel');

  // Modal states
  const [carouselModalVisible, setCarouselModalVisible] = useState(false);
  const [promotionModalVisible, setPromotionModalVisible] = useState(false);
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);

  // Form states
  const [carouselForm] = Form.useForm();
  const [promotionForm] = Form.useForm();
  const [settingsForm] = Form.useForm();

  // Edit states
  const [editingCarousel, setEditingCarousel] = useState(null);
  const [editingPromotion, setEditingPromotion] = useState(null);

  // Featured Categories state
  const [allCategories, setAllCategories] = useState([]);
  const [categoriesLoading, setCategoriesLoading] = useState(false);

  useEffect(() => {
    fetchSettings();
    fetchAllCategories();
  }, []);

  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await homepageSettingsApi.getSettings();
      if (response.data.success) {
        setSettings(response.data.data);
        // Initialize settings form with current values
        settingsForm.setFieldsValue({
          autoPlayCarousel: response.data.data.settings?.autoPlayCarousel,
          carouselSpeed: response.data.data.settings?.carouselSpeed,
          showPromotions: response.data.data.settings?.showPromotions,
          maxCarouselImages: response.data.data.settings?.maxCarouselImages,
          maxPromotionImages: response.data.data.settings?.maxPromotionImages
        });
      }
    } catch (error) {
      message.error('Failed to fetch homepage settings');
    } finally {
      setLoading(false);
    }
  };

  const fetchAllCategories = async () => {
    setCategoriesLoading(true);
    try {
      const response = await categoriesApi.getAllCategories();
      setAllCategories(response.data.data.categories || []);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      message.error('Failed to fetch categories');
    } finally {
      setCategoriesLoading(false);
    }
  };

  // Carousel handlers
  const handleAddCarousel = () => {
    setEditingCarousel(null);
    carouselForm.resetFields();
    setCarouselModalVisible(true);
  };

  const handleEditCarousel = (carousel) => {
    setEditingCarousel(carousel);
    carouselForm.setFieldsValue({
      title: carousel.title,
      description: carousel.description,
      linkUrl: carousel.linkUrl,
      isActive: carousel.isActive,
      sortOrder: carousel.sortOrder
    });
    setCarouselModalVisible(true);
  };

  const handleDeleteCarousel = async (imageId) => {
    setLoading(true);
    try {
      await homepageSettingsApi.deleteCarouselImage(imageId);
      message.success('Carousel image deleted successfully');
      fetchSettings();
    } catch (error) {
      message.error('Failed to delete carousel image');
    } finally {
      setLoading(false);
    }
  };

  const handleCarouselSubmit = async () => {
    try {
      const values = await carouselForm.validateFields();
      setLoading(true);

      const formData = new FormData();
      formData.append('title', values.title || '');
      formData.append('description', values.description || '');
      formData.append('linkUrl', values.linkUrl || '');

      // Handle image file properly
      if (values.image && values.image.length > 0) {
        const file = values.image[0];
        const imageFile = file.originFileObj || file;
        formData.append('image', imageFile);
      } else if (!editingCarousel) {
        message.error('Please select an image file');
        setLoading(false);
        return;
      }

      if (editingCarousel) {
        formData.append('isActive', values.isActive);
        formData.append('sortOrder', values.sortOrder);
        await homepageSettingsApi.updateCarouselImage(editingCarousel._id, formData);
        message.success('Carousel image updated successfully');
      } else {
        await homepageSettingsApi.addCarouselImage(formData);
        message.success('Carousel image added successfully');
      }

      setCarouselModalVisible(false);
      carouselForm.resetFields();
      fetchSettings();
    } catch (error) {
      console.error('Carousel upload error:', error);
      message.error('Failed to save carousel image');
    } finally {
      setLoading(false);
    }
  };

  // Promotion handlers
  const handleAddPromotion = () => {
    setEditingPromotion(null);
    promotionForm.resetFields();
    setPromotionModalVisible(true);
  };

  const handleEditPromotion = (promotion) => {
    setEditingPromotion(promotion);
    promotionForm.setFieldsValue({
      title: promotion.title,
      description: promotion.description,
      linkUrl: promotion.linkUrl,
      position: promotion.position,
      isActive: promotion.isActive,
      sortOrder: promotion.sortOrder,
      dateRange: promotion.startDate && promotion.endDate ?
        [dayjs(promotion.startDate), dayjs(promotion.endDate)] : null
    });
    setPromotionModalVisible(true);
  };

  const handleDeletePromotion = async (imageId) => {
    setLoading(true);
    try {
      await homepageSettingsApi.deletePromotionImage(imageId);
      message.success('Promotion image deleted successfully');
      fetchSettings();
    } catch (error) {
      message.error('Failed to delete promotion image');
    } finally {
      setLoading(false);
    }
  };

  const handlePromotionSubmit = async () => {
    try {
      const values = await promotionForm.validateFields();
      setLoading(true);

      const formData = new FormData();
      formData.append('title', values.title || '');
      formData.append('position', 'sidebar'); // Default to sidebar

      // Handle image file properly
      if (values.image && values.image.length > 0) {
        const file = values.image[0];
        const imageFile = file.originFileObj || file;
        formData.append('image', imageFile);
      } else if (!editingPromotion) {
        message.error('Please select an image file');
        setLoading(false);
        return;
      }

      if (editingPromotion) {
        formData.append('isActive', values.isActive);
        formData.append('sortOrder', values.sortOrder);
        await homepageSettingsApi.updatePromotionImage(editingPromotion._id, formData);
        message.success('Promotion image updated successfully');
      } else {
        await homepageSettingsApi.addPromotionImage(formData);
        message.success('Promotion image added successfully');
      }

      setPromotionModalVisible(false);
      promotionForm.resetFields();
      fetchSettings();
    } catch (error) {
      console.error('Promotion upload error:', error);
      message.error('Failed to save promotion image');
    } finally {
      setLoading(false);
    }
  };

  // Settings handlers
  const handleSettingsSubmit = async () => {
    try {
      const values = await settingsForm.validateFields();
      setLoading(true);

      await homepageSettingsApi.updateGeneralSettings(values);
      message.success('Settings updated successfully');
      setSettingsModalVisible(false);
      fetchSettings();
    } catch (error) {
      message.error('Failed to update settings');
    } finally {
      setLoading(false);
    }
  };

  // Featured Category handlers
  const handleFeaturedCategoriesChange = (selectedCategoryIds) => {
    // Update the settings state with selected category IDs
    setSettings(prev => ({
      ...prev,
      featuredCategoryIds: selectedCategoryIds
    }));
  };

  const handleSaveFeaturedCategories = async () => {
    try {
      setLoading(true);
      const featuredCategoryIds = settings.featuredCategoryIds || [];

      await homepageSettingsApi.updateGeneralSettings({
        featuredCategoryIds: featuredCategoryIds
      });

      message.success('Featured categories updated successfully');
      fetchSettings();
    } catch (error) {
      console.error('Error saving featured categories:', error);
      message.error('Failed to save featured categories');
    } finally {
      setLoading(false);
    }
  };

  // Table columns for carousel images
  const carouselColumns = [
    {
      title: 'Image',
      dataIndex: 'imageUrl',
      key: 'imageUrl',
      width: 100,
      render: (imageUrl) => (
        <Image
          width={60}
          height={40}
          src={imageUrl}
          style={{ objectFit: 'cover', borderRadius: 4 }}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
        />
      ),
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      render: (title) => title || '-',
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Sort Order',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 100,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditCarousel(record)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this carousel image?"
            onConfirm={() => handleDeleteCarousel(record._id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // Table columns for promotion images
  const promotionColumns = [
    {
      title: 'Image',
      dataIndex: 'imageUrl',
      key: 'imageUrl',
      width: 100,
      render: (imageUrl) => (
        <Image
          width={60}
          height={40}
          src={imageUrl}
          style={{ objectFit: 'cover', borderRadius: 4 }}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
        />
      ),
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      render: (title) => title || '-',
    },
    {
      title: 'Position',
      dataIndex: 'position',
      key: 'position',
      render: (position) => (
        <Tag color="blue">{position}</Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Active' : 'Inactive'}
        </Tag>
      ),
    },
    {
      title: 'Duration',
      key: 'duration',
      render: (_, record) => {
        if (record.startDate && record.endDate) {
          return (
            <div>
              <div>{dayjs(record.startDate).format('MMM DD, YYYY')}</div>
              <div>{dayjs(record.endDate).format('MMM DD, YYYY')}</div>
            </div>
          );
        }
        return '-';
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditPromotion(record)}
            />
          </Tooltip>
          <Popconfirm
            title="Are you sure you want to delete this promotion image?"
            onConfirm={() => handleDeletePromotion(record._id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];



  if (!settings) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          tabBarExtraContent={
            <Button
              type="primary"
              icon={<SettingOutlined />}
              onClick={() => setSettingsModalVisible(true)}
            >
              General Settings
            </Button>
          }
        >
          <TabPane tab={<span><PictureOutlined />Carousel Images</span>} key="carousel">
            <div style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddCarousel}
                disabled={settings.carouselImages?.length >= (settings.settings?.maxCarouselImages || 10)}
              >
                Add Carousel Image
              </Button>
              {settings.carouselImages?.length >= (settings.settings?.maxCarouselImages || 10) && (
                <span style={{ marginLeft: 8, color: '#ff4d4f' }}>
                  Maximum {settings.settings?.maxCarouselImages || 10} images allowed
                </span>
              )}
            </div>
            <Table
              columns={carouselColumns}
              dataSource={settings.carouselImages || []}
              rowKey="_id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab={<span><BankOutlined />Promotion Images</span>} key="promotions">
            <div style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddPromotion}
                disabled={settings.promotionImages?.length >= (settings.settings?.maxPromotionImages || 5)}
              >
                Add Promotion Image
              </Button>
              {settings.promotionImages?.length >= (settings.settings?.maxPromotionImages || 5) && (
                <span style={{ marginLeft: 8, color: '#ff4d4f' }}>
                  Maximum {settings.settings?.maxPromotionImages || 5} images allowed
                </span>
              )}
            </div>
            <Table
              columns={promotionColumns}
              dataSource={settings.promotionImages || []}
              rowKey="_id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>

          <TabPane tab={<span><AppstoreOutlined />Featured Categories</span>} key="categories">
            <div style={{ marginBottom: 16 }}>
              <h4>Select up to 5 categories to display on homepage</h4>
              <p style={{ color: '#666', marginBottom: 16 }}>
                Choose which categories should appear in the homepage sidebar menu.
              </p>
            </div>

            <Form layout="vertical">
              <Form.Item label="Featured Categories">
                <Select
                  mode="multiple"
                  placeholder="Select categories (max 5)"
                  value={settings.featuredCategoryIds || []}
                  onChange={handleFeaturedCategoriesChange}
                  maxCount={5}
                  style={{ width: '100%' }}
                  loading={categoriesLoading}
                >
                  {allCategories.map(category => (
                    <Option key={category._id} value={category._id}>
                      {category.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Button
                type="primary"
                onClick={handleSaveFeaturedCategories}
                loading={loading}
              >
                Save Featured Categories
              </Button>
            </Form>
          </TabPane>
        </Tabs>
      </Card>

      {/* Carousel Modal */}
      <Modal
        title={editingCarousel ? 'Edit Carousel Image' : 'Add Carousel Image'}
        open={carouselModalVisible}
        onOk={handleCarouselSubmit}
        onCancel={() => setCarouselModalVisible(false)}
        confirmLoading={loading}
        width={600}
      >
        <Form form={carouselForm} layout="vertical">
          <Form.Item
            name="image"
            label="Image"
            rules={editingCarousel ? [] : [{ required: true, message: 'Please upload an image' }]}
          >
            <Upload
              listType="picture-card"
              maxCount={1}
              beforeUpload={() => false}
              accept="image/*"
            >
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>Upload</div>
              </div>
            </Upload>
          </Form.Item>

          <Form.Item name="title" label="Title">
            <Input placeholder="Enter carousel title" />
          </Form.Item>

          <Form.Item name="description" label="Description">
            <TextArea rows={3} placeholder="Enter carousel description" />
          </Form.Item>

          <Form.Item name="linkUrl" label="Link URL">
            <Input placeholder="Enter link URL (optional)" />
          </Form.Item>

          {editingCarousel && (
            <>
              <Form.Item name="isActive" label="Active" valuePropName="checked">
                <Switch />
              </Form.Item>

              <Form.Item name="sortOrder" label="Sort Order">
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </>
          )}
        </Form>
      </Modal>

      {/* Promotion Modal */}
      <Modal
        title={editingPromotion ? 'Edit Promotion Image' : 'Add Promotion Image'}
        open={promotionModalVisible}
        onOk={handlePromotionSubmit}
        onCancel={() => setPromotionModalVisible(false)}
        confirmLoading={loading}
        width={600}
      >
        <Form form={promotionForm} layout="vertical">
          <Form.Item
            name="image"
            label="Image"
            rules={editingPromotion ? [] : [{ required: true, message: 'Please upload an image' }]}
          >
            <Upload
              listType="picture-card"
              maxCount={1}
              beforeUpload={() => false}
              accept="image/*"
            >
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>Upload</div>
              </div>
            </Upload>
          </Form.Item>

          <Form.Item
            name="title"
            label="Title"
            rules={[{ required: true, message: 'Please enter a title' }]}
          >
            <Input placeholder="Enter promotion title" />
          </Form.Item>

          {editingPromotion && (
            <>
              <Form.Item name="isActive" label="Active" valuePropName="checked">
                <Switch />
              </Form.Item>

              <Form.Item name="sortOrder" label="Sort Order">
                <InputNumber min={0} style={{ width: '100%' }} />
              </Form.Item>
            </>
          )}
        </Form>
      </Modal>

      {/* Settings Modal */}
      <Modal
        title="General Settings"
        open={settingsModalVisible}
        onOk={handleSettingsSubmit}
        onCancel={() => setSettingsModalVisible(false)}
        confirmLoading={loading}
        width={500}
      >
        <Form form={settingsForm} layout="vertical">
          <Form.Item name="autoPlayCarousel" label="Auto Play Carousel" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item name="carouselSpeed" label="Carousel Speed (ms)">
            <InputNumber min={1000} max={10000} step={500} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name="showPromotions" label="Show Promotions" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item name="maxCarouselImages" label="Max Carousel Images">
            <InputNumber min={1} max={20} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name="maxPromotionImages" label="Max Promotion Images">
            <InputNumber min={1} max={10} style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>


    </div>
  );
};

export default HomepageSettings;

