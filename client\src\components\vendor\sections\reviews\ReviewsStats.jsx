import React from 'react';
import { Card, Row, Col, Statistic, Progress, Space, Typography } from 'antd';
import { StarFilled, MessageOutlined } from '@ant-design/icons';

const { Text } = Typography;

const ReviewsStats = ({ stats }) => {
  const {
    totalReviews = 0,
    averageRating = 0,
    repliedReviews = 0,
    pendingReplies = 0,
    ratingDistribution = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
  } = stats;

  return (
    <>
      {/* Main Statistics Cards */}
      <Row gutter={16} className="mb-6">
        <Col xs={12} sm={6}>
          <Card className="rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300 hover:-translate-y-1">
            <Statistic
              title="Total Reviews"
              value={totalReviews}
              prefix={<StarFilled className="text-yellow-500" />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300 hover:-translate-y-1">
            <Statistic
              title="Average Rating"
              value={averageRating.toFixed(1)}
              suffix="/ 5"
              prefix={<StarFilled className="text-yellow-500" />}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300 hover:-translate-y-1">
            <Statistic
              title="Replied Reviews"
              value={repliedReviews}
              prefix={<MessageOutlined className="text-green-500" />}
              valueStyle={{ color: '#10b981' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card className="rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300 hover:-translate-y-1">
            <Statistic
              title="Pending Replies"
              value={pendingReplies}
              prefix={<MessageOutlined className="text-red-500" />}
              valueStyle={{ color: '#ef4444' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Rating Distribution */}
      <Card 
        className="mb-6 rounded-xl shadow-sm border border-gray-200" 
        title={<span className="text-lg font-semibold text-gray-800">Rating Distribution</span>}
      >
        <Row gutter={16}>
          {[5, 4, 3, 2, 1].map(rating => {
            const count = ratingDistribution[rating] || 0;
            const percentage = totalReviews > 0 ? (count / totalReviews) * 100 : 0;
            
            return (
              <Col key={rating} xs={24} sm={12} md={8} lg={4} className="mb-4">
                <div className="flex flex-col gap-2 p-3 bg-gray-50 rounded-lg">
                  <Space className="justify-between">
                    <div className="flex items-center gap-1">
                      <Text className="font-medium">{rating}</Text>
                      <StarFilled className="text-yellow-500" />
                    </div>
                  </Space>
                  <Progress
                    percent={percentage}
                    size="small"
                    strokeColor={rating >= 4 ? '#10b981' : rating >= 3 ? '#f59e0b' : '#ef4444'}
                    showInfo={false}
                    className="mb-1"
                  />
                  <Text type="secondary" className="text-sm">
                    {count} ({percentage.toFixed(0)}%)
                  </Text>
                </div>
              </Col>
            );
          })}
        </Row>
      </Card>
    </>
  );
};

export default ReviewsStats;
