const axios = require('axios');

// Configuration
const API_BASE_URL = 'https://multi-vendor-server-1tb9.onrender.com/api';

/**
 * Test vendor dashboard API endpoints
 */
async function testVendorDashboard() {
  console.log('🔍 Testing Vendor Dashboard API Endpoints...\n');

  // Test 1: Check if the API server is running
  console.log('1. Testing API Server Health...');
  try {
    const healthResponse = await axios.get(`${API_BASE_URL}/health`, {
      timeout: 10000
    });
    console.log('   ✅ API Server is running');
    console.log(`   📊 Response: ${JSON.stringify(healthResponse.data, null, 2)}`);
  } catch (error) {
    console.log('   ❌ API Server health check failed');
    console.log(`   🔴 Error: ${error.message}`);
    if (error.code === 'ECONNREFUSED') {
      console.log('   💡 Suggestion: Make sure the server is running on the correct port');
    }
  }

  console.log('\n' + '='.repeat(50) + '\n');

  // Test 2: Test vendor authentication endpoints
  console.log('2. Testing Vendor Authentication...');
  
  // You'll need to provide actual vendor credentials for testing
  const testCredentials = {
    email: '<EMAIL>', // Replace with actual vendor email
    password: 'password123'      // Replace with actual vendor password
  };

  try {
    const authResponse = await axios.post(`${API_BASE_URL}/auth/login`, testCredentials, {
      timeout: 10000
    });
    
    if (authResponse.data.success) {
      console.log('   ✅ Vendor authentication successful');
      const token = authResponse.data.token;
      const userType = authResponse.data.user?.userType;
      
      console.log(`   👤 User Type: ${userType}`);
      console.log(`   🔑 Token received: ${token ? 'Yes' : 'No'}`);

      if (userType === 'vendor' && token) {
        console.log('   🎯 Proceeding to test dashboard endpoints...');
        await testDashboardEndpoints(token);
      } else {
        console.log('   ⚠️  User is not a vendor or token missing');
      }
    } else {
      console.log('   ❌ Authentication failed');
      console.log(`   📝 Message: ${authResponse.data.message}`);
    }
  } catch (error) {
    console.log('   ❌ Authentication request failed');
    console.log(`   🔴 Error: ${error.message}`);
    if (error.response) {
      console.log(`   📄 Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    console.log('\n   💡 Suggestions:');
    console.log('   - Check if vendor credentials are correct');
    console.log('   - Verify the vendor account exists in the database');
    console.log('   - Ensure the auth endpoint is working properly');
  }
}

/**
 * Test dashboard specific endpoints with authentication
 */
async function testDashboardEndpoints(token) {
  console.log('\n3. Testing Dashboard Endpoints with Authentication...');

  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  };

  // Test dashboard stats endpoint
  console.log('\n   📊 Testing Dashboard Stats...');
  try {
    const statsResponse = await axios.get(`${API_BASE_URL}/vendor/dashboard/stats`, {
      headers,
      timeout: 15000
    });

    if (statsResponse.data.success) {
      console.log('   ✅ Dashboard stats retrieved successfully');
      console.log(`   📈 Data structure:`);
      const data = statsResponse.data.data;
      console.log(`      - Products: ${data.products?.totalProducts || 0} total, ${data.products?.activeProducts || 0} active`);
      console.log(`      - Orders: ${data.orders?.totalOrders || 0} total, Revenue: ₹${data.orders?.totalRevenue || 0}`);
      console.log(`      - Recent Orders: ${data.recentOrders?.length || 0} found`);
      console.log(`      - Top Products: ${data.topProducts?.length || 0} found`);
    } else {
      console.log('   ❌ Dashboard stats request failed');
      console.log(`   📝 Message: ${statsResponse.data.message}`);
    }
  } catch (error) {
    console.log('   ❌ Dashboard stats request error');
    console.log(`   🔴 Error: ${error.message}`);
    if (error.response) {
      console.log(`   📄 Response: ${JSON.stringify(error.response.data, null, 2)}`);
      
      if (error.response.status === 404) {
        console.log('   💡 Suggestion: Vendor record might not exist or endpoint not found');
      } else if (error.response.status === 401) {
        console.log('   💡 Suggestion: Authentication token might be invalid or expired');
      } else if (error.response.status === 500) {
        console.log('   💡 Suggestion: Server error - check backend logs');
      }
    }
  }

  // Test analytics endpoint
  console.log('\n   📈 Testing Analytics Endpoint...');
  try {
    const analyticsResponse = await axios.get(`${API_BASE_URL}/vendor/analytics`, {
      headers,
      params: { period: '30d', type: 'revenue' },
      timeout: 15000
    });

    if (analyticsResponse.data.success) {
      console.log('   ✅ Analytics data retrieved successfully');
      const data = analyticsResponse.data.data;
      console.log(`   📊 Analytics: ${data.analytics?.length || 0} data points for ${data.period} period`);
    } else {
      console.log('   ❌ Analytics request failed');
      console.log(`   📝 Message: ${analyticsResponse.data.message}`);
    }
  } catch (error) {
    console.log('   ❌ Analytics request error');
    console.log(`   🔴 Error: ${error.message}`);
    if (error.response) {
      console.log(`   📄 Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }

  // Test vendor test endpoint
  console.log('\n   🧪 Testing Vendor Authentication Test Endpoint...');
  try {
    const testResponse = await axios.get(`${API_BASE_URL}/vendor/dashboard/test`, {
      headers,
      timeout: 10000
    });

    if (testResponse.data.success) {
      console.log('   ✅ Vendor authentication test passed');
      const user = testResponse.data.user;
      console.log(`   👤 User ID: ${user.userId}`);
      console.log(`   🏪 Vendor: ${user.vendor ? user.vendor.businessName : 'Not found'}`);
      console.log(`   📊 Status: ${user.vendor ? user.vendor.status : 'N/A'}`);
    } else {
      console.log('   ❌ Vendor authentication test failed');
    }
  } catch (error) {
    console.log('   ❌ Vendor test endpoint error');
    console.log(`   🔴 Error: ${error.message}`);
    if (error.response) {
      console.log(`   📄 Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

/**
 * Test network connectivity and CORS
 */
async function testNetworkAndCORS() {
  console.log('\n4. Testing Network Connectivity and CORS...');

  try {
    // Test with different methods to check CORS
    const corsTestResponse = await axios.options(`${API_BASE_URL}/vendor/dashboard/stats`, {
      timeout: 10000
    });
    console.log('   ✅ CORS preflight check passed');
  } catch (error) {
    console.log('   ⚠️  CORS preflight check failed or not supported');
    console.log(`   🔴 Error: ${error.message}`);
  }

  // Test response times
  console.log('\n   ⏱️  Testing Response Times...');
  const startTime = Date.now();
  try {
    await axios.get(`${API_BASE_URL}/health`, { timeout: 5000 });
    const responseTime = Date.now() - startTime;
    console.log(`   📊 Response time: ${responseTime}ms`);
    
    if (responseTime > 3000) {
      console.log('   ⚠️  Slow response time detected');
      console.log('   💡 Suggestion: Server might be under heavy load or network issues');
    }
  } catch (error) {
    console.log('   ❌ Response time test failed');
  }
}

/**
 * Provide troubleshooting suggestions
 */
function provideTroubleshootingSuggestions() {
  console.log('\n' + '='.repeat(50));
  console.log('🔧 TROUBLESHOOTING SUGGESTIONS');
  console.log('='.repeat(50) + '\n');

  console.log('If the dashboard is showing loading spinner continuously:\n');

  console.log('1. 🌐 API CONNECTIVITY ISSUES:');
  console.log('   - Check if the server is running');
  console.log('   - Verify the API_BASE_URL in the client');
  console.log('   - Test network connectivity');
  console.log('   - Check CORS configuration\n');

  console.log('2. 🔐 AUTHENTICATION ISSUES:');
  console.log('   - Verify vendor account exists and is approved');
  console.log('   - Check if the authentication token is valid');
  console.log('   - Ensure userType is set to "vendor"');
  console.log('   - Clear localStorage and re-login\n');

  console.log('3. 🗄️  DATABASE ISSUES:');
  console.log('   - Check MongoDB connection');
  console.log('   - Verify vendor record exists in database');
  console.log('   - Check if products/orders data exists');
  console.log('   - Ensure database indexes are created\n');

  console.log('4. 🐛 CODE ISSUES:');
  console.log('   - Check browser console for JavaScript errors');
  console.log('   - Verify API endpoints in vendorApi.js');
  console.log('   - Check error handling in ResponsiveVendorDashboard.jsx');
  console.log('   - Ensure proper loading state management\n');

  console.log('5. 🔄 CACHE ISSUES:');
  console.log('   - Clear browser cache');
  console.log('   - Disable cache in developer tools');
  console.log('   - Force refresh the dashboard (Ctrl+F5)\n');

  console.log('6. 🏃‍♂️ IMMEDIATE FIXES TO TRY:');
  console.log('   - Add console.log statements in ResponsiveVendorDashboard.jsx');
  console.log('   - Check Network tab in browser developer tools');
  console.log('   - Test API endpoints directly in Postman/Insomnia');
  console.log('   - Check server logs for errors');
  console.log('   - Verify environment variables are set correctly');
}

// Run the tests
console.log('🚀 VENDOR DASHBOARD DEBUG TOOL');
console.log('='.repeat(50));

testVendorDashboard()
  .then(() => {
    return testNetworkAndCORS();
  })
  .then(() => {
    provideTroubleshootingSuggestions();
  })
  .catch((error) => {
    console.error('\n❌ Debug script error:', error.message);
  })
  .finally(() => {
    console.log('\n✅ Debug script completed.');
    console.log('📧 If issues persist, check the server logs and client browser console.');
  });
