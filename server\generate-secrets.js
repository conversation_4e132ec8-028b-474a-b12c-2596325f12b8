/**
 * Secret Generator for JWT and Session
 * Run with: node generate-secrets.js
 */

const crypto = require('crypto');

function generateSecret(length = 64) {
    return crypto.randomBytes(length).toString('hex');
}

function generateBase64Secret(length = 64) {
    return crypto.randomBytes(length).toString('base64');
}

console.log('🔐 Generated Secrets for Your Application\n');
console.log('Copy these to your .env file:\n');

console.log('# JWT Configuration');
console.log(`JWT_SECRET=${generateSecret(64)}`);
console.log(`JWT_REFRESH_SECRET=${generateSecret(64)}`);
console.log(`SESSION_SECRET=${generateSecret(64)}`);

console.log('\n# Alternative Base64 Secrets (choose one format)');
console.log(`# JWT_SECRET=${generateBase64Secret(48)}`);
console.log(`# JWT_REFRESH_SECRET=${generateBase64Secret(48)}`);
console.log(`# SESSION_SECRET=${generateBase64Secret(48)}`);

console.log('\n# Token Expiration (recommended values)');
console.log('JWT_EXPIRES_IN=7d');
console.log('JWT_REFRESH_EXPIRES_IN=30d');

console.log('\n🔒 Security Notes:');
console.log('- Each secret should be unique');
console.log('- Never commit these secrets to version control');
console.log('- Use different secrets for development and production');
console.log('- Store production secrets securely (environment variables)');
console.log('- Rotate secrets periodically in production');

console.log('\n📝 Quick Setup:');
console.log('1. Copy the generated secrets above');
console.log('2. Paste them into your .env file');
console.log('3. Remove the example secrets');
console.log('4. Restart your server');