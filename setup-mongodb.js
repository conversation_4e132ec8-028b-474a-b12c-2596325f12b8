#!/usr/bin/env node

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 MongoDB Setup Helper\n');

// Check if MongoDB is installed
function checkMongoInstallation() {
    return new Promise((resolve) => {
        exec('mongod --version', (error, stdout, stderr) => {
            if (error) {
                console.log('❌ MongoDB is not installed or not in PATH');
                resolve(false);
            } else {
                console.log('✅ MongoDB is installed');
                console.log('📋 Version:', stdout.split('\n')[0]);
                resolve(true);
            }
        });
    });
}

// Check if MongoDB service is running
function checkMongoService() {
    return new Promise((resolve) => {
        exec('mongo --eval "db.runCommand({ connectionStatus: 1 })"', (error, stdout, stderr) => {
            if (error) {
                console.log('⚠️ MongoDB service is not running');
                resolve(false);
            } else {
                console.log('✅ MongoDB service is running');
                resolve(true);
            }
        });
    });
}

// Start MongoDB service
function startMongoService() {
    return new Promise((resolve) => {
        console.log('🔄 Attempting to start MongoDB service...');
        
        // Try different methods to start MongoDB
        const commands = [
            'net start MongoDB',  // Windows service
            'brew services start mongodb/brew/mongodb-community',  // macOS with Homebrew
            'sudo systemctl start mongod',  // Linux systemd
            'sudo service mongod start'  // Linux service
        ];
        
        let commandIndex = 0;
        
        function tryNextCommand() {
            if (commandIndex >= commands.length) {
                console.log('❌ Could not start MongoDB service automatically');
                console.log('💡 Please start MongoDB manually');
                resolve(false);
                return;
            }
            
            exec(commands[commandIndex], (error, stdout, stderr) => {
                if (error) {
                    commandIndex++;
                    tryNextCommand();
                } else {
                    console.log('✅ MongoDB service started successfully');
                    resolve(true);
                }
            });
        }
        
        tryNextCommand();
    });
}

// Create MongoDB data directory
function createDataDirectory() {
    const dataDir = path.join(process.cwd(), 'mongodb-data');
    
    if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
        console.log('📁 Created MongoDB data directory:', dataDir);
    } else {
        console.log('📁 MongoDB data directory already exists:', dataDir);
    }
    
    return dataDir;
}

// Start MongoDB manually with custom data directory
function startMongoManually() {
    const dataDir = createDataDirectory();
    
    return new Promise((resolve) => {
        console.log('🔄 Starting MongoDB with custom data directory...');
        
        const mongodCommand = `mongod --dbpath "${dataDir}" --port 27017`;
        
        console.log('💡 Run this command in a separate terminal:');
        console.log(`   ${mongodCommand}`);
        console.log('');
        console.log('Or on Windows, you can also try:');
        console.log('   mongod --dbpath "mongodb-data"');
        
        resolve(true);
    });
}

// Main setup function
async function setupMongoDB() {
    console.log('🔍 Checking MongoDB installation...\n');
    
    const isInstalled = await checkMongoInstallation();
    
    if (!isInstalled) {
        console.log('\n📥 MongoDB Installation Instructions:');
        console.log('');
        console.log('🪟 Windows:');
        console.log('   1. Download from: https://www.mongodb.com/try/download/community');
        console.log('   2. Run the installer and follow the setup wizard');
        console.log('   3. Make sure to install MongoDB as a service');
        console.log('');
        console.log('🍎 macOS:');
        console.log('   brew tap mongodb/brew');
        console.log('   brew install mongodb-community');
        console.log('');
        console.log('🐧 Linux (Ubuntu/Debian):');
        console.log('   sudo apt update');
        console.log('   sudo apt install mongodb');
        console.log('');
        return;
    }
    
    console.log('\n🔍 Checking if MongoDB service is running...\n');
    
    const isRunning = await checkMongoService();
    
    if (!isRunning) {
        console.log('\n🔄 Attempting to start MongoDB service...\n');
        
        const started = await startMongoService();
        
        if (!started) {
            console.log('\n🛠️ Manual MongoDB Start Instructions:');
            await startMongoManually();
        }
    }
    
    console.log('\n✅ MongoDB setup complete!');
    console.log('🔗 Your application should now be able to connect to: mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('\n🚀 You can now restart your server with: npm run dev');
}

// Run the setup
setupMongoDB().catch(console.error);