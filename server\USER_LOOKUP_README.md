# User Lookup Scripts

Two scripts are available in the server directory to lookup user details:

## 1. Command Line Lookup
**File:** `user-lookup.js`
**Usage:** `node user-lookup.js <email>`

```bash
cd server
node user-lookup.js <EMAIL>
```

## 2. Interactive Search
**File:** `user-search.js`
**Usage:** `node user-search.js`

```bash
cd server
node user-search.js
```

Then enter emails when prompted. Type 'quit' to exit.

## Example Output

```
✅ Connected to database
🔍 Looking up user: <EMAIL>

📋 USER DETAILS:
================
ID: 507f1f77bcf86cd799439011
Email: <EMAIL>
Type: user
Role: customer
Name: John Doe
Status: Active
Blocked: No
Email Verified: Yes
Created: 1/15/2024
Last Active: 1/20/2024
```

Both scripts use the existing dependencies in the server directory, so no additional installation is required.