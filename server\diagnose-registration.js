/**
 * Comprehensive diagnostic script for registration endpoint
 * Run with: node diagnose-registration.js
 */

const axios = require('axios');
require('dotenv').config();

const BASE_URL = 'http://localhost:5000/api';

// Test cases for different scenarios
const testCases = [
    {
        name: 'Valid User Registration',
        data: {
            firstName: 'John',
            lastName: 'Doe',
            email: `john.doe.${Date.now()}@example.com`,
            password: 'SecurePassword123',
            userType: 'user'
        },
        expectedStatus: 201,
        shouldSucceed: true
    },
    {
        name: 'Valid Vendor Registration',
        data: {
            businessName: 'Acme Corporation',
            businessType: 'retail',
            contactPerson: '<PERSON>',
            email: `acme.corp.${Date.now()}@example.com`,
            password: 'SecurePassword123',
            userType: 'vendor'
        },
        expectedStatus: 201,
        shouldSucceed: true
    },
    {
        name: 'Missing Required User Fields',
        data: {
            email: `incomplete.user.${Date.now()}@example.com`,
            password: 'SecurePassword123',
            userType: 'user'
            // Missing firstName, lastName
        },
        expectedStatus: 400,
        shouldSucceed: false
    },
    {
        name: 'Missing Required Vendor Fields',
        data: {
            email: `incomplete.vendor.${Date.now()}@example.com`,
            password: 'SecurePassword123',
            userType: 'vendor'
            // Missing businessName, businessType, contactPerson
        },
        expectedStatus: 400,
        shouldSucceed: false
    },
    {
        name: 'Invalid Email Format',
        data: {
            firstName: 'Test',
            lastName: 'User',
            email: 'invalid-email-format',
            password: 'SecurePassword123',
            userType: 'user'
        },
        expectedStatus: 400,
        shouldSucceed: false
    },
    {
        name: 'Weak Password',
        data: {
            firstName: 'Test',
            lastName: 'User',
            email: `weak.password.${Date.now()}@example.com`,
            password: '123',
            userType: 'user'
        },
        expectedStatus: 400,
        shouldSucceed: false
    },
    {
        name: 'Invalid User Type',
        data: {
            firstName: 'Test',
            lastName: 'User',
            email: `invalid.type.${Date.now()}@example.com`,
            password: 'SecurePassword123',
            userType: 'invalid'
        },
        expectedStatus: 400,
        shouldSucceed: false
    },
    {
        name: 'Invalid Business Type',
        data: {
            businessName: 'Test Business',
            businessType: 'invalid',
            contactPerson: 'Test Person',
            email: `invalid.business.${Date.now()}@example.com`,
            password: 'SecurePassword123',
            userType: 'vendor'
        },
        expectedStatus: 400,
        shouldSucceed: false
    }
];

async function testRegistrationEndpoint(testCase) {
    try {
        console.log(`\n🧪 Testing: ${testCase.name}`);
        console.log(`   Expected: ${testCase.shouldSucceed ? 'SUCCESS' : 'FAILURE'} (${testCase.expectedStatus})`);
        
        const response = await axios.post(`${BASE_URL}/auth/register`, testCase.data, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const success = response.status === testCase.expectedStatus && testCase.shouldSucceed;
        console.log(`   ${success ? '✅' : '⚠️'} Status: ${response.status} - ${response.data.message}`);
        
        if (response.data.data) {
            console.log(`   📧 Email: ${response.data.data.user.email}`);
            console.log(`   👤 Type: ${response.data.data.user.userType}`);
            console.log(`   🔑 Token: ${response.data.data.token ? 'Generated' : 'Missing'}`);
        }

        return {
            testCase: testCase.name,
            passed: success,
            status: response.status,
            message: response.data.message,
            data: response.data
        };

    } catch (error) {
        if (error.response) {
            const success = error.response.status === testCase.expectedStatus && !testCase.shouldSucceed;
            console.log(`   ${success ? '✅' : '❌'} Status: ${error.response.status} - ${error.response.data.message}`);
            
            if (error.response.data.errors) {
                console.log(`   📝 Validation Errors:`);
                error.response.data.errors.forEach(err => {
                    console.log(`      - ${err.field}: ${err.message}`);
                });
            }

            return {
                testCase: testCase.name,
                passed: success,
                status: error.response.status,
                message: error.response.data.message,
                errors: error.response.data.errors
            };
        } else {
            console.log(`   ❌ Network Error: ${error.message}`);
            return {
                testCase: testCase.name,
                passed: false,
                error: error.message
            };
        }
    }
}

async function checkServerHealth() {
    try {
        console.log('🏥 Checking server health...');
        const response = await axios.get(`${BASE_URL}/auth/health`);
        console.log(`✅ Server is healthy: ${response.data.message}`);
        return true;
    } catch (error) {
        console.log(`❌ Server health check failed: ${error.message}`);
        return false;
    }
}

async function checkDatabaseConnection() {
    try {
        console.log('\n🗄️ Checking database connection...');
        // Try to make a request that would require database access
        const response = await axios.post(`${BASE_URL}/auth/login`, {
            email: '<EMAIL>',
            password: 'test'
        });
    } catch (error) {
        if (error.response && error.response.status === 401) {
            console.log('✅ Database connection is working (got expected 401 for invalid login)');
            return true;
        } else {
            console.log(`❌ Database connection issue: ${error.message}`);
            return false;
        }
    }
}

async function checkEnvironmentVariables() {
    console.log('\n🔧 Checking environment variables...');
    
    const requiredVars = [
        'MONGODB_URI',
        'JWT_SECRET',
        'JWT_REFRESH_SECRET'
    ];

    const optionalVars = [
        'SMTP_HOST',
        'SMTP_USER',
        'SMTP_PASS',
        'FRONTEND_URL'
    ];

    console.log('📋 Required variables:');
    requiredVars.forEach(varName => {
        const value = process.env[varName];
        console.log(`   ${value ? '✅' : '❌'} ${varName}: ${value ? 'Set' : 'Missing'}`);
    });

    console.log('\n📋 Optional variables:');
    optionalVars.forEach(varName => {
        const value = process.env[varName];
        console.log(`   ${value ? '✅' : '⚠️'} ${varName}: ${value ? 'Set' : 'Not set'}`);
    });
}

async function runDiagnostics() {
    console.log('🔍 Starting Registration Endpoint Diagnostics...\n');

    // Check environment
    checkEnvironmentVariables();

    // Check server health
    const serverHealthy = await checkServerHealth();
    if (!serverHealthy) {
        console.log('\n❌ Cannot proceed with tests - server is not healthy');
        return;
    }

    // Check database
    const dbHealthy = await checkDatabaseConnection();
    if (!dbHealthy) {
        console.log('\n⚠️ Database connection issues detected');
    }

    // Run test cases
    console.log('\n🧪 Running Registration Test Cases...');
    const results = [];

    for (const testCase of testCases) {
        const result = await testRegistrationEndpoint(testCase);
        results.push(result);
        
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Summary
    console.log('\n📊 Test Results Summary:');
    const passed = results.filter(r => r.passed).length;
    const total = results.length;
    
    console.log(`\n✅ Passed: ${passed}/${total} tests`);
    console.log(`❌ Failed: ${total - passed}/${total} tests`);

    if (passed === total) {
        console.log('\n🎉 All tests passed! Registration endpoint is working correctly.');
        console.log('\n💡 The 400 errors you saw earlier are likely due to:');
        console.log('   1. Attempting to register with emails that already exist');
        console.log('   2. Missing required fields in the request');
        console.log('   3. Invalid data format (weak passwords, invalid emails, etc.)');
    } else {
        console.log('\n⚠️ Some tests failed. Check the details above.');
        
        const failedTests = results.filter(r => !r.passed);
        console.log('\n❌ Failed tests:');
        failedTests.forEach(test => {
            console.log(`   - ${test.testCase}: ${test.message || test.error}`);
        });
    }

    console.log('\n🔧 Troubleshooting Tips:');
    console.log('   1. Use unique email addresses for each registration attempt');
    console.log('   2. Ensure all required fields are provided based on userType');
    console.log('   3. Use strong passwords (8+ chars, uppercase, lowercase, number)');
    console.log('   4. Check that email format is valid');
    console.log('   5. Use valid userType values: "user" or "vendor"');
    console.log('   6. For vendors, use valid businessType values');
}

// Main execution
runDiagnostics().catch(error => {
    console.error('\n❌ Diagnostic script error:', error);
    process.exit(1);
});