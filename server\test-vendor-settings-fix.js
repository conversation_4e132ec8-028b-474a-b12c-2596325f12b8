const mongoose = require('mongoose');
const { User, Vendor } = require('./src/models');
require('dotenv').config();

async function testVendorSettingsUpdate() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find a vendor user
    const vendorUser = await User.findOne({ userType: 'vendor' });
    if (!vendorUser) {
      console.log('❌ No vendor user found');
      return;
    }

    console.log('📋 Found vendor user:', vendorUser.email);

    // Find the vendor profile
    const vendor = await Vendor.findOne({ user: vendorUser._id });
    if (!vendor) {
      console.log('❌ No vendor profile found for user');
      return;
    }

    console.log('📋 Found vendor profile:', vendor.businessName);

    // Test 1: Update vendor profile
    console.log('\n🧪 Test 1: Updating vendor profile...');
    const profileUpdateData = {
      businessName: 'Updated Test Business',
      businessDescription: 'Updated business description',
      contactInfo: {
        businessEmail: vendorUser.email,
        businessPhone: '+1234567890',
        website: 'https://updated-website.com'
      },
      businessAddress: {
        street: '123 Updated Street',
        city: 'Updated City',
        state: 'Updated State',
        zipCode: '12345',
        country: 'Updated Country'
      },
      settings: {
        returnPolicy: 'Updated return policy',
        shippingPolicy: 'Updated shipping policy',
        processingTime: 3
      }
    };

    const updatedVendor = await Vendor.findByIdAndUpdate(
      vendor._id,
      { $set: profileUpdateData },
      { new: true, runValidators: true }
    );

    if (updatedVendor) {
      console.log('✅ Vendor profile updated successfully');
      console.log('   - Business Name:', updatedVendor.businessName);
      console.log('   - Business Email:', updatedVendor.contactInfo?.businessEmail);
      console.log('   - Processing Time:', updatedVendor.settings?.processingTime);
    } else {
      console.log('❌ Failed to update vendor profile');
    }

    // Test 2: Update user profile
    console.log('\n🧪 Test 2: Updating user profile...');
    const userUpdateData = {
      firstName: 'Updated First',
      lastName: 'Updated Last',
      phone: '+9876543210',
      address: '456 Updated User Address',
      preferences: {
        language: 'en',
        currency: 'USD'
      }
    };

    const updatedUser = await User.findByIdAndUpdate(
      vendorUser._id,
      { $set: userUpdateData },
      { new: true, runValidators: true }
    );

    if (updatedUser) {
      console.log('✅ User profile updated successfully');
      console.log('   - Name:', `${updatedUser.firstName} ${updatedUser.lastName}`);
      console.log('   - Phone:', updatedUser.phone);
      console.log('   - Currency:', updatedUser.preferences?.currency);
    } else {
      console.log('❌ Failed to update user profile');
    }

    // Test 3: Test vendor settings update
    console.log('\n🧪 Test 3: Updating vendor settings...');
    const settingsUpdateData = {
      storeSettings: {
        autoAcceptOrders: true,
        minimumOrderAmount: 50
      },
      notificationSettings: {
        newOrders: true,
        lowStock: false,
        reviews: true
      }
    };

    const settingsUpdatedVendor = await Vendor.findByIdAndUpdate(
      vendor._id,
      { $set: settingsUpdateData },
      { new: true, runValidators: true }
    );

    if (settingsUpdatedVendor) {
      console.log('✅ Vendor settings updated successfully');
      console.log('   - Auto Accept Orders:', settingsUpdatedVendor.storeSettings?.autoAcceptOrders);
      console.log('   - Minimum Order Amount:', settingsUpdatedVendor.storeSettings?.minimumOrderAmount);
    } else {
      console.log('❌ Failed to update vendor settings');
    }

    // Test 4: Test image upload simulation
    console.log('\n🧪 Test 4: Testing image upload simulation...');
    const imageUpdateData = {
      logo: 'https://res.cloudinary.com/alicartify/image/upload/v1234567890/test-logo.jpg',
      banner: 'https://res.cloudinary.com/alicartify/image/upload/v1234567890/test-banner.jpg'
    };

    const imageUpdatedVendor = await Vendor.findByIdAndUpdate(
      vendor._id,
      { $set: imageUpdateData },
      { new: true, runValidators: true }
    );

    if (imageUpdatedVendor) {
      console.log('✅ Vendor images updated successfully');
      console.log('   - Logo:', imageUpdatedVendor.logo);
      console.log('   - Banner:', imageUpdatedVendor.banner);
    } else {
      console.log('❌ Failed to update vendor images');
    }

    // Test 5: Fetch complete vendor profile with user data
    console.log('\n🧪 Test 5: Fetching complete vendor profile...');
    const completeVendor = await Vendor.findOne({ user: vendorUser._id })
      .populate('user', 'firstName lastName email phone avatar preferences');

    if (completeVendor) {
      console.log('✅ Complete vendor profile fetched successfully');
      console.log('   - Business Name:', completeVendor.businessName);
      console.log('   - User Name:', `${completeVendor.user.firstName} ${completeVendor.user.lastName}`);
      console.log('   - User Email:', completeVendor.user.email);
      console.log('   - Business Address:', completeVendor.businessAddress);
      console.log('   - Settings:', completeVendor.settings);
    } else {
      console.log('❌ Failed to fetch complete vendor profile');
    }

    console.log('\n✅ All tests completed successfully!');

  } catch (error) {
    console.error('❌ Error during testing:', error);
    console.error('Error details:', error.message);
    if (error.errors) {
      console.error('Validation errors:', error.errors);
    }
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testVendorSettingsUpdate();