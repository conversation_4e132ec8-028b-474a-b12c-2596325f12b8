#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Running Component Analysis Now...\n');

try {
    // Run the large components analysis
    console.log('📊 Analyzing large components and test coverage...');
    console.log('='.repeat(80));
    
    execSync('node large-components-test-analysis.js', { 
        stdio: 'inherit',
        cwd: process.cwd()
    });
    
} catch (error) {
    console.error('❌ Error running analysis:', error.message);
    
    // Fallback: manual analysis
    console.log('\n🔄 Running fallback manual analysis...');
    
    const results = [];
    
    // Analyze client directory
    const clientPath = path.join(process.cwd(), 'client', 'src');
    if (fs.existsSync(clientPath)) {
        console.log('📁 Analyzing client/src directory...');
        findLargeFiles(clientPath, results, 'Frontend');
    }
    
    // Analyze server directory
    const serverPath = path.join(process.cwd(), 'server', 'src');
    if (fs.existsSync(serverPath)) {
        console.log('📁 Analyzing server/src directory...');
        findLargeFiles(serverPath, results, 'Backend');
    }
    
    // Display results
    console.log('\n📊 MANUAL ANALYSIS RESULTS:');
    console.log('='.repeat(80));
    
    const largeFiles = results.filter(f => f.lines > 200).sort((a, b) => b.lines - a.lines);
    
    console.log(`Found ${largeFiles.length} large components (>200 lines):`);
    console.log('-'.repeat(80));
    
    largeFiles.forEach((file, index) => {
        console.log(`${index + 1}. ${file.name} (${file.lines} lines) - ${file.type}`);
        console.log(`   Path: ${file.path}`);
    });
    
    if (largeFiles.length === 0) {
        console.log('✅ No components found with more than 200 lines');
    }
}

function findLargeFiles(dirPath, results, type) {
    try {
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                if (!['node_modules', '.git', 'dist', 'build', '__tests__', 'tests'].includes(item)) {
                    findLargeFiles(fullPath, results, type);
                }
            } else if (stat.isFile()) {
                const ext = path.extname(item);
                const validExtensions = ['.js', '.jsx', '.ts', '.tsx'];
                
                if (validExtensions.includes(ext) && !item.includes('.test.') && !item.includes('.spec.')) {
                    try {
                        const content = fs.readFileSync(fullPath, 'utf8');
                        const lines = content.split('\n').length;
                        
                        results.push({
                            name: item,
                            path: path.relative(process.cwd(), fullPath),
                            lines: lines,
                            type: type
                        });
                    } catch (error) {
                        // Skip files that can't be read
                    }
                }
            }
        }
    } catch (error) {
        console.error(`Error analyzing directory ${dirPath}:`, error.message);
    }
}

console.log('\n✅ Analysis completed!');