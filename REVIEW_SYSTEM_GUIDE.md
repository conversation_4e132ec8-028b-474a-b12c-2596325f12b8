# Review & Rating System Implementation Guide

This document explains how to integrate the complete review and rating system into your multi-vendor eCommerce platform.

## 🎯 Features Implemented

### ✅ Core Features
- **Customer Reviews**: Only customers can write reviews
- **5-Star Rating System**: Rating from 1-5 stars with dynamic average calculation
- **300 Character Limit**: Enforced review length restriction
- **Vendor Replies**: Vendors can reply to reviews on their products
- **User Authentication**: Only logged-in customers can review
- **One Review Per Product**: Customers can only review each product once

### ✅ Advanced Features  
- **Real-time Rating Updates**: Product ratings update when reviews are added
- **Review Sorting**: Sort by newest, oldest, highest/lowest rating
- **Review Analytics**: Rating distribution and statistics
- **Vendor Dashboard**: Comprehensive review management for vendors
- **Mobile Responsive**: Fully responsive design
- **Dark Theme Support**: CSS includes dark theme styles

## 📁 Files Created

### Backend (Server)
```
server/src/
├── models/
│   ├── Review.js              # Review schema with ratings
│   └── ReviewReply.js         # Vendor reply schema
├── controllers/
│   └── reviewController.js    # All review operations
└── routes/
    └── reviews.js            # Review API endpoints
```

### Frontend (Client)
```
client/src/
├── components/
│   ├── reviews/
│   │   ├── ReviewForm.jsx     # Customer review form
│   │   ├── ReviewForm.css
│   │   ├── ReviewList.jsx     # Display product reviews
│   │   ├── ReviewList.css
│   │   ├── ReviewReply.jsx    # Vendor reply component
│   │   └── ReviewReply.css
│   └── vendor/
│       ├── VendorReviews.jsx  # Vendor dashboard component
│       └── VendorReviews.css
└── services/
    └── reviewService.js       # API service functions
```

## 🔧 Installation Steps

### 1. Backend Integration

#### Add Routes to Main App
Add this to your main server file (app.js or server.js):

```javascript
const reviewRoutes = require('./src/routes/reviews');
app.use('/api/reviews', reviewRoutes);
```

#### Database Models
The Review and ReviewReply models are ready to use. They will automatically create the necessary database collections.

### 2. Frontend Integration

#### Install Dependencies (if not already installed)
```bash
npm install antd @ant-design/icons axios
```

#### Import Components
```javascript
// In your product page
import ReviewForm from '../components/reviews/ReviewForm';
import ReviewList from '../components/reviews/ReviewList';
import { reviewService } from '../services/reviewService';

// In your vendor dashboard
import VendorReviews from '../components/vendor/VendorReviews';
```

## 🚀 Usage Examples

### Customer Product Page
```jsx
function ProductPage({ product }) {
  const [reviews, setReviews] = useState([]);
  const [canReview, setCanReview] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleReviewSubmit = async (reviewData) => {
    await reviewService.createReview(reviewData);
    // Refresh reviews
    loadReviews();
  };

  const loadReviews = async () => {
    setLoading(true);
    try {
      const data = await reviewService.getProductReviews(product._id);
      setReviews(data.reviews);
    } catch (error) {
      console.error('Error loading reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {/* Product details */}
      
      {canReview && (
        <ReviewForm
          productId={product._id}
          productName={product.name}
          onReviewSubmit={handleReviewSubmit}
        />
      )}
      
      <ReviewList
        productId={product._id}
        reviews={reviews}
        loading={loading}
        onPageChange={loadReviews}
        onSortChange={loadReviews}
      />
    </div>
  );
}
```

### Vendor Dashboard
```jsx
function VendorDashboard() {
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleReplySubmit = async (reviewId, message) => {
    await reviewService.replyToReview(reviewId, message);
    loadReviews(); // Refresh
  };

  const handleReplyUpdate = async (replyId, message) => {
    await reviewService.updateReply(replyId, message);
    loadReviews(); // Refresh
  };

  const handleReplyDelete = async (replyId) => {
    await reviewService.deleteReply(replyId);
    loadReviews(); // Refresh
  };

  return (
    <VendorReviews
      reviews={reviews}
      loading={loading}
      onReplySubmit={handleReplySubmit}
      onReplyUpdate={handleReplyUpdate}
      onReplyDelete={handleReplyDelete}
    />
  );
}
```

## 🔒 Security Features

### Authentication & Authorization
- **JWT Token Validation**: All protected routes require valid JWT
- **User Type Checking**: Only customers can write reviews
- **Vendor Ownership**: Vendors can only reply to reviews on their products
- **Input Validation**: All input is validated and sanitized

### Data Validation
- **Review Length**: 10-300 characters enforced
- **Reply Length**: 10-500 characters enforced
- **Rating Range**: 1-5 stars only
- **Duplicate Prevention**: One review per customer per product

## 📊 API Endpoints

### Public Endpoints
- `GET /api/reviews/product/:productId` - Get product reviews

### Customer Endpoints (Requires Authentication)
- `POST /api/reviews` - Create review
- `GET /api/reviews/my-reviews` - Get customer's reviews
- `GET /api/reviews/can-review/:productId` - Check if can review

### Vendor Endpoints (Requires Authentication)
- `GET /api/reviews/vendor/my-reviews` - Get vendor's reviews
- `POST /api/reviews/:reviewId/reply` - Reply to review
- `PUT /api/reviews/reply/:replyId` - Update reply
- `DELETE /api/reviews/reply/:replyId` - Delete reply

## 🎨 Customization

### Styling
All components use CSS classes that can be easily customized:
- `.review-form-card` - Review form container
- `.reviews-card` - Review list container
- `.vendor-reviews-card` - Vendor dashboard container

### Configuration
Update these constants in the components as needed:
- Review character limits
- Pagination sizes
- Color schemes
- Animation timings

## 🔄 Product Rating Updates

The system automatically updates product ratings when reviews are added:

```javascript
// In reviewController.js - createReview method
const productRating = await Review.getProductRating(productId);
await Product.findByIdAndUpdate(productId, {
  'reviews.averageRating': productRating.averageRating,
  'reviews.totalReviews': productRating.totalReviews,
  'reviews.ratingDistribution': productRating.ratingDistribution
});
```

## 📱 Mobile Responsive

All components are fully responsive with:
- Adaptive layouts for different screen sizes
- Touch-friendly buttons and forms
- Optimized spacing and typography
- Mobile-first CSS approach

## 🌙 Dark Theme

CSS includes dark theme support using:
```css
@media (prefers-color-scheme: dark) {
  /* Dark theme styles */
}
```

## 🚀 Production Ready

The code includes:
- Error handling and user feedback
- Loading states and spinners
- Input validation and sanitization
- Modular and reusable components
- Clean, maintainable code structure
- Comprehensive CSS with responsive design

## 🔧 Environment Variables

Make sure to set:
```env
REACT_APP_API_URL=http://localhost:5000/api
JWT_SECRET=your-secret-key
```

## 📈 Next Steps

1. **Integration**: Add the routes to your server and import components
2. **Testing**: Test all functionality with different user roles
3. **Customization**: Adjust styling to match your design system
4. **Deployment**: Deploy with proper environment variables

The system is designed to be production-ready with clean, modular code that's easy to maintain and extend!
