const mongoose = require('mongoose');
require('dotenv').config();

// Import the HomepageSettings model
const HomepageSettings = require('./src/models/HomepageSettings');

async function fixExistingImages() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('✅ Connected to MongoDB');

    // Get the homepage settings
    const settings = await HomepageSettings.findOne();
    
    if (!settings) {
      console.log('❌ No homepage settings found');
      return;
    }

    console.log('📊 Current data:');
    console.log('- Carousel images:', settings.carouselImages?.length || 0);
    console.log('- Promotion images:', settings.promotionImages?.length || 0);

    let updated = false;

    // Fix carousel images
    if (settings.carouselImages && settings.carouselImages.length > 0) {
      settings.carouselImages.forEach((image, index) => {
        if (image.isActive === undefined || image.isActive === null) {
          console.log(`🔧 Setting isActive=true for carousel image ${index + 1}`);
          image.isActive = true;
          updated = true;
        }
      });
    }

    // Fix promotion images
    if (settings.promotionImages && settings.promotionImages.length > 0) {
      settings.promotionImages.forEach((image, index) => {
        if (image.isActive === undefined || image.isActive === null) {
          console.log(`🔧 Setting isActive=true for promotion image ${index + 1}`);
          image.isActive = true;
          updated = true;
        }
      });
    }

    if (updated) {
      await settings.save();
      console.log('✅ Updated existing images with isActive=true');
    } else {
      console.log('ℹ️ All images already have isActive field set');
    }

    // Display final state
    console.log('\n📊 Final data:');
    console.log('Carousel images:');
    settings.carouselImages?.forEach((img, i) => {
      console.log(`  ${i + 1}. Title: "${img.title}", isActive: ${img.isActive}, URL: ${img.imageUrl?.substring(0, 50)}...`);
    });
    
    console.log('Promotion images:');
    settings.promotionImages?.forEach((img, i) => {
      console.log(`  ${i + 1}. Title: "${img.title}", isActive: ${img.isActive}, URL: ${img.imageUrl?.substring(0, 50)}...`);
    });

  } catch (error) {
    console.error('❌ Error fixing existing images:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

fixExistingImages();
