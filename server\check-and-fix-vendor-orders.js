require('dotenv').config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// Connect to MongoDB
async function connectDB() {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB:', mongoUri);
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
}

// Load models
const User = require('./src/models/User');
const Vendor = require('./src/models/Vendor'); 
const Product = require('./src/models/Product');
const Order = require('./src/models/Order');

async function checkAndFixVendorOrders() {
  await connectDB();
  
  console.log('\n🔍 CHECKING VENDOR ORDER MANAGEMENT\n');
  
  // 1. Check existing data
  const userCount = await User.countDocuments();
  const vendorCount = await Vendor.countDocuments();
  const productCount = await Product.countDocuments();
  const orderCount = await Order.countDocuments();
  
  console.log('📊 Current Database State:');
  console.log(`Users: ${userCount}`);
  console.log(`Vendors: ${vendorCount}`);
  console.log(`Products: ${productCount}`);
  console.log(`Orders: ${orderCount}`);
  
  if (vendorCount === 0 || orderCount === 0) {
    console.log('\n🔧 Creating test data...');
    await createTestData();
  }
  
  // 2. Find vendors and their orders
  const vendors = await Vendor.find().populate('user', 'email firstName lastName userType');
  
  console.log(`\n📋 Found ${vendors.length} vendors:`);
  
  for (const vendor of vendors) {
    console.log(`\n🏪 Vendor: ${vendor.businessName || 'Unnamed'}`);
    console.log(`   Email: ${vendor.user?.email || 'N/A'}`);
    console.log(`   Status: ${vendor.status || 'N/A'}`);
    console.log(`   ID: ${vendor._id}`);
    
    // Find orders containing this vendor's items
    const orders = await Order.find({ 'items.vendor': vendor._id })
      .populate('customer', 'firstName lastName email')
      .lean();
    
    console.log(`   Orders: ${orders.length}`);
    
    if (orders.length > 0) {
      console.log('   Order details:');
      orders.forEach((order, idx) => {
        const vendorItems = order.items.filter(item => 
          item.vendor.toString() === vendor._id.toString()
        );
        const vendorTotal = vendorItems.reduce((sum, item) => sum + item.totalPrice, 0);
        
        console.log(`   ${idx + 1}. ${order.orderNumber} - ₹${vendorTotal.toFixed(2)} - ${order.status || order.orderStatus}`);
      });
    }
    
    // Test the API aggregation pipeline
    console.log(`\n🧪 Testing API for ${vendor.businessName}:`);
    
    const pipeline = [
      { $match: { 'items.vendor': vendor._id } },
      {
        $addFields: {
          vendorItems: {
            $filter: {
              input: '$items',
              cond: { $eq: ['$$this.vendor', vendor._id] }
            }
          }
        }
      },
      {
        $addFields: {
          vendorItemsCount: { $size: '$vendorItems' },
          vendorTotal: {
            $sum: '$vendorItems.totalPrice'
          }
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'customer',
          foreignField: '_id',
          as: 'customerInfo'
        }
      },
      {
        $unwind: '$customerInfo'
      },
      {
        $project: {
          orderNumber: 1,
          customer: {
            _id: '$customerInfo._id',
            firstName: '$customerInfo.firstName',
            lastName: '$customerInfo.lastName',
            email: '$customerInfo.email',
            phone: '$customerInfo.phone'
          },
          vendorItems: 1,
          vendorItemsCount: 1,
          vendorTotal: 1,
          status: 1,
          payment: 1,
          createdAt: 1
        }
      },
      { $limit: 5 }
    ];
    
    const apiResult = await Order.aggregate(pipeline);
    console.log(`   API would return: ${apiResult.length} orders`);
    
    if (apiResult.length > 0) {
      console.log('   Sample API response:');
      const sample = apiResult[0];
      console.log(`     Order: ${sample.orderNumber}`);
      console.log(`     Customer: ${sample.customer.firstName} ${sample.customer.lastName}`);
      console.log(`     Items: ${sample.vendorItemsCount}`);
      console.log(`     Total: ₹${sample.vendorTotal.toFixed(2)}`);
      console.log(`     Status: ${sample.status}`);
      console.log(`     Payment: ${sample.payment?.status}`);
    }
  }
  
  console.log('\n✅ Check complete!');
  await mongoose.disconnect();
}

async function createTestData() {
  try {
    // Create test customer
    let customer = await User.findOne({ email: '<EMAIL>' });
    if (!customer) {
      const hashedPassword = await bcrypt.hash('password123', 12);
      customer = new User({
        firstName: 'Test',
        lastName: 'Customer', 
        email: '<EMAIL>',
        password: hashedPassword,
        userType: 'customer',
        isVerified: true
      });
      await customer.save();
      console.log('✅ Created test customer');
    }
    
    // Create test vendor user
    let vendorUser = await User.findOne({ email: '<EMAIL>' });
    if (!vendorUser) {
      const hashedPassword = await bcrypt.hash('password123', 12);
      vendorUser = new User({
        firstName: 'Test',
        lastName: 'Vendor',
        email: '<EMAIL>', 
        password: hashedPassword,
        userType: 'vendor',
        isVerified: true
      });
      await vendorUser.save();
      console.log('✅ Created test vendor user');
    }
    
    // Create vendor profile
    let vendor = await Vendor.findOne({ user: vendorUser._id });
    if (!vendor) {
      vendor = new Vendor({
        user: vendorUser._id,
        businessName: 'Test Electronics Store',
        businessType: 'retail',
        description: 'Test vendor for electronics',
        status: 'active',
        address: {
          street: '123 Test Street',
          city: 'Test City',
          state: 'Test State',
          zipCode: '12345',
          country: 'India'
        },
        commission: {
          rate: 15,
          type: 'percentage'
        }
      });
      await vendor.save();
      console.log('✅ Created test vendor profile');
    }
    
    // Create test product
    let product = await Product.findOne({ vendor: vendor._id });
    if (!product) {
      product = new Product({
        name: 'Test Smartphone',
        description: 'A test smartphone for orders',
        vendor: vendor._id,
        pricing: {
          basePrice: 15000,
          salePrice: 14000
        },
        sku: 'TEST-PHONE-001',
        category: 'Electronics',
        status: 'active',
        inventory: {
          quantity: 100,
          lowStockThreshold: 10
        }
      });
      await product.save();
      console.log('✅ Created test product');
    }
    
    // Create test orders with different statuses
    const existingOrders = await Order.find({ 'items.vendor': vendor._id });
    if (existingOrders.length === 0) {
      const orderStatuses = ['pending', 'processing', 'shipped', 'delivered'];
      const paymentStatuses = ['completed', 'completed', 'completed', 'completed']; // All completed for vendor to see
      
      for (let i = 0; i < 5; i++) {
        const quantity = Math.floor(Math.random() * 3) + 1;
        const unitPrice = product.pricing.salePrice;
        const totalPrice = unitPrice * quantity;
        
        const order = new Order({
          customer: customer._id,
          items: [{
            product: product._id,
            vendor: vendor._id,
            name: product.name,
            sku: product.sku,
            quantity: quantity,
            unitPrice: unitPrice,
            totalPrice: totalPrice,
            status: orderStatuses[i % orderStatuses.length]
          }],
          billing: {
            firstName: customer.firstName,
            lastName: customer.lastName,
            email: customer.email,
            phone: '+91-9876543210',
            address: {
              street: '456 Customer Street',
              city: 'Customer City',
              state: 'Customer State',
              zipCode: '54321',
              country: 'India'
            }
          },
          shipping: {
            firstName: customer.firstName,
            lastName: customer.lastName,
            address: {
              street: '456 Customer Street',
              city: 'Customer City',
              state: 'Customer State',
              zipCode: '54321',
              country: 'India'
            },
            method: 'standard',
            cost: 50
          },
          payment: {
            method: 'cash_on_delivery',
            status: paymentStatuses[i % paymentStatuses.length],
            currency: 'INR'
          },
          pricing: {
            subtotal: totalPrice,
            tax: Math.round(totalPrice * 0.18), // 18% GST
            shipping: 50,
            discount: 0,
            total: totalPrice + Math.round(totalPrice * 0.18) + 50
          },
          status: orderStatuses[i % orderStatuses.length],
          timeline: [{
            status: 'pending',
            timestamp: new Date(),
            note: 'Order placed'
          }]
        });
        
        // Update pricing.total to match calculated total
        order.pricing.total = order.pricing.subtotal + order.pricing.tax + order.pricing.shipping - order.pricing.discount;
        
        await order.save();
      }
      
      console.log('✅ Created 5 test orders with different statuses');
    }
    
    console.log('🎉 Test data creation complete!');
    
  } catch (error) {
    console.error('❌ Error creating test data:', error);
  }
}

// Run the check
checkAndFixVendorOrders().catch(console.error);
