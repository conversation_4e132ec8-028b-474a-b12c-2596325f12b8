require('dotenv').config();
const mongoose = require('mongoose');
const { Vendor, Order, User, Product } = require('./src/models');

const createTestOrders = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Check existing orders
    const existingOrders = await Order.find().limit(5);
    console.log('\n=== EXISTING ORDERS ===');
    console.log('Total existing orders:', existingOrders.length);

    if (existingOrders.length > 0) {
      console.log('\n=== FIRST EXISTING ORDER ===');
      const firstOrder = existingOrders[0];
      console.log('Order ID:', firstOrder._id);
      console.log('Order Number:', firstOrder.orderNumber);
      console.log('Customer ID:', firstOrder.customer);
      console.log('Items count:', firstOrder.items?.length || 0);
      
      if (firstOrder.items && firstOrder.items.length > 0) {
        console.log('\n=== ORDER ITEMS ===');
        firstOrder.items.forEach((item, index) => {
          console.log(`Item ${index + 1}:`);
          console.log('  ID:', item._id);
          console.log('  Name:', item.name);
          console.log('  Product ID:', item.product);
          console.log('  Vendor ID:', item.vendor);
          console.log('  Quantity:', item.quantity);
          console.log('  Price:', item.price);
          console.log('  Total Price:', item.totalPrice);
          console.log('  Status:', item.status);
        });
      }

      // Check if any orders have vendor items
      const ordersWithVendorItems = await Order.find({ 'items.vendor': { $exists: true } }).limit(3);
      console.log('\n=== ORDERS WITH VENDOR ITEMS ===');
      console.log('Orders with vendor items:', ordersWithVendorItems.length);

      if (ordersWithVendorItems.length > 0) {
        const orderWithVendor = ordersWithVendorItems[0];
        console.log('\nFirst order with vendor items:');
        console.log('Order ID:', orderWithVendor._id);
        console.log('Items with vendors:');
        orderWithVendor.items.forEach((item, index) => {
          if (item.vendor) {
            console.log(`  Item ${index + 1}: Vendor ID ${item.vendor}`);
          }
        });
      }
    }

    // Check vendors and products
    const vendors = await Vendor.find().limit(3);
    console.log('\n=== VENDORS ===');
    console.log('Total vendors:', vendors.length);
    vendors.forEach((vendor, index) => {
      console.log(`Vendor ${index + 1}:`);
      console.log('  ID:', vendor._id);
      console.log('  Business Name:', vendor.businessName);
      console.log('  User ID:', vendor.user);
    });

    const products = await Product.find().limit(3);
    console.log('\n=== PRODUCTS ===');
    console.log('Total products:', products.length);
    products.forEach((product, index) => {
      console.log(`Product ${index + 1}:`);
      console.log('  ID:', product._id);
      console.log('  Name:', product.name);
      console.log('  Vendor ID:', product.vendor);
    });

    const customers = await User.find({ userType: 'customer' }).limit(3);
    console.log('\n=== CUSTOMERS ===');
    console.log('Total customers:', customers.length);
    customers.forEach((customer, index) => {
      console.log(`Customer ${index + 1}:`);
      console.log('  ID:', customer._id);
      console.log('  Name:', customer.firstName, customer.lastName);
      console.log('  Email:', customer.email);
    });

    // If we have vendors, products, and customers, but no orders, let's create a test order
    if (vendors.length > 0 && products.length > 0 && customers.length > 0 && existingOrders.length === 0) {
      console.log('\n=== CREATING TEST ORDER ===');
      
      const vendor = vendors[0];
      const product = products[0];
      const customer = customers[0];

      const testOrder = new Order({
        orderNumber: `ORD-${Date.now()}`,
        customer: customer._id,
        items: [{
          _id: new mongoose.Types.ObjectId(),
          product: product._id,
          vendor: vendor._id,
          name: product.name,
          sku: product.sku || 'TEST-SKU',
          quantity: 2,
          price: 25.99,
          totalPrice: 51.98,
          status: 'pending'
        }],
        subtotal: 51.98,
        tax: 4.16,
        shipping: 5.99,
        total: 62.13,
        status: 'pending',
        payment: {
          method: 'cash_on_delivery',
          status: 'pending'
        },
        billing: {
          firstName: customer.firstName,
          lastName: customer.lastName,
          email: customer.email,
          phone: customer.phone || '+1234567890',
          address: '123 Test Street',
          city: 'Test City',
          state: 'Test State',
          country: 'USA',
          zipCode: '12345'
        },
        shipping: {
          address: '123 Test Street',
          city: 'Test City',
          state: 'Test State',
          country: 'USA',
          zipCode: '12345'
        },
        timeline: [{
          status: 'pending',
          timestamp: new Date(),
          note: 'Order placed',
          updatedBy: customer._id
        }]
      });

      await testOrder.save();
      console.log('Test order created:', testOrder.orderNumber);
      console.log('Order ID:', testOrder._id);
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
};

createTestOrders();
