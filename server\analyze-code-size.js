#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// File extensions to analyze
const FRONTEND_EXTENSIONS = ['.js', '.jsx', '.ts', '.tsx', '.vue', '.css', '.scss', '.sass'];
const BACKEND_EXTENSIONS = ['.js', '.ts', '.py', '.java', '.php', '.rb', '.go', '.cs'];
const CONFIG_EXTENSIONS = ['.json', '.yaml', '.yml', '.xml', '.toml'];

// Directories to exclude
const EXCLUDE_DIRS = [
    'node_modules', 
    '.git', 
    'dist', 
    'build', 
    'coverage', 
    '.next', 
    '.nuxt',
    '__pycache__',
    'vendor',
    'uploads',
    '.vscode',
    '.augment',
    '.qodo'
];

// Files to exclude
const EXCLUDE_FILES = [
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',
    '.DS_Store',
    'Thumbs.db'
];

function countLines(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');
        
        // Count non-empty lines and exclude comment-only lines for better accuracy
        let codeLines = 0;
        let totalLines = lines.length;
        let commentLines = 0;
        let emptyLines = 0;
        
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed === '') {
                emptyLines++;
            } else if (trimmed.startsWith('//') || trimmed.startsWith('/*') || trimmed.startsWith('*') || trimmed.startsWith('#')) {
                commentLines++;
            } else {
                codeLines++;
            }
        }
        
        return {
            total: totalLines,
            code: codeLines,
            comments: commentLines,
            empty: emptyLines
        };
    } catch (error) {
        console.error(`Error reading file ${filePath}:`, error.message);
        return { total: 0, code: 0, comments: 0, empty: 0 };
    }
}

function shouldExcludeDir(dirName) {
    return EXCLUDE_DIRS.some(exclude => dirName.includes(exclude));
}

function shouldExcludeFile(fileName) {
    return EXCLUDE_FILES.includes(fileName);
}

function getFileType(filePath, baseDir) {
    const ext = path.extname(filePath).toLowerCase();
    
    if (baseDir.includes('client') || baseDir.includes('frontend')) {
        if (FRONTEND_EXTENSIONS.includes(ext)) return 'Frontend';
    }
    
    if (baseDir.includes('server') || baseDir.includes('backend') || baseDir.includes('api')) {
        if (BACKEND_EXTENSIONS.includes(ext)) return 'Backend';
    }
    
    if (CONFIG_EXTENSIONS.includes(ext)) return 'Config';
    
    // Fallback based on extension
    if (FRONTEND_EXTENSIONS.includes(ext)) return 'Frontend';
    if (BACKEND_EXTENSIONS.includes(ext)) return 'Backend';
    
    return 'Other';
}

function analyzeDirectory(dirPath, results = [], baseDir = '') {
    try {
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                if (!shouldExcludeDir(item)) {
                    analyzeDirectory(fullPath, results, baseDir || item);
                }
            } else if (stat.isFile()) {
                if (!shouldExcludeFile(item)) {
                    const lines = countLines(fullPath);
                    const fileType = getFileType(fullPath, baseDir);
                    const relativePath = path.relative(path.join(__dirname, '..'), fullPath);
                    
                    results.push({
                        name: item,
                        path: relativePath,
                        type: fileType,
                        lines: lines,
                        size: stat.size
                    });
                }
            }
        }
    } catch (error) {
        console.error(`Error analyzing directory ${dirPath}:`, error.message);
    }
    
    return results;
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function generateReport(results) {
    // Sort by total lines (descending)
    const sortedByLines = [...results].sort((a, b) => b.lines.total - a.lines.total);
    
    // Filter for significant files (more than 10 lines)
    const significantFiles = sortedByLines.filter(file => file.lines.total > 10);
    
    console.log('\n' + '='.repeat(80));
    console.log('CODE SIZE ANALYSIS REPORT - MULTI-VENDOR ECOMMERCE');
    console.log('='.repeat(80));
    
    // Summary by type
    const summary = {};
    results.forEach(file => {
        if (!summary[file.type]) {
            summary[file.type] = { files: 0, totalLines: 0, codeLines: 0, totalSize: 0 };
        }
        summary[file.type].files++;
        summary[file.type].totalLines += file.lines.total;
        summary[file.type].codeLines += file.lines.code;
        summary[file.type].totalSize += file.size;
    });
    
    console.log('\nSUMMARY BY TYPE:');
    console.log('-'.repeat(80));
    Object.entries(summary).forEach(([type, stats]) => {
        console.log(`${type.padEnd(12)} | Files: ${stats.files.toString().padStart(4)} | Total Lines: ${stats.totalLines.toString().padStart(6)} | Code Lines: ${stats.codeLines.toString().padStart(6)} | Size: ${formatBytes(stats.totalSize)}`);
    });
    
    // Top 20 largest files by total lines
    console.log('\nTOP 20 LARGEST FILES BY TOTAL LINES:');
    console.log('-'.repeat(80));
    console.log('Rank | Type       | Total | Code  | File Name & Path');
    console.log('-'.repeat(80));
    
    significantFiles.slice(0, 20).forEach((file, index) => {
        const rank = (index + 1).toString().padStart(4);
        const type = file.type.padEnd(10);
        const total = file.lines.total.toString().padStart(5);
        const code = file.lines.code.toString().padStart(5);
        const fileName = file.name.length > 30 ? file.name.substring(0, 27) + '...' : file.name;
        const filePath = file.path.length > 50 ? '...' + file.path.substring(file.path.length - 47) : file.path;
        
        console.log(`${rank} | ${type} | ${total} | ${code} | ${fileName}`);
        console.log(`     |            |       |       | ${filePath}`);
        console.log('-'.repeat(80));
    });
    
    // Frontend specific analysis
    const frontendFiles = significantFiles.filter(f => f.type === 'Frontend');
    if (frontendFiles.length > 0) {
        console.log('\nTOP 15 LARGEST FRONTEND COMPONENTS:');
        console.log('-'.repeat(80));
        console.log('Rank | Total | Code  | File Name & Path');
        console.log('-'.repeat(80));
        
        frontendFiles.slice(0, 15).forEach((file, index) => {
            const rank = (index + 1).toString().padStart(4);
            const total = file.lines.total.toString().padStart(5);
            const code = file.lines.code.toString().padStart(5);
            console.log(`${rank} | ${total} | ${code} | ${file.name}`);
            console.log(`     |       |       | ${file.path}`);
            console.log('-'.repeat(80));
        });
    }
    
    // Backend specific analysis
    const backendFiles = significantFiles.filter(f => f.type === 'Backend');
    if (backendFiles.length > 0) {
        console.log('\nTOP 15 LARGEST BACKEND COMPONENTS:');
        console.log('-'.repeat(80));
        console.log('Rank | Total | Code  | File Name & Path');
        console.log('-'.repeat(80));
        
        backendFiles.slice(0, 15).forEach((file, index) => {
            const rank = (index + 1).toString().padStart(4);
            const total = file.lines.total.toString().padStart(5);
            const code = file.lines.code.toString().padStart(5);
            console.log(`${rank} | ${total} | ${code} | ${file.name}`);
            console.log(`     |       |       | ${file.path}`);
            console.log('-'.repeat(80));
        });
    }
    
    // Files with high complexity (high line count)
    const complexFiles = significantFiles.filter(f => f.lines.total > 200);
    if (complexFiles.length > 0) {
        console.log('\nFILES WITH HIGH COMPLEXITY (>200 lines):');
        console.log('-'.repeat(80));
        console.log('Type       | Total | Code  | File Name & Path');
        console.log('-'.repeat(80));
        
        complexFiles.forEach(file => {
            const type = file.type.padEnd(10);
            const total = file.lines.total.toString().padStart(5);
            const code = file.lines.code.toString().padStart(5);
            console.log(`${type} | ${total} | ${code} | ${file.name}`);
            console.log(`           |       |       | ${file.path}`);
            console.log('-'.repeat(80));
        });
    }
    
    // Component analysis for React/Vue files
    const componentFiles = significantFiles.filter(f => 
        f.name.endsWith('.jsx') || f.name.endsWith('.tsx') || f.name.endsWith('.vue')
    );
    
    if (componentFiles.length > 0) {
        console.log('\nREACT/VUE COMPONENT ANALYSIS:');
        console.log('-'.repeat(80));
        console.log('Total | Code  | Component Name & Path');
        console.log('-'.repeat(80));
        
        componentFiles.forEach(file => {
            const total = file.lines.total.toString().padStart(5);
            const code = file.lines.code.toString().padStart(5);
            console.log(`${total} | ${code} | ${file.name}`);
            console.log(`      |       | ${file.path}`);
            console.log('-'.repeat(80));
        });
    }
    
    console.log(`\nAnalysis completed. Total files analyzed: ${results.length}`);
    console.log(`Files with significant code (>10 lines): ${significantFiles.length}`);
    console.log(`Files with high complexity (>200 lines): ${complexFiles.length}`);
    console.log(`Frontend components found: ${frontendFiles.length}`);
    console.log(`Backend components found: ${backendFiles.length}`);
}

// Main execution
console.log('Starting code size analysis for Multi-Vendor eCommerce...');
console.log('Analyzing project structure...');

const results = [];
const projectRoot = path.join(__dirname, '..');

// Analyze client directory
const clientPath = path.join(projectRoot, 'client');
if (fs.existsSync(clientPath)) {
    console.log('Analyzing client directory...');
    analyzeDirectory(clientPath, results, 'client');
}

// Analyze server directory
const serverPath = path.join(projectRoot, 'server');
if (fs.existsSync(serverPath)) {
    console.log('Analyzing server directory...');
    analyzeDirectory(serverPath, results, 'server');
}

// Analyze root level files
console.log('Analyzing root directory files...');
const rootItems = fs.readdirSync(projectRoot);
rootItems.forEach(item => {
    const fullPath = path.join(projectRoot, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isFile() && !shouldExcludeFile(item)) {
        const lines = countLines(fullPath);
        const fileType = getFileType(fullPath, '');
        
        results.push({
            name: item,
            path: item,
            type: fileType,
            lines: lines,
            size: stat.size
        });
    }
});

generateReport(results);