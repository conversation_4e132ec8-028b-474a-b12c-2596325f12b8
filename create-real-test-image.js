const fs = require('fs');
const path = require('path');

// Create a minimal but valid PNG file (1x1 pixel)
function createValidPNG() {
  // PNG signature
  const signature = Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]);
  
  // IHDR chunk (13 bytes data + 12 bytes overhead)
  const ihdrLength = Buffer.from([0x00, 0x00, 0x00, 0x0D]); // 13 bytes
  const ihdrType = Buffer.from('IHDR');
  const ihdrData = Buffer.from([
    0x00, 0x00, 0x00, 0x01, // Width: 1
    0x00, 0x00, 0x00, 0x01, // Height: 1
    0x08, // Bit depth: 8
    0x02, // Color type: 2 (RGB)
    0x00, // Compression: 0
    0x00, // Filter: 0
    0x00  // Interlace: 0
  ]);
  const ihdrCrc = Buffer.from([0x90, 0x77, 0x53, 0xDE]); // Pre-calculated CRC
  
  // IDAT chunk (minimal data)
  const idatLength = Buffer.from([0x00, 0x00, 0x00, 0x0C]); // 12 bytes
  const idatType = Buffer.from('IDAT');
  const idatData = Buffer.from([
    0x78, 0x9C, 0x62, 0xF8, 0x0F, 0x00, 0x01, 0x01, 0x01, 0x00, 0x18, 0xDD
  ]);
  const idatCrc = Buffer.from([0x8D, 0xB4, 0x2C, 0x20]); // Pre-calculated CRC
  
  // IEND chunk
  const iendLength = Buffer.from([0x00, 0x00, 0x00, 0x00]); // 0 bytes
  const iendType = Buffer.from('IEND');
  const iendCrc = Buffer.from([0xAE, 0x42, 0x60, 0x82]); // Pre-calculated CRC
  
  return Buffer.concat([
    signature,
    ihdrLength, ihdrType, ihdrData, ihdrCrc,
    idatLength, idatType, idatData, idatCrc,
    iendLength, iendType, iendCrc
  ]);
}

// Create a minimal but valid JPEG file
function createValidJPEG() {
  return Buffer.from([
    // SOI (Start of Image)
    0xFF, 0xD8,
    
    // APP0 segment
    0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
    0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00,
    
    // DQT (Define Quantization Table)
    0xFF, 0xDB, 0x00, 0x43, 0x00,
    0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07,
    0x07, 0x09, 0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C,
    0x0B, 0x0B, 0x0C, 0x19, 0x12, 0x13, 0x0F, 0x14, 0x1D,
    0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20, 0x24,
    0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28,
    0x37, 0x29, 0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F,
    0x27, 0x39, 0x3D, 0x38, 0x32, 0x3C, 0x2E, 0x33, 0x34,
    0x32,
    
    // SOF0 (Start of Frame)
    0xFF, 0xC0, 0x00, 0x11, 0x08, 0x00, 0x01, 0x00, 0x01,
    0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11,
    0x01,
    
    // DHT (Define Huffman Table)
    0xFF, 0xC4, 0x00, 0x1F, 0x00, 0x00, 0x01, 0x05, 0x01,
    0x01, 0x01, 0x01, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
    0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B,
    
    // SOS (Start of Scan)
    0xFF, 0xDA, 0x00, 0x0C, 0x03, 0x01, 0x00, 0x02, 0x11,
    0x03, 0x11, 0x00, 0x3F, 0x00,
    
    // Minimal image data
    0xD2, 0xCF, 0x20,
    
    // EOI (End of Image)
    0xFF, 0xD9
  ]);
}

// Create test images
const testDir = __dirname;

// Create PNG
const pngData = createValidPNG();
const pngPath = path.join(testDir, 'test-image.png');
fs.writeFileSync(pngPath, pngData);
console.log(`✅ Created valid PNG: ${pngPath} (${pngData.length} bytes)`);

// Create JPEG
const jpegData = createValidJPEG();
const jpegPath = path.join(testDir, 'test-image.jpg');
fs.writeFileSync(jpegPath, jpegData);
console.log(`✅ Created valid JPEG: ${jpegPath} (${jpegData.length} bytes)`);

console.log('\nTest images created successfully!');
console.log('You can now use these files for testing carousel upload.');