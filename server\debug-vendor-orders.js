require('dotenv').config();
const mongoose = require('mongoose');
const { Vendor, Order, User } = require('./src/models');

const debugVendorOrders = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Find the vendor that actually has orders
    const vendor = await Vendor.findOne({ _id: new mongoose.Types.ObjectId('6881fd066652576061a0be6e') }).populate('user');
    if (!vendor) {
      console.log('Specific vendor not found');
      return;
    }

    console.log('\n=== VENDOR INFO ===');
    console.log('Vendor ID:', vendor._id);
    console.log('Business Name:', vendor.businessName);
    console.log('User ID:', vendor.user._id);

    // Get orders containing this vendor's items
    const orders = await Order.find({ 'items.vendor': vendor._id })
      .populate('customer', 'firstName lastName email phone')
      .populate('items.product', 'name images pricing sku')
      .populate('items.vendor', 'businessName')
      .limit(5);

    console.log('\n=== ORDERS FOUND ===');
    console.log('Total orders containing vendor items:', orders.length);

    if (orders.length > 0) {
      const firstOrder = orders[0];
      console.log('\n=== FIRST ORDER STRUCTURE ===');
      console.log('Order ID:', firstOrder._id);
      console.log('Order Number:', firstOrder.orderNumber);
      console.log('Customer:', firstOrder.customer?.firstName, firstOrder.customer?.lastName);
      console.log('Total Items:', firstOrder.items.length);
      
      // Filter vendor items
      const vendorItems = firstOrder.items.filter(item => 
        item.vendor._id.toString() === vendor._id.toString()
      );
      
      console.log('Vendor Items Count:', vendorItems.length);
      console.log('\n=== VENDOR ITEMS DETAILS ===');
      vendorItems.forEach((item, index) => {
        console.log(`Item ${index + 1}:`);
        console.log('  Product Name:', item.product?.name || item.name);
        console.log('  SKU:', item.product?.sku || item.sku);
        console.log('  Quantity:', item.quantity);
        console.log('  Price:', item.price);
        console.log('  Total Price:', item.totalPrice);
        console.log('  Status:', item.status);
        console.log('  Vendor:', item.vendor.businessName);
      });

      // Test the aggregation pipeline like the controller
      console.log('\n=== TESTING AGGREGATION PIPELINE ===');
      const pipeline = [
        { $match: { 'items.vendor': vendor._id } },
        {
          $addFields: {
            vendorItems: {
              $filter: {
                input: '$items',
                cond: { $eq: ['$$this.vendor', vendor._id] }
              }
            }
          }
        },
        {
          $addFields: {
            vendorItemsCount: { $size: '$vendorItems' },
            vendorTotal: {
              $sum: '$vendorItems.totalPrice'
            }
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: 'customer',
            foreignField: '_id',
            as: 'customerInfo'
          }
        },
        {
          $unwind: '$customerInfo'
        },
        {
          $project: {
            orderNumber: 1,
            customer: {
              _id: '$customerInfo._id',
              firstName: '$customerInfo.firstName',
              lastName: '$customerInfo.lastName',
              email: '$customerInfo.email',
              phone: '$customerInfo.phone'
            },
            vendorItems: 1,
            vendorItemsCount: 1,
            vendorTotal: 1,
            status: 1,
            payment: 1,
            shipping: 1,
            createdAt: 1,
            updatedAt: 1,
            timeline: 1
          }
        },
        { $limit: 3 }
      ];

      const aggregatedOrders = await Order.aggregate(pipeline);
      console.log('Aggregated Orders Count:', aggregatedOrders.length);
      
      if (aggregatedOrders.length > 0) {
        const firstAggregated = aggregatedOrders[0];
        console.log('\n=== AGGREGATED ORDER STRUCTURE ===');
        console.log('Order Number:', firstAggregated.orderNumber);
        console.log('Customer Name:', firstAggregated.customer?.firstName, firstAggregated.customer?.lastName);
        console.log('Vendor Items Count:', firstAggregated.vendorItemsCount);
        console.log('Vendor Total:', firstAggregated.vendorTotal);
        console.log('Vendor Items:', firstAggregated.vendorItems?.length || 0);
        
        if (firstAggregated.vendorItems?.length > 0) {
          console.log('\n=== AGGREGATED VENDOR ITEMS ===');
          firstAggregated.vendorItems.forEach((item, index) => {
            console.log(`Item ${index + 1}:`);
            console.log('  ID:', item._id);
            console.log('  Name:', item.name);
            console.log('  SKU:', item.sku);
            console.log('  Quantity:', item.quantity);
            console.log('  Price:', item.price);
            console.log('  Total Price:', item.totalPrice);
            console.log('  Status:', item.status);
          });
        }
      }
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
};

debugVendorOrders();
