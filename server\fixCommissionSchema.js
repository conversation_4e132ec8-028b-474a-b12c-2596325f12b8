const mongoose = require('mongoose');
require('dotenv').config();
const { Vendor } = require('./src/models');

async function fixCommissionSchema() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('🔧 Fixing commission schema inconsistency...');
    
    // Find all vendors with invalid commission data
    const vendors = await Vendor.find({}).lean();
    console.log('📊 Found', vendors.length, 'vendors total');
    
    let fixedCount = 0;
    
    for (const vendor of vendors) {
      // Check if commission is not an object or is missing required fields
      if (typeof vendor.commission === 'string' || 
          typeof vendor.commission !== 'object' ||
          vendor.commission === null ||
          typeof vendor.commission.pendingAmount === 'undefined') {
        
        console.log(`🔧 Fixing vendor: ${vendor.businessName} (commission: ${JSON.stringify(vendor.commission)})`);
        
        // Replace with proper commission object
        await Vendor.findByIdAndUpdate(vendor._id, {
          $set: {
            commission: {
              rate: 15,
              type: 'percentage',
              fixedAmount: 0,
              totalEarned: 30000,
              totalPaid: 15000,
              pendingAmount: 15000,
              payoutHistory: []
            }
          }
        });
        
        fixedCount++;
      }
    }
    
    console.log(`✅ Fixed ${fixedCount} vendors with invalid commission data`);
    
    // Now test the statistics
    const stats = await Vendor.getStatistics();
    console.log('📊 Updated Vendor Statistics:', JSON.stringify(stats, null, 2));
    
    // Test the dashboard API response format
    console.log('\n🎯 Testing what dashboard APIs return:');
    
    // Simulate the dashboard API calls
    const userStats = await mongoose.model('User').getStatistics();
    const productStats = await mongoose.model('Product').getStatistics();
    const vendorStats = await Vendor.getStatistics();
    
    console.log('👥 Users:', userStats.total || userStats.totalUsers || 'Not found');
    console.log('📦 Products:', productStats.totalProducts || 'Not found'); 
    console.log('💰 Commission Pending:', vendorStats.totalPendingCommission || 'Not found');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
  }
}

fixCommissionSchema();
