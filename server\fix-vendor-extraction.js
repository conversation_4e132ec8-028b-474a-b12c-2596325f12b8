/**
 * Comprehensive fix for vendor extraction issue
 * This script will:
 * 1. Debug the database to understand the vendor data structure
 * 2. Fix the cart population to include vendor data
 * 3. Test the fix by placing an order
 */

const mongoose = require('mongoose');
const axios = require('axios');
require('dotenv').config();

// Import models
const Cart = require('./src/models/Cart');
const Product = require('./src/models/Product');
const User = require('./src/models/User');
const Vendor = require('./src/models/Vendor');

const API_BASE_URL = process.env.API_URL || 'http://localhost:5000/api';

// Test credentials
const TEST_CUSTOMER = {
  email: '<EMAIL>',
  password: 'Free@009'
};

let authToken = null;

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('✅ MongoDB connected for debugging');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Helper function for API calls
const apiCall = async (method, endpoint, data = null) => {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...(authToken && { Authorization: `Bearer ${authToken}` })
      }
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, {
      status: error.response?.status,
      message: error.response?.data?.message || error.message
    });
    throw error;
  }
};

// Login function
const login = async () => {
  console.log('🔐 Logging in...');
  try {
    const response = await apiCall('POST', '/auth/login', TEST_CUSTOMER);
    authToken = response.data?.token || response.token;
    console.log('✅ Login successful');
    return response;
  } catch (error) {
    console.log('❌ Login failed:', error.message);
    return null;
  }
};

// Database-level debugging
const debugProductInDatabase = async (productId) => {
  console.log('\n🔍 DATABASE LEVEL DEBUGGING');
  console.log('='.repeat(50));
  
  try {
    // 1. Check raw product data
    console.log('\n1. Raw product data:');
    const rawProduct = await Product.findById(productId);
    if (!rawProduct) {
      console.log('❌ Product not found in database');
      return null;
    }
    
    console.log('✅ Product found:', {
      id: rawProduct._id,
      name: rawProduct.name,
      status: rawProduct.status,
      vendorField: rawProduct.vendor,
      vendorType: typeof rawProduct.vendor
    });
    
    // 2. Check if vendor exists
    console.log('\n2. Checking vendor:');
    if (rawProduct.vendor) {
      const vendor = await Vendor.findById(rawProduct.vendor).populate('user');
      if (vendor) {
        console.log('✅ Vendor found:', {
          id: vendor._id,
          businessName: vendor.businessName,
          status: vendor.status,
          userId: vendor.user?._id,
          userEmail: vendor.user?.email
        });
      } else {
        console.log('❌ Vendor not found for ID:', rawProduct.vendor);
      }
    } else {
      console.log('❌ Product has no vendor field');
    }
    
    // 3. Check product with populated vendor
    console.log('\n3. Product with populated vendor:');
    const populatedProduct = await Product.findById(productId).populate('vendor');
    console.log('Populated vendor:', {
      hasVendor: !!populatedProduct.vendor,
      vendorType: typeof populatedProduct.vendor,
      vendorData: populatedProduct.vendor
    });
    
    return rawProduct;
    
  } catch (error) {
    console.error('❌ Database debug error:', error);
    return null;
  }
};

// Debug cart in database
const debugCartInDatabase = async (customerId) => {
  console.log('\n🛒 CART DATABASE DEBUGGING');
  console.log('='.repeat(50));
  
  try {
    // Find customer
    const customer = await User.findOne({ email: TEST_CUSTOMER.email });
    if (!customer) {
      console.log('❌ Customer not found');
      return null;
    }
    
    console.log('✅ Customer found:', customer._id);
    
    // Find cart
    const cart = await Cart.findOne({ customer: customer._id });
    if (!cart) {
      console.log('❌ Cart not found');
      return null;
    }
    
    console.log('✅ Cart found with', cart.items.length, 'items');
    
    // Check each cart item
    for (let i = 0; i < cart.items.length; i++) {
      const item = cart.items[i];
      console.log(`\nCart Item ${i + 1}:`, {
        productId: item.product,
        vendorId: item.vendor,
        quantity: item.quantity,
        priceAtAdd: item.priceAtAdd
      });
      
      // Check if vendor exists
      if (item.vendor) {
        const vendor = await Vendor.findById(item.vendor);
        console.log('  Vendor exists:', !!vendor);
        if (vendor) {
          console.log('  Vendor details:', {
            id: vendor._id,
            businessName: vendor.businessName,
            status: vendor.status
          });
        }
      } else {
        console.log('  ❌ No vendor ID in cart item');
      }
    }
    
    // Get cart with populated data
    console.log('\n4. Cart with populated data:');
    const populatedCart = await Cart.findOne({ customer: customer._id })
      .populate('items.product')
      .populate('items.vendor');
    
    populatedCart.items.forEach((item, index) => {
      console.log(`\nPopulated Item ${index + 1}:`, {
        productName: item.product?.name,
        hasVendor: !!item.vendor,
        vendorType: typeof item.vendor,
        vendorData: item.vendor ? {
          id: item.vendor._id,
          businessName: item.vendor.businessName
        } : null
      });
    });
    
    return cart;
    
  } catch (error) {
    console.error('❌ Cart debug error:', error);
    return null;
  }
};

// Fix cart vendor data
const fixCartVendorData = async (productId) => {
  console.log('\n🔧 FIXING CART VENDOR DATA');
  console.log('='.repeat(50));
  
  try {
    // Find customer
    const customer = await User.findOne({ email: TEST_CUSTOMER.email });
    if (!customer) {
      console.log('❌ Customer not found');
      return false;
    }
    
    // Find product with vendor
    const product = await Product.findById(productId);
    if (!product || !product.vendor) {
      console.log('❌ Product or vendor not found');
      return false;
    }
    
    console.log('✅ Product vendor ID:', product.vendor);
    
    // Find cart
    const cart = await Cart.findOne({ customer: customer._id });
    if (!cart) {
      console.log('❌ Cart not found');
      return false;
    }
    
    // Update cart items with vendor data
    let updated = false;
    for (let item of cart.items) {
      if (item.product.toString() === productId && !item.vendor) {
        console.log('🔧 Updating cart item with vendor ID:', product.vendor);
        item.vendor = product.vendor;
        updated = true;
      }
    }
    
    if (updated) {
      await cart.save();
      console.log('✅ Cart updated with vendor data');
      return true;
    } else {
      console.log('ℹ️ No cart items needed updating');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Fix cart error:', error);
    return false;
  }
};

// Test order placement after fix
const testOrderPlacement = async () => {
  console.log('\n🛍️ TESTING ORDER PLACEMENT AFTER FIX');
  console.log('='.repeat(50));
  
  try {
    // Get user profile
    const userResponse = await apiCall('GET', '/customer/profile');
    const user = userResponse.data;
    
    // Get cart
    const cartResponse = await apiCall('GET', '/customer/cart');
    const cart = cartResponse.data;
    
    if (!cart || !cart.items || cart.items.length === 0) {
      console.log('❌ Cart is empty');
      return false;
    }
    
    console.log('✅ Cart has', cart.items.length, 'items');
    
    // Test vendor extraction
    const orderItems = cart.items.map((item, index) => {
      console.log(`\nProcessing item ${index + 1}: ${item.product?.name}`);
      
      let vendorId = null;
      let strategy = 'none';
      
      // Strategy 1: Direct vendor from cart item
      if (item.vendor) {
        if (typeof item.vendor === 'object' && item.vendor._id) {
          vendorId = item.vendor._id.toString();
          strategy = 'item.vendor._id';
        } else if (typeof item.vendor === 'string' && item.vendor.length === 24) {
          vendorId = item.vendor;
          strategy = 'item.vendor (string)';
        }
      }
      
      // Strategy 2: Vendor from populated product
      if (!vendorId && item.product?.vendor) {
        if (typeof item.product.vendor === 'object' && item.product.vendor._id) {
          vendorId = item.product.vendor._id.toString();
          strategy = 'item.product.vendor._id';
        } else if (typeof item.product.vendor === 'string' && item.product.vendor.length === 24) {
          vendorId = item.product.vendor;
          strategy = 'item.product.vendor (string)';
        }
      }
      
      if (!vendorId) {
        throw new Error(`Vendor information is missing for product: ${item.product?.name}`);
      }
      
      console.log(`✅ Vendor ID extracted: ${vendorId} (via ${strategy})`);
      
      return {
        product: item.product._id,
        vendor: vendorId,
        name: item.product.name,
        quantity: item.quantity,
        unitPrice: item.priceAtAdd,
        totalPrice: item.priceAtAdd * item.quantity
      };
    });
    
    console.log('\n✅ All items processed successfully!');
    
    // Prepare order data
    const subtotal = cart.totalAmount || 0;
    const protectPromiseFee = 28;
    const total = subtotal + protectPromiseFee;
    
    const orderData = {
      items: orderItems,
      billing: {
        firstName: user.firstName || 'Test',
        lastName: user.lastName || 'User',
        email: user.email || TEST_CUSTOMER.email,
        phone: user.phone || '1234567890',
        address: {
          street: user.address || 'Test Address',
          city: user.city || 'Test City',
          state: user.state || 'Test State',
          zipCode: user.zipCode || '12345',
          country: user.country || 'India'
        }
      },
      shipping: {
        firstName: user.firstName || 'Test',
        lastName: user.lastName || 'User',
        address: {
          street: user.address || 'Test Address',
          city: user.city || 'Test City',
          state: user.state || 'Test State',
          zipCode: user.zipCode || '12345',
          country: user.country || 'India'
        },
        method: 'standard',
        cost: 0
      },
      payment: {
        method: 'cod',
        status: 'pending'
      },
      pricing: {
        subtotal: subtotal,
        tax: 0,
        shipping: protectPromiseFee,
        discount: 0,
        total: total
      },
      customerNotes: 'Test order after vendor fix',
      estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
    };
    
    console.log('\n📦 Placing order...');
    const response = await apiCall('POST', '/customer/orders', orderData);
    
    if (response.success) {
      console.log('🎉 ORDER PLACED SUCCESSFULLY!');
      console.log('Order details:', {
        orderId: response.data.order.orderNumber,
        total: response.data.order.pricing.total,
        status: response.data.order.status
      });
      return true;
    } else {
      console.log('❌ Order placement failed:', response.message);
      return false;
    }
    
  } catch (error) {
    console.log('❌ Order placement error:', error.message);
    return false;
  }
};

// Main function
const main = async () => {
  console.log('🚀 COMPREHENSIVE VENDOR EXTRACTION FIX');
  console.log('='.repeat(60));
  
  const productId = '6881fd806652576061a0be95';
  
  try {
    // Connect to database
    await connectDB();
    
    // Login to API
    const loginResult = await login();
    if (!authToken) {
      console.log('❌ Cannot proceed without authentication');
      return;
    }
    
    // 1. Debug product in database
    const product = await debugProductInDatabase(productId);
    if (!product) {
      console.log('❌ Cannot proceed without product');
      return;
    }
    
    // 2. Debug cart in database
    await debugCartInDatabase();
    
    // 3. Fix cart vendor data if needed
    const fixed = await fixCartVendorData(productId);
    if (fixed) {
      console.log('✅ Cart vendor data has been fixed');
    }
    
    // 4. Test order placement
    const orderSuccess = await testOrderPlacement();
    
    if (orderSuccess) {
      console.log('\n🎉 SUCCESS! The vendor extraction issue has been resolved!');
      console.log('The order was placed successfully, proving the fix works.');
    } else {
      console.log('\n❌ Order placement still failed. Additional investigation needed.');
    }
    
  } catch (error) {
    console.error('❌ Main function error:', error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('\n🏁 Database connection closed');
  }
};

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  debugProductInDatabase,
  debugCartInDatabase,
  fixCartVendorData,
  testOrderPlacement
};