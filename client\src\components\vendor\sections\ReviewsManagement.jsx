import React, { useState, useEffect } from 'react';
import { Form, Modal, notification } from 'antd';
import { reviewService } from '../../../services/reviewService';
import ReviewsStats from './reviews/ReviewsStats';
import ReviewsFilters from './reviews/ReviewsFilters';
import ReviewsTable from './reviews/ReviewsTable';
import ReviewModals from './reviews/ReviewModals';

const ReviewsManagement = () => {
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [selectedRating, setSelectedRating] = useState('all');
  const [searchText, setSearchText] = useState('');
  const [replyModalVisible, setReplyModalVisible] = useState(false);
  const [editReplyModalVisible, setEditReplyModalVisible] = useState(false);
  const [selectedReview, setSelectedReview] = useState(null);
  const [selectedReply, setSelectedReply] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [replyForm] = Form.useForm();
  const [editReplyForm] = Form.useForm();
  const [stats, setStats] = useState({
    totalReviews: 0,
    averageRating: 0,
    repliedReviews: 0,
    pendingReplies: 0,
    ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
  });

  // Load vendor reviews with filters
  const loadReviews = async (page = 1, rating = selectedRating, search = searchText) => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: pagination.pageSize,
        ...(rating !== 'all' && { rating }),
        ...(search && { search })
      };
      
      const data = await reviewService.getVendorReviews(params);
      console.log('Vendor Reviews API Response:', data); // Debug log
      
      setReviews(data.reviews || []);
      setPagination(prev => ({
        ...prev,
        current: page,
        total: data.pagination?.totalReviews || 0
      }));
      
      // Calculate stats from all available reviews (not just current page)
      const allReviews = data.allReviews || data.reviews || [];
      calculateStats(data.reviews || [], allReviews);
      
    } catch (error) {
      console.error('Error loading vendor reviews:', error);
      notification.error({
        message: 'Error Loading Reviews',
        description: error.message || 'Failed to load reviews. Please try again.',
        placement: 'topRight'
      });
    } finally {
      setLoading(false);
    }
  };

  // Calculate statistics
  const calculateStats = (currentReviews, allReviews) => {
    const total = allReviews.length;
    if (total === 0) {
      setStats({
        totalReviews: 0,
        averageRating: 0,
        repliedReviews: 0,
        pendingReplies: 0,
        ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
      });
      return;
    }

    const avgRating = allReviews.reduce((acc, review) => acc + review.rating, 0) / total;
    const replied = allReviews.filter(review => review.replies && review.replies.length > 0).length;
    const pending = total - replied;
    
    const distribution = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
    allReviews.forEach(review => {
      distribution[review.rating]++;
    });

    setStats({
      totalReviews: total,
      averageRating: avgRating,
      repliedReviews: replied,
      pendingReplies: pending,
      ratingDistribution: distribution
    });
  };

  // Handle reply submission
  const handleReplySubmit = async (values) => {
    if (!selectedReview) return;

    setSubmitting(true);
    try {
      await reviewService.replyToReview(selectedReview._id, values.message);
      
      notification.success({
        message: 'Reply Posted',
        description: 'Your reply has been posted successfully!',
        placement: 'topRight'
      });
      
      setReplyModalVisible(false);
      setSelectedReview(null);
      replyForm.resetFields();
      loadReviews(pagination.current, selectedRating, searchText);
      
    } catch (error) {
      notification.error({
        message: 'Error Posting Reply',
        description: error.message || 'Failed to post reply. Please try again.',
        placement: 'topRight'
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle reply update
  const handleReplyUpdate = async (values) => {
    if (!selectedReply) return;

    setSubmitting(true);
    try {
      await reviewService.updateReply(selectedReply._id, values.message);
      
      notification.success({
        message: 'Reply Updated',
        description: 'Your reply has been updated successfully!',
        placement: 'topRight'
      });
      
      setEditReplyModalVisible(false);
      setSelectedReply(null);
      editReplyForm.resetFields();
      loadReviews(pagination.current, selectedRating, searchText);
      
    } catch (error) {
      notification.error({
        message: 'Error Updating Reply',
        description: error.message || 'Failed to update reply. Please try again.',
        placement: 'topRight'
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Handle reply deletion
  const handleReplyDelete = (reply) => {
    Modal.confirm({
      title: 'Delete Reply',
      content: 'Are you sure you want to delete this reply? This action cannot be undone.',
      okText: 'Delete',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          await reviewService.deleteReply(reply._id);
          
          notification.success({
            message: 'Reply Deleted',
            description: 'Reply has been deleted successfully!',
            placement: 'topRight'
          });
          
          loadReviews(pagination.current, selectedRating, searchText);
        } catch (error) {
          notification.error({
            message: 'Error Deleting Reply',
            description: error.message || 'Failed to delete reply. Please try again.',
            placement: 'topRight'
          });
        }
      }
    });
  };

  // Handle table pagination
  const handleTableChange = (paginationInfo) => {
    loadReviews(paginationInfo.current, selectedRating, searchText);
  };

  // Handle rating filter change
  const handleRatingFilter = (rating) => {
    setSelectedRating(rating);
    loadReviews(1, rating, searchText);
  };

  // Handle search
  const handleSearch = (value) => {
    setSearchText(value);
    loadReviews(1, selectedRating, value);
  };

  useEffect(() => {
    loadReviews();
  }, []);

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Reviews Management</h1>
      
      <ReviewsStats stats={stats} />
      
      <ReviewsFilters
        selectedRating={selectedRating}
        searchText={searchText}
        onRatingFilter={handleRatingFilter}
        onSearch={handleSearch}
        onRefresh={() => loadReviews(pagination.current, selectedRating, searchText)}
        loading={loading}
      />
      
      <ReviewsTable
        reviews={reviews}
        loading={loading}
        pagination={pagination}
        onTableChange={handleTableChange}
        onReply={(review) => {
          setSelectedReview(review);
          setReplyModalVisible(true);
        }}
        onEditReply={(reply) => {
          setSelectedReply(reply);
          editReplyForm.setFieldValue('message', reply.message);
          setEditReplyModalVisible(true);
        }}
        onDeleteReply={handleReplyDelete}
      />
      
      <ReviewModals
        replyModalVisible={replyModalVisible}
        selectedReview={selectedReview}
        replyForm={replyForm}
        onReplySubmit={handleReplySubmit}
        onReplyCancel={() => {
          setReplyModalVisible(false);
          setSelectedReview(null);
          replyForm.resetFields();
        }}
        editReplyModalVisible={editReplyModalVisible}
        selectedReply={selectedReply}
        editReplyForm={editReplyForm}
        onReplyUpdate={handleReplyUpdate}
        onEditReplyCancel={() => {
          setEditReplyModalVisible(false);
          setSelectedReply(null);
          editReplyForm.resetFields();
        }}
        submitting={submitting}
      />
    </div>
  );
};

export default ReviewsManagement;
