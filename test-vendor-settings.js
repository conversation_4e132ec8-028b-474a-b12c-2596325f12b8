#!/usr/bin/env node

/**
 * Comprehensive Test Script for Vendor Settings Functionality
 * Tests all the modified vendor settings features including:
 * - Business profile updates
 * - User profile updates  
 * - Password reset functionality
 * - Real-time data fetching and updates
 */

const axios = require('axios');
const colors = require('colors');

// Configuration
const API_BASE_URL = process.env.API_URL || 'http://localhost:5000/api';
const TEST_EMAIL = process.env.TEST_EMAIL || '<EMAIL>';
const TEST_PASSWORD = process.env.TEST_PASSWORD || 'Free@009';

let authToken = '';
let vendorId = '';

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  total: 0
};

// Helper functions
const log = (message, type = 'info') => {
  const timestamp = new Date().toISOString();
  switch (type) {
    case 'success':
      console.log(`[${timestamp}] ✅ ${message}`.green);
      break;
    case 'error':
      console.log(`[${timestamp}] ❌ ${message}`.red);
      break;
    case 'warning':
      console.log(`[${timestamp}] ⚠️  ${message}`.yellow);
      break;
    case 'info':
    default:
      console.log(`[${timestamp}] ℹ️  ${message}`.blue);
      break;
  }
};

const runTest = async (testName, testFunction) => {
  testResults.total++;
  try {
    log(`Running test: ${testName}`, 'info');
    await testFunction();
    testResults.passed++;
    log(`✅ PASSED: ${testName}`, 'success');
  } catch (error) {
    testResults.failed++;
    log(`❌ FAILED: ${testName} - ${error.message}`, 'error');
    console.error('Error details:', error.response?.data || error.message);
  }
};

const makeRequest = async (method, endpoint, data = null, headers = {}) => {
  const config = {
    method,
    url: `${API_BASE_URL}${endpoint}`,
    headers: {
      'Content-Type': 'application/json',
      ...(authToken && { Authorization: `Bearer ${authToken}` }),
      ...headers
    }
  };

  if (data) {
    config.data = data;
  }

  return await axios(config);
};

// Test functions
const testLogin = async () => {
  log('Attempting vendor login...');
  const response = await makeRequest('POST', '/auth/login', {
    email: TEST_EMAIL,
    password: TEST_PASSWORD
  });

  console.log('Login response:', JSON.stringify(response.data, null, 2));

  if (response.data.success && response.data.data && response.data.data.token) {
    authToken = response.data.data.token;
    vendorId = response.data.data.user.id;
    log('Login successful', 'success');
  } else {
    throw new Error(`Login failed - Response: ${JSON.stringify(response.data)}`);
  }
};

const testGetVendorProfile = async () => {
  const response = await makeRequest('GET', '/vendor/store/profile');
  
  if (!response.data.success) {
    throw new Error('Failed to fetch vendor profile');
  }

  const profile = response.data.data;
  if (!profile.user || !profile.businessName) {
    throw new Error('Profile data incomplete');
  }

  log('Vendor profile fetched successfully', 'success');
  return profile;
};

const testUpdateBusinessProfile = async () => {
  const updateData = {
    businessName: 'Updated Test Business ' + Date.now(),
    businessDescription: 'This is an updated test business description for testing purposes',
    businessType: 'individual',
    contactInfo: {
      businessEmail: '<EMAIL>',
      businessPhone: '******-0123',
      website: 'https://www.updatedtestbusiness.com'
    },
    businessAddress: {
      street: '123 Updated Test Street',
      city: 'Test City',
      state: 'TC',
      zipCode: '12345',
      country: 'United States'
    },
    settings: {
      returnPolicy: 'Updated 30-day return policy',
      shippingPolicy: 'Updated free shipping on orders over $50',
      minimumOrderAmount: 25,
      processingTime: 3
    },
    taxId: 'TAX123456789',
    businessRegistrationNumber: 'BRN987654321'
  };

  const response = await makeRequest('PUT', '/vendor/store/profile', updateData);
  
  if (!response.data.success) {
    throw new Error('Failed to update business profile');
  }

  // Verify the update by fetching the profile again
  const updatedProfile = await testGetVendorProfile();
  if (updatedProfile.businessName !== updateData.businessName) {
    throw new Error('Business profile update not reflected');
  }

  log('Business profile updated successfully', 'success');
};

const testUpdateUserProfile = async () => {
  const updateData = {
    firstName: 'UpdatedFirst',
    lastName: 'UpdatedLast',
    phone: '******-9876',
    address: '456 Updated User Street, User City, UC 54321',
    preferences: {
      language: 'en',
      currency: 'INR'
    }
  };

  const response = await makeRequest('PUT', '/vendor/store/profile', updateData);
  
  if (!response.data.success) {
    throw new Error('Failed to update user profile');
  }

  // Verify the update
  const updatedProfile = await testGetVendorProfile();
  if (updatedProfile.user.firstName !== updateData.firstName) {
    throw new Error('User profile update not reflected');
  }

  log('User profile updated successfully', 'success');
};

const testPasswordReset = async () => {
  const newPassword = 'newtestpassword123';
  
  const passwordData = {
    security: {
      currentPassword: TEST_PASSWORD,
      newPassword: newPassword
    }
  };

  const response = await makeRequest('PUT', '/vendor/store/settings', passwordData);
  
  if (!response.data.success) {
    throw new Error('Failed to update password');
  }

  // Test login with new password
  try {
    const loginResponse = await makeRequest('POST', '/auth/login', {
      email: TEST_EMAIL,
      password: newPassword
    });

    if (!loginResponse.data.success) {
      throw new Error('Login with new password failed');
    }

    // Reset password back to original for other tests
    const resetBackData = {
      security: {
        currentPassword: newPassword,
        newPassword: TEST_PASSWORD
      }
    };
    
    authToken = loginResponse.data.data.token; // Update token
    await makeRequest('PUT', '/vendor/store/settings', resetBackData);
    
  } catch (error) {
    throw new Error('Password reset verification failed: ' + error.message);
  }

  log('Password reset functionality working correctly', 'success');
};

const testRealTimeDataFetch = async () => {
  // Test multiple rapid requests to ensure real-time data consistency
  const requests = [];
  for (let i = 0; i < 5; i++) {
    requests.push(makeRequest('GET', '/vendor/store/profile'));
  }

  const responses = await Promise.all(requests);
  
  // Verify all responses are successful and consistent
  for (let i = 0; i < responses.length; i++) {
    if (!responses[i].data.success) {
      throw new Error(`Request ${i + 1} failed`);
    }
    
    if (i > 0) {
      const currentProfile = responses[i].data.data;
      const previousProfile = responses[i - 1].data.data;
      
      if (currentProfile.businessName !== previousProfile.businessName) {
        throw new Error('Data inconsistency detected in concurrent requests');
      }
    }
  }

  log('Real-time data fetching working correctly', 'success');
};

const testDataPersistence = async () => {
  // Update data
  const testData = {
    businessName: 'Persistence Test Business ' + Date.now(),
    businessDescription: 'Testing data persistence across requests'
  };

  await makeRequest('PUT', '/vendor/store/profile', testData);
  
  // Wait a moment
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Fetch data again
  const profile = await testGetVendorProfile();
  
  if (profile.businessName !== testData.businessName) {
    throw new Error('Data not persisted correctly');
  }

  log('Data persistence working correctly', 'success');
};

const testErrorHandling = async () => {
  // Test invalid password reset
  try {
    await makeRequest('PUT', '/vendor/store/settings', {
      security: {
        currentPassword: 'wrongpassword',
        newPassword: 'newpassword123'
      }
    });
    throw new Error('Should have failed with wrong password');
  } catch (error) {
    if (error.response?.status !== 400) {
      throw new Error('Expected 400 error for wrong password');
    }
  }

  // Test empty business name
  try {
    await makeRequest('PUT', '/vendor/store/profile', {
      businessName: ''
    });
    // This might succeed depending on validation, so we don't throw here
  } catch (error) {
    // Expected validation error
  }

  log('Error handling working correctly', 'success');
};

const testFormValidation = async () => {
  // Test invalid email format
  try {
    await makeRequest('PUT', '/vendor/store/profile', {
      email: 'invalid-email-format'
    });
  } catch (error) {
    // May or may not fail depending on backend validation
  }

  // Test business profile required fields
  const profile = await testGetVendorProfile();
  if (!profile.businessName || !profile.user.firstName || !profile.user.lastName) {
    throw new Error('Required fields missing from profile');
  }

  log('Form validation working correctly', 'success');
};

// Main test runner
const runAllTests = async () => {
  console.log('🚀 Starting Vendor Settings Testing Suite'.bold.cyan);
  console.log(`Testing against: ${API_BASE_URL}`.gray);
  console.log(`Using test email: ${TEST_EMAIL}`.gray);
  console.log('=' .repeat(60));

  try {
    // Authentication test
    await runTest('User Login', testLogin);
    
    // Profile management tests
    await runTest('Get Vendor Profile', testGetVendorProfile);
    await runTest('Update Business Profile', testUpdateBusinessProfile);
    await runTest('Update User Profile', testUpdateUserProfile);
    
    // Security tests
    await runTest('Password Reset', testPasswordReset);
    
    // Performance and reliability tests
    await runTest('Real-time Data Fetch', testRealTimeDataFetch);
    await runTest('Data Persistence', testDataPersistence);
    
    // Error handling tests
    await runTest('Error Handling', testErrorHandling);
    await runTest('Form Validation', testFormValidation);

  } catch (error) {
    log(`Critical error during testing: ${error.message}`, 'error');
  }

  // Print results
  console.log('=' .repeat(60));
  console.log('📊 TEST RESULTS'.bold.cyan);
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`✅ Passed: ${testResults.passed}`.green);
  console.log(`❌ Failed: ${testResults.failed}`.red);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.failed === 0) {
    console.log('🎉 All tests passed! Vendor Settings functionality is working correctly.'.green.bold);
  } else {
    console.log('⚠️  Some tests failed. Please review the errors above.'.yellow.bold);
  }
  
  console.log('=' .repeat(60));
};

// Error handling for uncaught exceptions
process.on('unhandledRejection', (reason, promise) => {
  log(`Unhandled Rejection at: ${promise}, reason: ${reason}`, 'error');
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  log(`Uncaught Exception: ${error.message}`, 'error');
  process.exit(1);
});

// Run the tests
if (require.main === module) {
  runAllTests()
    .then(() => {
      process.exit(testResults.failed === 0 ? 0 : 1);
    })
    .catch((error) => {
      log(`Test suite failed: ${error.message}`, 'error');
      process.exit(1);
    });
}

module.exports = {
  runAllTests,
  testResults
};
