require('dotenv').config();
const mongoose = require('mongoose');

// Connect to MongoDB
async function connectDB() {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce';
    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
}

// Load models
const Order = require('./src/models/Order');

async function fixPaymentStatus() {
  await connectDB();
  
  console.log('\n🔧 FIXING PAYMENT STATUS FOR EXISTING ORDERS\n');
  
  // Find all orders with pending payment status
  const pendingOrders = await Order.find({ 'payment.status': 'pending' });
  
  console.log(`Found ${pendingOrders.length} orders with pending payment status`);
  
  if (pendingOrders.length > 0) {
    // Update all pending orders to completed
    const result = await Order.updateMany(
      { 'payment.status': 'pending' },
      { 
        $set: { 
          'payment.status': 'completed',
          'payment.paidAt': new Date()
        } 
      }
    );
    
    console.log(`✅ Updated ${result.modifiedCount} orders to completed payment status`);
    
    // Verify the changes
    const completedOrders = await Order.countDocuments({ 'payment.status': 'completed' });
    console.log(`Now there are ${completedOrders} orders with completed payment status`);
  }
  
  await mongoose.disconnect();
  console.log('✅ Fix complete!');
}

fixPaymentStatus().catch(console.error);
