/**
 * Fix the customerOrderController to manually generate orderNumber
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'src/controllers/customerOrderController.js');

// Read the file
let content = fs.readFileSync(filePath, 'utf8');

// Find and replace the order creation part
const oldOrderCreation = `    // Create the order
    const order = new Order({
      customer: req.user._id,
      items,
      billing,
      shipping,
      payment,
      pricing,
      customerNotes,
      status: 'confirmed',
      timeline: [{
        status: 'confirmed',
        timestamp: new Date(),
        note: 'Order has been confirmed',
        updatedBy: req.user._id
      }]
    });`;

const newOrderCreation = `    // Generate order number manually
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const timestamp = Date.now().toString().slice(-6);
    const orderNumber = \`ORD\${year}\${month}\${day}\${timestamp}\`;

    // Create the order
    const order = new Order({
      orderNumber: orderNumber,
      customer: req.user._id,
      items,
      billing,
      shipping,
      payment,
      pricing,
      customerNotes,
      status: 'confirmed',
      timeline: [{
        status: 'confirmed',
        timestamp: new Date(),
        note: 'Order has been confirmed',
        updatedBy: req.user._id
      }]
    });`;

// Replace the order creation
content = content.replace(oldOrderCreation, newOrderCreation);

// Write back to file
fs.writeFileSync(filePath, content);

console.log('✅ Fixed customerOrderController to manually generate orderNumber');
console.log('Added: Manual orderNumber generation before Order creation');