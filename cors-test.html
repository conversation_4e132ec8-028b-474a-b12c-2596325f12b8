<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Test</title>
</head>
<body>
    <h1>CORS Test</h1>
    <button onclick="testSearch()">Test Search API</button>
    <div id="result"></div>

    <script>
        async function testSearch() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Testing...</p>';
            
            try {
                const response = await fetch('http://localhost:5000/api/public/products/search?q=gojo&limit=50&sortBy=relevance&inStock=false', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <h3 style="color: green;">✅ Success!</h3>
                        <p>Status: ${response.status}</p>
                        <p>Results found: ${data.data?.products?.length || 0}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h3 style="color: red;">❌ HTTP Error</h3>
                        <p>Status: ${response.status}</p>
                        <p>Status Text: ${response.statusText}</p>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3 style="color: red;">❌ Network Error</h3>
                    <p>Error: ${error.message}</p>
                    <p>This might be a CORS issue if the server is not properly configured.</p>
                `;
                console.error('Request failed:', error);
            }
        }
    </script>
</body>
</html>
