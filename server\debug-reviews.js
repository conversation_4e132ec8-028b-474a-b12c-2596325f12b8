#!/usr/bin/env node

/**
 * Debug Script: Check existing reviews and resolve duplicate review issues
 */

const mongoose = require('mongoose');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '.env') });

// Import models
const Review = require('./src/models/Review');
const Product = require('./src/models/Product');
const User = require('./src/models/User');

// Configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce';
const PRODUCT_ID = '6881fd806652576061a0be95'; // The problematic product

/**
 * Connect to MongoDB
 */
async function connectDB() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

/**
 * Debug reviews for the problematic product
 */
async function debugReviews() {
  console.log('\n🔍 Debugging reviews for product:', PRODUCT_ID);
  
  try {
    // Check if product exists
    const product = await Product.findById(PRODUCT_ID).populate('vendor', 'businessName email');
    if (!product) {
      console.error('❌ Product not found');
      return;
    }
    
    console.log(`✅ Product found: ${product.name}`);
    console.log(`   - Vendor: ${product.vendor?.businessName} (${product.vendor?._id})`);
    
    // Find all reviews for this product
    const reviews = await Review.find({ product: PRODUCT_ID })
      .populate('customer', 'firstName lastName email userType')
      .populate('vendor', 'businessName email');
    
    console.log(`\n📊 Found ${reviews.length} review(s) for this product:`);
    
    reviews.forEach((review, index) => {
      console.log(`\n   Review ${index + 1}:`);
      console.log(`     - ID: ${review._id}`);
      console.log(`     - Customer: ${review.customer?.firstName} ${review.customer?.lastName} (${review.customer?.email})`);
      console.log(`     - Customer ID: ${review.customer?._id}`);
      console.log(`     - Customer Type: ${review.customer?.userType}`);
      console.log(`     - Rating: ${review.rating}/5`);
      console.log(`     - Comment: "${review.comment}"`);
      console.log(`     - Status: ${review.status}`);
      console.log(`     - Created: ${review.createdAt}`);
      console.log(`     - Vendor: ${review.vendor?.businessName} (${review.vendor?._id})`);
    });
    
    // Find customers to test with
    console.log('\n👥 Available customers for testing:');
    const customers = await User.find({ userType: 'customer' }).limit(5);
    customers.forEach((customer, index) => {
      const hasReviewed = reviews.some(review => 
        review.customer._id.toString() === customer._id.toString()
      );
      console.log(`   ${index + 1}. ${customer.firstName} ${customer.lastName} (${customer.email})`);
      console.log(`      - ID: ${customer._id}`);
      console.log(`      - Has reviewed this product: ${hasReviewed ? '✅ YES' : '❌ NO'}`);
    });
    
  } catch (error) {
    console.error('❌ Error debugging reviews:', error);
  }
}

/**
 * Test canReviewProduct function
 */
async function testCanReviewFunction() {
  console.log('\n🧪 Testing canReviewProduct function:');
  
  try {
    const customers = await User.find({ userType: 'customer' }).limit(3);
    
    for (const customer of customers) {
      const canReview = await Review.canCustomerReview(customer._id, PRODUCT_ID);
      console.log(`   - ${customer.firstName} ${customer.lastName}: ${canReview ? '✅ CAN review' : '❌ CANNOT review'}`);
    }
  } catch (error) {
    console.error('❌ Error testing canReviewProduct:', error);
  }
}

/**
 * Clean up duplicate or problematic reviews (optional)
 */
async function cleanupReviews() {
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  const answer = await new Promise(resolve => {
    readline.question('\n❓ Do you want to remove duplicate/problematic reviews? (y/N): ', resolve);
  });
  readline.close();
  
  if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
    try {
      // Find duplicate reviews (same customer, same product)
      const duplicates = await Review.aggregate([
        {
          $group: {
            _id: { customer: '$customer', product: '$product' },
            count: { $sum: 1 },
            docs: { $push: '$_id' }
          }
        },
        {
          $match: { count: { $gt: 1 } }
        }
      ]);
      
      console.log(`\n🔍 Found ${duplicates.length} duplicate review groups`);
      
      for (const duplicate of duplicates) {
        console.log(`   - Removing ${duplicate.count - 1} duplicate(s) for customer-product pair`);
        // Keep the first review, remove the rest
        const toRemove = duplicate.docs.slice(1);
        await Review.deleteMany({ _id: { $in: toRemove } });
      }
      
      console.log('✅ Cleanup completed');
    } catch (error) {
      console.error('❌ Error during cleanup:', error);
    }
  }
}

/**
 * Main function
 */
async function main() {
  console.log('🔧 Review Debug Script Starting...\n');
  
  try {
    await connectDB();
    await debugReviews();
    await testCanReviewFunction();
    await cleanupReviews();
    
    console.log('\n✅ Debug script completed successfully!');
  } catch (error) {
    console.error('\n❌ Debug script failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main };
