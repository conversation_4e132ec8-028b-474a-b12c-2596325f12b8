const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

async function debugCarouselWithAuth() {
  console.log('🔍 Debugging Carousel Upload with Admin Authentication...\n');
  
  const BASE_URL = 'http://localhost:5000';
  const ADMIN_CREDENTIALS = {
    email: '<EMAIL>',
    password: 'password@admin123'
  };
  
  try {
    // Step 1: Check server status
    console.log('1. Checking server status...');
    try {
      const healthCheck = await axios.get(`${BASE_URL}/api/health`);
      console.log('✅ Server is running');
    } catch (error) {
      console.log('⚠️ Server health check failed, but continuing...');
    }
    
    // Step 2: Login as admin to get token
    console.log('\n2. Logging in as admin...');
    let adminToken = null;
    
    try {
      const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, ADMIN_CREDENTIALS);
      
      if (loginResponse.data.success && loginResponse.data.token) {
        adminToken = loginResponse.data.token;
        console.log('✅ Successfully logged in as admin');
        console.log(`📋 Admin user: ${loginResponse.data.user.email} (${loginResponse.data.user.userType})`);
      } else {
        console.log('❌ Login failed:', loginResponse.data);
        return;
      }
    } catch (error) {
      console.log('❌ Login error:', error.response?.data || error.message);
      return;
    }
    
    // Step 3: Create test image
    console.log('\n3. Creating test image...');
    const testImagePath = path.join(__dirname, 'test-carousel-auth.jpg');
    
    // Create a proper JPEG file
    const jpegHeader = Buffer.from([
      0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
      0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
      0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
      0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
      0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
      0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
      0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
      0x3C, 0x2E, 0x33, 0x34, 0x32
    ]);
    const jpegFooter = Buffer.from([0xFF, 0xD9]);
    const jpegBody = Buffer.alloc(1000, 0x00); // Larger body for more realistic file
    
    const testImageData = Buffer.concat([jpegHeader, jpegBody, jpegFooter]);
    fs.writeFileSync(testImagePath, testImageData);
    console.log(`✅ Created test image: ${testImagePath} (${testImageData.length} bytes)`);
    
    // Step 4: Test carousel upload with proper authentication
    console.log('\n4. Testing carousel upload with authentication...');
    
    const form = new FormData();
    form.append('image', fs.createReadStream(testImagePath), {
      filename: 'test-carousel-auth.jpg',
      contentType: 'image/jpeg'
    });
    form.append('title', 'Authenticated Test Carousel');
    form.append('description', 'Testing carousel upload with proper admin authentication');
    form.append('linkUrl', 'https://example.com/auth-test');
    
    console.log('📦 FormData contents:');
    console.log('  - image: File (test-carousel-auth.jpg, image/jpeg)');
    console.log('  - title: "Authenticated Test Carousel"');
    console.log('  - description: "Testing carousel upload with proper admin authentication"');
    console.log('  - linkUrl: "https://example.com/auth-test"');
    
    try {
      console.log('📤 Sending authenticated request...');
      const response = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, form, {
        headers: {
          ...form.getHeaders(),
          'Authorization': `Bearer ${adminToken}`
        },
        timeout: 30000,
        maxContentLength: Infinity,
        maxBodyLength: Infinity
      });
      
      console.log('✅ Upload successful!');
      console.log(`📥 Response status: ${response.status}`);
      console.log('📄 Response data:');
      console.log(JSON.stringify(response.data, null, 2));
      
    } catch (error) {
      console.log('❌ Upload failed!');
      console.log(`📥 Response status: ${error.response?.status}`);
      console.log('📄 Error details:');
      console.log(JSON.stringify(error.response?.data, null, 2));
      
      // Analyze the specific error
      if (error.response?.data?.message === 'Image file is required') {
        console.log('\n🔍 DIAGNOSIS: File not received by server');
        console.log('This indicates:');
        console.log('- FormData is not properly constructed');
        console.log('- Multer middleware is not receiving the file');
        console.log('- Field name mismatch (should be "image")');
        console.log('- File stream issue');
      } else if (error.response?.status === 401) {
        console.log('\n🔍 DIAGNOSIS: Authentication issue');
        console.log('- Token may be invalid or expired');
        console.log('- Admin middleware rejecting request');
      } else if (error.response?.status === 413) {
        console.log('\n🔍 DIAGNOSIS: File too large');
        console.log('- File exceeds size limits');
      } else if (error.code === 'ECONNABORTED') {
        console.log('\n🔍 DIAGNOSIS: Request timeout');
        console.log('- Upload taking too long');
        console.log('- Cloudinary upload timeout');
      }
    }
    
    // Step 5: Test without file to confirm validation
    console.log('\n5. Testing without file (should fail with validation error)...');
    
    const formNoFile = new FormData();
    formNoFile.append('title', 'test');
    formNoFile.append('description', 'test');
    formNoFile.append('linkUrl', '');
    
    try {
      const responseNoFile = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, formNoFile, {
        headers: {
          ...formNoFile.getHeaders(),
          'Authorization': `Bearer ${adminToken}`
        },
        timeout: 10000
      });
      
      console.log('❌ Unexpected success without file:', responseNoFile.status);
      
    } catch (error) {
      if (error.response?.data?.message === 'Image file is required') {
        console.log('✅ Correctly rejected request without file');
        console.log('Server-side validation is working properly');
      } else {
        console.log('❌ Unexpected error:', error.response?.status, error.response?.data);
      }
    }
    
    // Step 6: Test getting homepage settings
    console.log('\n6. Testing GET homepage settings...');
    
    try {
      const getResponse = await axios.get(`${BASE_URL}/api/admin/homepage-settings`, {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });
      
      console.log('✅ Successfully retrieved homepage settings');
      console.log(`📊 Current carousel images: ${getResponse.data.data.carouselImages?.length || 0}`);
      
      if (getResponse.data.data.carouselImages?.length > 0) {
        console.log('📋 Existing carousel images:');
        getResponse.data.data.carouselImages.forEach((img, index) => {
          console.log(`  ${index + 1}. ${img.title} - ${img.imageUrl}`);
        });
      }
      
    } catch (error) {
      console.log('❌ Failed to get homepage settings:', error.response?.data);
    }
    
    // Step 7: Provide debugging summary
    console.log('\n7. 🔧 DEBUGGING SUMMARY:');
    console.log('');
    console.log('✅ Authentication: Working (admin login successful)');
    console.log('✅ Server: Running and accessible');
    console.log('✅ Validation: Working (rejects requests without files)');
    console.log('');
    
    console.log('If the file upload is still failing, check:');
    console.log('');
    console.log('🔍 Frontend Issues:');
    console.log('- Ensure form field name is exactly "image"');
    console.log('- Verify FormData construction in frontend');
    console.log('- Check if file is actually selected');
    console.log('- Ensure form has enctype="multipart/form-data"');
    console.log('');
    console.log('🔍 Server Issues:');
    console.log('- Check Cloudinary configuration');
    console.log('- Verify multer middleware setup');
    console.log('- Check file size limits');
    console.log('- Review server logs for multer errors');
    console.log('');
    console.log('🔍 Network Issues:');
    console.log('- Check browser network tab for request details');
    console.log('- Verify CORS settings');
    console.log('- Check for proxy/firewall issues');
    
    // Cleanup
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
      console.log('\n🧹 Cleaned up test files');
    }
    
  } catch (error) {
    console.error('❌ Debug script failed:', error.message);
  }
}

// Run the debug script
debugCarouselWithAuth().catch(console.error);