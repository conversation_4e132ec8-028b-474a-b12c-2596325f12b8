const axios = require('axios');

// Test CORS configuration
async function testCORS() {
  try {
    console.log('Testing CORS configuration...');
    
    // Test OPTIONS request (preflight)
    console.log('\n1. Testing OPTIONS request (preflight)...');
    const optionsResponse = await axios({
      method: 'OPTIONS',
      url: 'http://localhost:5000/api/public/products/search',
      headers: {
        'Origin': 'http://localhost:5173',
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type'
      }
    });
    
    console.log('✅ OPTIONS request successful');
    console.log('Headers received:', optionsResponse.headers);
    
    // Test actual GET request
    console.log('\n2. Testing GET request with search...');
    const getResponse = await axios({
      method: 'GET',
      url: 'http://localhost:5000/api/public/products/search?q=test',
      headers: {
        'Origin': 'http://localhost:5173',
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ GET request successful');
    console.log('Response status:', getResponse.status);
    console.log('CORS headers:', {
      'Access-Control-Allow-Origin': getResponse.headers['access-control-allow-origin'],
      'Access-Control-Allow-Credentials': getResponse.headers['access-control-allow-credentials'],
      'Access-Control-Allow-Methods': getResponse.headers['access-control-allow-methods']
    });
    
    console.log('\n🎉 All CORS tests passed!');
    
  } catch (error) {
    console.error('❌ CORS test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response headers:', error.response.headers);
    }
  }
}

// Test health endpoint first
async function testHealth() {
  try {
    console.log('Testing server health...');
    const response = await axios.get('http://localhost:5000/api/health');
    console.log('✅ Server is healthy:', response.data.message);
    return true;
  } catch (error) {
    console.error('❌ Server health check failed:', error.message);
    return false;
  }
}

async function runTests() {
  const isHealthy = await testHealth();
  if (isHealthy) {
    await testCORS();
  } else {
    console.log('❌ Server is not responding. Please ensure the server is running on port 5000.');
  }
}

runTests();
