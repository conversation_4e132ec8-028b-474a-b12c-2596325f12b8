<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Cart Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .test-log { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>Admin Cart Fix Test</h1>
    <p>This test verifies that admin users cannot access cart functionality.</p>

    <div class="test-section">
        <h3>Test Setup</h3>
        <p>This test simulates different user types accessing cart functionality.</p>
        <button onclick="testCustomerAccess()">Test Customer Cart Access</button>
        <button onclick="testAdminAccess()">Test Admin Cart Access</button>
        <button onclick="testVendorAccess()">Test Vendor Cart Access</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div class="test-section">
        <h3>Test Results</h3>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h3>Test Log</h3>
        <div id="test-log" class="test-log"></div>
    </div>

    <script>
        let logElement = document.getElementById('test-log');
        let resultsElement = document.getElementById('test-results');
        
        function log(message) {
            console.log(message);
            logElement.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            resultsElement.appendChild(div);
        }
        
        function clearResults() {
            resultsElement.innerHTML = '';
            logElement.textContent = '';
        }
        
        // Simulate cart context validation logic
        function simulateCartAccess(userType) {
            log(`Testing cart access for user type: ${userType}`);
            
            // Simulate the validateCartAccess function
            const user = { userType };
            
            if (user.userType !== 'customer') {
                const reason = `User type is '${user.userType}', but cart requires 'customer'`;
                log(`Cart access denied: ${reason}`);
                return {
                    success: false,
                    userType,
                    reason,
                    message: `Cart functionality is only available for customers. ${user.userType.charAt(0).toUpperCase() + user.userType.slice(1)}s cannot add items to cart.`
                };
            }
            
            log(`Cart access granted for customer user`);
            return {
                success: true,
                userType,
                message: 'Cart access granted'
            };
        }
        
        // Simulate the isNonCustomer check from CartContext
        function isNonCustomer(userType) {
            return userType !== 'customer';
        }
        
        function testCustomerAccess() {
            log('=== Testing Customer Cart Access ===');
            const result = simulateCartAccess('customer');
            
            if (result.success) {
                addResult('✅ Customer can access cart functionality', 'success');
                log('Test PASSED: Customer has cart access');
            } else {
                addResult('❌ Customer cannot access cart functionality', 'error');
                log('Test FAILED: Customer should have cart access');
            }
        }
        
        function testAdminAccess() {
            log('=== Testing Admin Cart Access ===');
            const result = simulateCartAccess('admin');
            
            if (!result.success) {
                addResult('✅ Admin correctly blocked from cart functionality', 'success');
                addResult(`Reason: ${result.reason}`, 'info');
                log('Test PASSED: Admin properly blocked from cart');
                
                // Test the notification message
                if (result.message.includes('Admins cannot add items to cart')) {
                    addResult('✅ Admin-specific error message displayed', 'success');
                    log('Test PASSED: Admin error message is correct');
                } else {
                    addResult('⚠️ Generic error message shown (not admin-specific)', 'info');
                    log('Note: Using generic error message instead of admin-specific');
                }
            } else {
                addResult('❌ Admin can access cart functionality (BUG!)', 'error');
                log('Test FAILED: Admin should be blocked from cart');
            }
        }
        
        function testVendorAccess() {
            log('=== Testing Vendor Cart Access ===');
            const result = simulateCartAccess('vendor');
            
            if (!result.success) {
                addResult('✅ Vendor correctly blocked from cart functionality', 'success');
                addResult(`Reason: ${result.reason}`, 'info');
                log('Test PASSED: Vendor properly blocked from cart');
                
                // Test the notification message
                if (result.message.includes('Vendors cannot add items to cart')) {
                    addResult('✅ Vendor-specific error message displayed', 'success');
                    log('Test PASSED: Vendor error message is correct');
                } else {
                    addResult('⚠️ Generic error message shown (not vendor-specific)', 'info');
                    log('Note: Using generic error message instead of vendor-specific');
                }
            } else {
                addResult('❌ Vendor can access cart functionality (BUG!)', 'error');
                log('Test FAILED: Vendor should be blocked from cart');
            }
        }
        
        // Test UI visibility logic
        function testNavbarVisibility() {
            log('=== Testing Navbar Cart Visibility ===');
            
            const testCases = [
                { userType: 'customer', shouldShow: true },
                { userType: 'vendor', shouldShow: false },
                { userType: 'admin', shouldShow: false },
                { userType: null, shouldShow: false },
                { userType: undefined, shouldShow: false }
            ];
            
            testCases.forEach(testCase => {
                const shouldShow = (testCase.userType === 'customer');
                const result = shouldShow === testCase.shouldShow;
                
                if (result) {
                    addResult(`✅ Cart ${shouldShow ? 'shown' : 'hidden'} for ${testCase.userType || 'unauthenticated'} user`, 'success');
                    log(`Test PASSED: Cart visibility correct for ${testCase.userType || 'unauthenticated'}`);
                } else {
                    addResult(`❌ Cart visibility incorrect for ${testCase.userType || 'unauthenticated'} user`, 'error');
                    log(`Test FAILED: Cart visibility wrong for ${testCase.userType || 'unauthenticated'}`);
                }
            });
        }
        
        // Auto-run navbar visibility test on page load
        window.addEventListener('load', () => {
            log('Admin Cart Fix Test initialized');
            testNavbarVisibility();
        });
    </script>
</body>
</html>
