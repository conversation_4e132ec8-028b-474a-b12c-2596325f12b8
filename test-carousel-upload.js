const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

async function testCarouselUpload() {
  try {
    console.log('Testing carousel image upload...');
    
    // First, let's test authentication and get settings
    console.log('\n1. Testing GET /api/admin/homepage-settings (without auth)...');
    try {
      const getResponse = await axios.get('http://localhost:5000/api/admin/homepage-settings');
      console.log('GET Response:', getResponse.data);
    } catch (error) {
      console.log('GET Error (expected - needs auth):', error.response?.status, error.response?.data);
    }
    
    // Create a test image file for upload
    const testImagePath = path.join(__dirname, 'test-image.jpg');
    if (!fs.existsSync(testImagePath)) {
      // Create a minimal valid JPEG file for testing
      const jpeg_header = Buffer.from([0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46]);
      const jpeg_footer = Buffer.from([0xFF, 0xD9]);
      const testImageData = Buffer.concat([jpeg_header, Buffer.alloc(100, 0x00), jpeg_footer]);
      fs.writeFileSync(testImagePath, testImageData);
      console.log('Created test image file:', testImagePath);
    }
    
    // Try to find an admin token or create one for testing
    console.log('\n2. Testing POST /api/admin/homepage-settings/carousel (without auth)...');
    const form = new FormData();
    form.append('image', fs.createReadStream(testImagePath));
    form.append('title', 'Test Carousel Image');
    form.append('description', 'This is a test carousel image');
    form.append('linkUrl', 'https://example.com');
    
    try {
      const response = await axios.post('http://localhost:5000/api/admin/homepage-settings/carousel', form, {
        headers: {
          ...form.getHeaders(),
        },
      });
      console.log('Upload Response:', response.data);
    } catch (error) {
      console.log('Upload Error:', error.response?.status, error.response?.statusText);
      console.log('Error Data:', error.response?.data);
      console.log('Request Headers:', error.config?.headers);
    }
    
    // Test with different content types
    console.log('\n3. Testing with application/json content type (should fail)...');
    try {
      const jsonResponse = await axios.post('http://localhost:5000/api/admin/homepage-settings/carousel', {
        title: 'Test Carousel Image',
        description: 'This is a test carousel image',
        linkUrl: 'https://example.com'
      }, {
        headers: {
          'Content-Type': 'application/json',
        },
      });
      console.log('JSON Response:', jsonResponse.data);
    } catch (error) {
      console.log('JSON Error:', error.response?.status, error.response?.data);
    }
    
    // Cleanup
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
      console.log('\nCleaned up test image file');
    }
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

// Check if server is running
const net = require('net');
const server = net.createServer();

server.listen(5000, (err) => {
  if (err) {
    console.log('Server is running on port 5000, proceeding with tests...');
    server.close();
    testCarouselUpload();
  } else {
    console.log('No server found on port 5000');
    server.close();
  }
});

server.on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.log('Server is running on port 5000, proceeding with tests...');
    testCarouselUpload();
  } else {
    console.log('Error checking server:', err.message);
  }
});
