<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fixed Carousel Upload Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .file-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎠 Fixed Carousel Upload</h1>
        <p>This example shows the correct way to implement carousel image upload with proper error handling.</p>
        
        <!-- IMPORTANT: enctype="multipart/form-data" is required for file uploads -->
        <form id="carouselForm" enctype="multipart/form-data">
            <div class="form-group">
                <label for="imageFile">Image File (Required) *:</label>
                <!-- IMPORTANT: name="image" must match server expectation -->
                <input type="file" id="imageFile" name="image" accept="image/*" required>
                <div id="fileInfo" class="file-info" style="display: none;"></div>
            </div>
            
            <div class="form-group">
                <label for="title">Title *:</label>
                <input type="text" id="title" name="title" required placeholder="Enter carousel title">
            </div>
            
            <div class="form-group">
                <label for="description">Description:</label>
                <textarea id="description" name="description" rows="3" placeholder="Enter description (optional)"></textarea>
            </div>
            
            <div class="form-group">
                <label for="linkUrl">Link URL:</label>
                <input type="url" id="linkUrl" name="linkUrl" placeholder="https://example.com (optional)">
            </div>
            
            <button type="submit" id="submitBtn">Upload Carousel Image</button>
        </form>
        
        <div id="status"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';
        let adminToken = null;
        
        // Show file information when selected
        document.getElementById('imageFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const fileInfo = document.getElementById('fileInfo');
            
            if (file) {
                fileInfo.style.display = 'block';
                fileInfo.innerHTML = `
                    <strong>Selected File:</strong><br>
                    Name: ${file.name}<br>
                    Size: ${(file.size / 1024).toFixed(2)} KB<br>
                    Type: ${file.type}
                `;
                
                // Validate file
                validateFile(file);
            } else {
                fileInfo.style.display = 'none';
            }
        });
        
        function validateFile(file) {
            const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
            const maxSize = 5 * 1024 * 1024; // 5MB
            
            if (!allowedTypes.includes(file.type)) {
                showStatus('Please select a valid image file (PNG, JPEG, GIF, WebP)', 'error');
                return false;
            }
            
            if (file.size > maxSize) {
                showStatus('File size must be less than 5MB', 'error');
                return false;
            }
            
            showStatus('File validation passed', 'success');
            return true;
        }
        
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }
        
        async function getAdminToken() {
            if (adminToken) return adminToken;
            
            try {
                showStatus('Authenticating...', 'info');
                
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password@admin123'
                    })
                });
                
                const data = await response.json();
                
                if (data.success && (data.token || data.data?.token)) {
                    adminToken = data.token || data.data.token;
                    showStatus('Authentication successful', 'success');
                    return adminToken;
                } else {
                    throw new Error(data.message || 'Authentication failed');
                }
                
            } catch (error) {
                showStatus(`Authentication error: ${error.message}`, 'error');
                return null;
            }
        }
        
        // Handle form submission
        document.getElementById('carouselForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.textContent = 'Uploading...';
            
            try {
                // Get admin token
                const token = await getAdminToken();
                if (!token) {
                    throw new Error('Authentication required');
                }
                
                // Validate form
                const imageFile = document.getElementById('imageFile').files[0];
                const title = document.getElementById('title').value.trim();
                
                if (!imageFile) {
                    throw new Error('Please select an image file');
                }
                
                if (!title) {
                    throw new Error('Please enter a title');
                }
                
                if (!validateFile(imageFile)) {
                    throw new Error('File validation failed');
                }
                
                // Create FormData - IMPORTANT: Don't set Content-Type header manually
                const formData = new FormData();
                formData.append('image', imageFile); // MUST be named 'image'
                formData.append('title', title);
                formData.append('description', document.getElementById('description').value.trim());
                formData.append('linkUrl', document.getElementById('linkUrl').value.trim());
                
                showStatus('Uploading image...', 'info');
                
                // Upload to server
                const response = await fetch(`${API_BASE}/api/admin/homepage-settings/carousel`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                        // DON'T set Content-Type - let browser set it with boundary
                    },
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showStatus('✅ Carousel image uploaded successfully!', 'success');
                    
                    // Reset form
                    document.getElementById('carouselForm').reset();
                    document.getElementById('fileInfo').style.display = 'none';
                    
                    // Show uploaded image info
                    const uploadedImages = result.data.carouselImages || [];
                    const latestImage = uploadedImages[uploadedImages.length - 1];
                    if (latestImage) {
                        setTimeout(() => {
                            showStatus(`✅ Image uploaded: "${latestImage.title}" - View at: ${latestImage.imageUrl}`, 'success');
                        }, 2000);
                    }
                    
                } else {
                    throw new Error(result.message || result.error || 'Upload failed');
                }
                
            } catch (error) {
                showStatus(`❌ Upload failed: ${error.message}`, 'error');
                console.error('Upload error:', error);
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Upload Carousel Image';
            }
        });
        
        // Initialize
        showStatus('Ready to upload. Please select an image file and fill in the details.', 'info');
    </script>
</body>
</html>