#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function updatePackageJsonScripts() {
    console.log('🔧 Setting up testing scripts in package.json files...\n');
    
    // Update server package.json
    const serverPackagePath = path.join(process.cwd(), 'server', 'package.json');
    if (fs.existsSync(serverPackagePath)) {
        console.log('📦 Updating server package.json...');
        
        try {
            const serverPackage = JSON.parse(fs.readFileSync(serverPackagePath, 'utf8'));
            
            // Add testing scripts
            if (!serverPackage.scripts) {
                serverPackage.scripts = {};
            }
            
            serverPackage.scripts = {
                ...serverPackage.scripts,
                "test": "jest",
                "test:watch": "jest --watch",
                "test:coverage": "jest --coverage",
                "test:verbose": "jest --verbose",
                "analyze-components": "node ../large-components-test-analysis.js",
                "generate-tests": "node ../generate-test-templates.js"
            };
            
            // Add testing dependencies if not present
            if (!serverPackage.devDependencies) {
                serverPackage.devDependencies = {};
            }
            
            const testingDeps = {
                "jest": "^29.7.0",
                "supertest": "^6.3.3",
                "@types/jest": "^29.5.5",
                "@types/supertest": "^2.0.12"
            };
            
            Object.keys(testingDeps).forEach(dep => {
                if (!serverPackage.devDependencies[dep] && !serverPackage.dependencies?.[dep]) {
                    serverPackage.devDependencies[dep] = testingDeps[dep];
                }
            });
            
            fs.writeFileSync(serverPackagePath, JSON.stringify(serverPackage, null, 2));
            console.log('   ✅ Server package.json updated');
            
        } catch (error) {
            console.log('   ❌ Failed to update server package.json:', error.message);
        }
    }
    
    // Update client package.json
    const clientPackagePath = path.join(process.cwd(), 'client', 'package.json');
    if (fs.existsSync(clientPackagePath)) {
        console.log('📦 Updating client package.json...');
        
        try {
            const clientPackage = JSON.parse(fs.readFileSync(clientPackagePath, 'utf8'));
            
            // Add testing scripts
            if (!clientPackage.scripts) {
                clientPackage.scripts = {};
            }
            
            clientPackage.scripts = {
                ...clientPackage.scripts,
                "test": "vitest",
                "test:ui": "vitest --ui",
                "test:coverage": "vitest --coverage",
                "test:watch": "vitest --watch",
                "analyze-components": "node ../large-components-test-analysis.js",
                "generate-tests": "node ../generate-test-templates.js"
            };
            
            // Add testing dependencies if not present
            if (!clientPackage.devDependencies) {
                clientPackage.devDependencies = {};
            }
            
            const testingDeps = {
                "vitest": "^1.0.0",
                "@testing-library/react": "^14.0.0",
                "@testing-library/jest-dom": "^6.1.0",
                "@testing-library/user-event": "^14.5.0",
                "@vitest/ui": "^1.0.0",
                "@vitest/coverage-v8": "^1.0.0",
                "jsdom": "^23.0.0"
            };
            
            Object.keys(testingDeps).forEach(dep => {
                if (!clientPackage.devDependencies[dep] && !clientPackage.dependencies?.[dep]) {
                    clientPackage.devDependencies[dep] = testingDeps[dep];
                }
            });
            
            fs.writeFileSync(clientPackagePath, JSON.stringify(clientPackage, null, 2));
            console.log('   ✅ Client package.json updated');
            
        } catch (error) {
            console.log('   ❌ Failed to update client package.json:', error.message);
        }
    }
    
    // Update root package.json
    const rootPackagePath = path.join(process.cwd(), 'package.json');
    if (fs.existsSync(rootPackagePath)) {
        console.log('📦 Updating root package.json...');
        
        try {
            const rootPackage = JSON.parse(fs.readFileSync(rootPackagePath, 'utf8'));
            
            // Add testing scripts
            if (!rootPackage.scripts) {
                rootPackage.scripts = {};
            }
            
            rootPackage.scripts = {
                ...rootPackage.scripts,
                "test": "npm run test:server && npm run test:client",
                "test:server": "cd server && npm test",
                "test:client": "cd client && npm test",
                "test:coverage": "npm run test:server:coverage && npm run test:client:coverage",
                "test:server:coverage": "cd server && npm run test:coverage",
                "test:client:coverage": "cd client && npm run test:coverage",
                "analyze-components": "node large-components-test-analysis.js",
                "generate-tests": "node generate-test-templates.js",
                "setup-testing": "node setup-testing-scripts.js"
            };
            
            fs.writeFileSync(rootPackagePath, JSON.stringify(rootPackage, null, 2));
            console.log('   ✅ Root package.json updated');
            
        } catch (error) {
            console.log('   ❌ Failed to update root package.json:', error.message);
        }
    }
    
    console.log('\n✅ Package.json scripts setup completed!');
    console.log('\n�� Available commands:');
    console.log('   npm run analyze-components    - Analyze component sizes and test coverage');
    console.log('   npm run generate-tests        - Generate test templates for large components');
    console.log('   npm run test                  - Run all tests');
    console.log('   npm run test:server           - Run backend tests');
    console.log('   npm run test:client           - Run frontend tests');
    console.log('   npm run test:coverage         - Run tests with coverage report');
    console.log('\n🚀 Next steps:');
    console.log('   1. Run: npm install (in both client and server directories)');
    console.log('   2. Run: npm run analyze-components');
    console.log('   3. Run: npm run generate-tests');
    console.log('   4. Start writing tests for your components!');
}

updatePackageJsonScripts();