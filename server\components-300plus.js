#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// File extensions to analyze
const FRONTEND_EXTENSIONS = ['.js', '.jsx', '.ts', '.tsx', '.vue', '.css', '.scss', '.sass'];
const BACKEND_EXTENSIONS = ['.js', '.ts', '.py', '.java', '.php', '.rb', '.go', '.cs'];

// Directories to exclude
const EXCLUDE_DIRS = [
    'node_modules', 
    '.git', 
    'dist', 
    'build', 
    'coverage', 
    '.next', 
    '.nuxt',
    '__pycache__',
    'vendor',
    'uploads',
    '.vscode',
    '.augment',
    '.qodo'
];

// Files to exclude
const EXCLUDE_FILES = [
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',
    '.DS_Store',
    'Thumbs.db'
];

function countLines(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');
        
        let codeLines = 0;
        let totalLines = lines.length;
        let commentLines = 0;
        let emptyLines = 0;
        
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed === '') {
                emptyLines++;
            } else if (trimmed.startsWith('//') || trimmed.startsWith('/*') || trimmed.startsWith('*') || trimmed.startsWith('#')) {
                commentLines++;
            } else {
                codeLines++;
            }
        }
        
        return {
            total: totalLines,
            code: codeLines,
            comments: commentLines,
            empty: emptyLines
        };
    } catch (error) {
        return { total: 0, code: 0, comments: 0, empty: 0 };
    }
}

function shouldExcludeDir(dirName) {
    return EXCLUDE_DIRS.some(exclude => dirName.includes(exclude));
}

function shouldExcludeFile(fileName) {
    return EXCLUDE_FILES.includes(fileName);
}

function getFileType(filePath, baseDir) {
    const ext = path.extname(filePath).toLowerCase();
    
    if (baseDir.includes('client') || baseDir.includes('frontend')) {
        if (FRONTEND_EXTENSIONS.includes(ext)) return 'Frontend';
    }
    
    if (baseDir.includes('server') || baseDir.includes('backend') || baseDir.includes('api')) {
        if (BACKEND_EXTENSIONS.includes(ext)) return 'Backend';
    }
    
    // Fallback based on extension
    if (FRONTEND_EXTENSIONS.includes(ext)) return 'Frontend';
    if (BACKEND_EXTENSIONS.includes(ext)) return 'Backend';
    
    return 'Other';
}

function getComponentType(filePath) {
    const pathLower = filePath.toLowerCase();
    const fileName = path.basename(filePath).toLowerCase();
    
    // Backend types
    if (pathLower.includes('controller')) return 'Controller';
    if (pathLower.includes('model')) return 'Model';
    if (pathLower.includes('service')) return 'Service';
    if (pathLower.includes('middleware')) return 'Middleware';
    if (pathLower.includes('route')) return 'Route';
    if (pathLower.includes('validator') || pathLower.includes('validation')) return 'Validator';
    if (pathLower.includes('util')) return 'Utility';
    if (pathLower.includes('schema')) return 'Schema';
    
    // Frontend types
    if (pathLower.includes('page') || fileName.includes('page')) return 'Page';
    if (pathLower.includes('context')) return 'Context';
    if (pathLower.includes('hook')) return 'Hook';
    if (fileName.endsWith('.jsx') || fileName.endsWith('.tsx')) return 'Component';
    if (fileName.endsWith('.css') || fileName.endsWith('.scss')) return 'Style';
    
    return 'Other';
}

function analyzeDirectory(dirPath, results = [], baseDir = '') {
    try {
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                if (!shouldExcludeDir(item)) {
                    analyzeDirectory(fullPath, results, baseDir || item);
                }
            } else if (stat.isFile()) {
                if (!shouldExcludeFile(item)) {
                    const lines = countLines(fullPath);
                    
                    // Only include files with 300+ lines
                    if (lines.total >= 300) {
                        const fileType = getFileType(fullPath, baseDir);
                        const componentType = getComponentType(fullPath);
                        const relativePath = path.relative(path.join(__dirname, '..'), fullPath);
                        
                        results.push({
                            name: item,
                            path: relativePath,
                            type: fileType,
                            componentType: componentType,
                            lines: lines,
                            size: stat.size
                        });
                    }
                }
            }
        }
    } catch (error) {
        console.error(`Error analyzing directory ${dirPath}:`, error.message);
    }
    
    return results;
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function generateReport(results) {
    // Sort by total lines (descending)
    const sortedResults = results.sort((a, b) => b.lines.total - a.lines.total);
    
    console.log('\n' + '='.repeat(120));
    console.log('COMPONENTS WITH 300+ LINES OF CODE - REFACTORING PRIORITY LIST');
    console.log('='.repeat(120));
    console.log(`Found ${results.length} components with 300+ lines of code\n`);
    
    // All components with 300+ lines
    console.log('RANK | TOTAL | CODE  | TYPE      | COMPONENT TYPE | SIZE     | FILE NAME');
    console.log('-'.repeat(120));
    
    sortedResults.forEach((file, index) => {
        const rank = (index + 1).toString().padStart(4);
        const total = file.lines.total.toString().padStart(5);
        const code = file.lines.code.toString().padStart(5);
        const type = file.type.padEnd(9);
        const componentType = file.componentType.padEnd(14);
        const size = formatBytes(file.size).padStart(8);
        
        console.log(`${rank} | ${total} | ${code} | ${type} | ${componentType} | ${size} | ${file.name}`);
        console.log(`     |       |       |           |                |          | ${file.path}`);
        console.log('-'.repeat(120));
    });
    
    // Separate by Frontend and Backend
    const frontendFiles = sortedResults.filter(f => f.type === 'Frontend');
    const backendFiles = sortedResults.filter(f => f.type === 'Backend');
    
    if (frontendFiles.length > 0) {
        console.log('\n' + '='.repeat(120));
        console.log('FRONTEND COMPONENTS (300+ lines) - REFACTORING PRIORITY');
        console.log('='.repeat(120));
        console.log('RANK | TOTAL | CODE  | COMPONENT TYPE | FILE NAME & PATH');
        console.log('-'.repeat(120));
        
        frontendFiles.forEach((file, index) => {
            const rank = (index + 1).toString().padStart(4);
            const total = file.lines.total.toString().padStart(5);
            const code = file.lines.code.toString().padStart(5);
            const componentType = file.componentType.padEnd(14);
            
            console.log(`${rank} | ${total} | ${code} | ${componentType} | ${file.name}`);
            console.log(`     |       |       |                | ${file.path}`);
            console.log('-'.repeat(120));
        });
    }
    
    if (backendFiles.length > 0) {
        console.log('\n' + '='.repeat(120));
        console.log('BACKEND COMPONENTS (300+ lines) - REFACTORING PRIORITY');
        console.log('='.repeat(120));
        console.log('RANK | TOTAL | CODE  | COMPONENT TYPE | FILE NAME & PATH');
        console.log('-'.repeat(120));
        
        backendFiles.forEach((file, index) => {
            const rank = (index + 1).toString().padStart(4);
            const total = file.lines.total.toString().padStart(5);
            const code = file.lines.code.toString().padStart(5);
            const componentType = file.componentType.padEnd(14);
            
            console.log(`${rank} | ${total} | ${code} | ${componentType} | ${file.name}`);
            console.log(`     |       |       |                | ${file.path}`);
            console.log('-'.repeat(120));
        });
    }
    
    // Summary by component type
    console.log('\n' + '='.repeat(120));
    console.log('SUMMARY BY COMPONENT TYPE');
    console.log('='.repeat(120));
    
    const typeStats = {};
    results.forEach(file => {
        const key = `${file.type}_${file.componentType}`;
        if (!typeStats[key]) {
            typeStats[key] = { count: 0, totalLines: 0, avgLines: 0 };
        }
        typeStats[key].count++;
        typeStats[key].totalLines += file.lines.total;
    });
    
    // Calculate averages
    Object.keys(typeStats).forEach(key => {
        typeStats[key].avgLines = Math.round(typeStats[key].totalLines / typeStats[key].count);
    });
    
    console.log('TYPE & COMPONENT     | COUNT | TOTAL LINES | AVG LINES');
    console.log('-'.repeat(60));
    
    Object.entries(typeStats)
        .sort((a, b) => b[1].totalLines - a[1].totalLines)
        .forEach(([key, stats]) => {
            const [type, componentType] = key.split('_');
            const label = `${type} ${componentType}`.padEnd(19);
            const count = stats.count.toString().padStart(5);
            const totalLines = stats.totalLines.toString().padStart(11);
            const avgLines = stats.avgLines.toString().padStart(9);
            
            console.log(`${label} | ${count} | ${totalLines} | ${avgLines}`);
        });
    
    // Refactoring recommendations
    console.log('\n' + '='.repeat(120));
    console.log('REFACTORING RECOMMENDATIONS');
    console.log('='.repeat(120));
    
    const criticalFiles = results.filter(f => f.lines.total > 800);
    const highPriorityFiles = results.filter(f => f.lines.total > 500 && f.lines.total <= 800);
    const mediumPriorityFiles = results.filter(f => f.lines.total >= 300 && f.lines.total <= 500);
    
    console.log(`CRITICAL (800+ lines): ${criticalFiles.length} files - Immediate refactoring required`);
    console.log(`HIGH PRIORITY (500-800 lines): ${highPriorityFiles.length} files - Should be refactored soon`);
    console.log(`MEDIUM PRIORITY (300-500 lines): ${mediumPriorityFiles.length} files - Consider refactoring`);
    
    if (criticalFiles.length > 0) {
        console.log('\nCRITICAL FILES (800+ lines):');
        criticalFiles.forEach(file => {
            console.log(`  • ${file.name} (${file.lines.total} lines) - ${file.componentType}`);
            console.log(`    Path: ${file.path}`);
        });
    }
    
    console.log(`\nTotal components analyzed: ${results.length}`);
    console.log(`Total lines in these components: ${results.reduce((sum, f) => sum + f.lines.total, 0).toLocaleString()}`);
    console.log(`Average lines per component: ${Math.round(results.reduce((sum, f) => sum + f.lines.total, 0) / results.length)}`);
}

// Main execution
console.log('Analyzing components with 300+ lines of code...');

const results = [];
const projectRoot = path.join(__dirname, '..');

// Analyze client directory
const clientPath = path.join(projectRoot, 'client');
if (fs.existsSync(clientPath)) {
    console.log('Scanning client directory...');
    analyzeDirectory(clientPath, results, 'client');
}

// Analyze server directory
const serverPath = path.join(projectRoot, 'server');
if (fs.existsSync(serverPath)) {
    console.log('Scanning server directory...');
    analyzeDirectory(serverPath, results, 'server');
}

// Analyze root level files
console.log('Scanning root directory...');
const rootItems = fs.readdirSync(projectRoot);
rootItems.forEach(item => {
    const fullPath = path.join(projectRoot, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isFile() && !shouldExcludeFile(item)) {
        const lines = countLines(fullPath);
        
        if (lines.total >= 300) {
            const fileType = getFileType(fullPath, '');
            const componentType = getComponentType(fullPath);
            
            results.push({
                name: item,
                path: item,
                type: fileType,
                componentType: componentType,
                lines: lines,
                size: stat.size
            });
        }
    }
});

generateReport(results);