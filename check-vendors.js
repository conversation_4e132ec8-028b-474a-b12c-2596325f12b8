const mongoose = require('mongoose');
const User = require('./server/src/models/User');

const checkVendors = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('Connected to MongoDB');
    
    const vendors = await User.find({userType: 'vendor'}).select('email firstName lastName userType');
    
    console.log('Available vendor users:');
    if (vendors.length === 0) {
      console.log('No vendor users found in database');
    } else {
      vendors.forEach(v => {
        console.log(`Email: ${v.email}, Name: ${v.firstName} ${v.lastName}, Type: ${v.userType}`);
      });
    }
    
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error:', error.message);
  }
};

checkVendors();
