/**
 * Detailed test for order creation to identify the exact error
 */

const axios = require('axios');
require('dotenv').config();

const API_BASE_URL = process.env.API_URL || 'http://localhost:5000/api';

// Test credentials
const TEST_CUSTOMER = {
  email: '<EMAIL>',
  password: 'Free@009'
};

let authToken = null;

// API call helper
const apiCall = async (method, endpoint, data = null) => {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...(authToken && { Authorization: `Bearer ${authToken}` })
      }
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`API Error (${method} ${endpoint}):`, {
      status: error.response?.status,
      message: error.response?.data?.message || error.message,
      details: error.response?.data
    });
    throw error;
  }
};

// Login
const login = async () => {
  console.log('🔐 Logging in...');
  const response = await apiCall('POST', '/auth/login', TEST_CUSTOMER);
  authToken = response.data?.token || response.token;
  console.log('✅ Login successful');
  return response;
};

// Test order creation with detailed error handling
const testOrderCreation = async () => {
  console.log('\n🛍️ TESTING ORDER CREATION WITH DETAILED ERROR HANDLING');
  console.log('='.repeat(60));
  
  try {
    // Get user profile
    const userResponse = await apiCall('GET', '/customer/profile');
    const user = userResponse.data;
    console.log('✅ User profile fetched');
    
    // Get cart
    const cartResponse = await apiCall('GET', '/customer/cart');
    const cart = cartResponse.data;
    console.log('✅ Cart fetched with', cart.items?.length || 0, 'items');
    
    if (!cart || !cart.items || cart.items.length === 0) {
      console.log('❌ Cart is empty');
      return false;
    }
    
    // Process order items
    const orderItems = cart.items.map((item, index) => {
      console.log(`Processing item ${index + 1}: ${item.product?.name}`);
      
      let vendorId = null;
      if (item.vendor && typeof item.vendor === 'object' && item.vendor._id) {
        vendorId = item.vendor._id.toString();
      } else if (typeof item.vendor === 'string' && item.vendor.length === 24) {
        vendorId = item.vendor;
      }
      
      if (!vendorId) {
        throw new Error(`Vendor information is missing for product: ${item.product?.name}`);
      }
      
      console.log(`✅ Vendor ID: ${vendorId}`);
      
      return {
        product: item.product._id,
        vendor: vendorId,
        name: item.product.name,
        sku: item.product.sku || `SKU${item.product._id}`,
        image: item.product.images?.[0]?.url || item.product.images?.[0] || '',
        quantity: item.quantity,
        unitPrice: item.priceAtAdd,
        totalPrice: item.priceAtAdd * item.quantity,
        variant: item.selectedVariant ? {
          name: item.selectedVariant.name,
          options: item.selectedVariant.options || []
        } : undefined
      };
    });
    
    console.log('\n✅ All items processed successfully!');
    
    // Prepare order data
    const subtotal = cart.totalAmount || 0;
    const protectPromiseFee = 28;
    const total = subtotal + protectPromiseFee;
    
    const orderData = {
      items: orderItems,
      billing: {
        firstName: user.firstName || 'Test',
        lastName: user.lastName || 'User',
        email: user.email || TEST_CUSTOMER.email,
        phone: user.phone || '1234567890',
        address: {
          street: user.address || 'Test Address',
          city: user.city || 'Test City',
          state: user.state || 'Test State',
          zipCode: user.zipCode || '12345',
          country: user.country || 'India'
        }
      },
      shipping: {
        firstName: user.firstName || 'Test',
        lastName: user.lastName || 'User',
        address: {
          street: user.address || 'Test Address',
          city: user.city || 'Test City',
          state: user.state || 'Test State',
          zipCode: user.zipCode || '12345',
          country: user.country || 'India'
        },
        method: 'standard',
        cost: 0
      },
      payment: {
        method: 'cod',
        status: 'pending'
      },
      pricing: {
        subtotal: subtotal,
        tax: 0,
        shipping: protectPromiseFee,
        discount: 0,
        total: total
      },
      customerNotes: 'Test order after vendor fix',
      estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
    };
    
    console.log('\n📦 Order data prepared:');
    console.log('Items:', orderData.items.length);
    console.log('Total:', orderData.pricing.total);
    console.log('Payment method:', orderData.payment.method);
    console.log('Billing address:', orderData.billing.address.city);
    console.log('Shipping address:', orderData.shipping.address.city);
    
    console.log('\n📤 Sending order creation request...');
    
    // Make the API call with detailed error logging
    try {
      const response = await axios({
        method: 'POST',
        url: `${API_BASE_URL}/customer/orders`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${authToken}`
        },
        data: orderData
      });
      
      if (response.data.success) {
        console.log('🎉 ORDER PLACED SUCCESSFULLY!');
        console.log('Order details:', {
          orderId: response.data.data.order.orderNumber,
          total: response.data.data.order.pricing.total,
          status: response.data.data.order.status,
          trackingCount: response.data.data.trackings?.length || 0
        });
        return true;
      } else {
        console.log('❌ Order placement failed:', response.data.message);
        return false;
      }
      
    } catch (apiError) {
      console.log('\n❌ DETAILED API ERROR:');
      console.log('Status:', apiError.response?.status);
      console.log('Status Text:', apiError.response?.statusText);
      console.log('Error Message:', apiError.response?.data?.message);
      console.log('Error Details:', apiError.response?.data?.error);
      console.log('Full Response Data:', JSON.stringify(apiError.response?.data, null, 2));
      
      if (apiError.response?.data?.errors) {
        console.log('\nValidation Errors:');
        apiError.response.data.errors.forEach((err, index) => {
          console.log(`  ${index + 1}. ${err.path || err.param}: ${err.msg}`);
        });
      }
      
      return false;
    }
    
  } catch (error) {
    console.log('❌ Test error:', error.message);
    return false;
  }
};

// Main function
const main = async () => {
  console.log('🚀 DETAILED ORDER CREATION TEST');
  console.log('='.repeat(60));
  
  try {
    await login();
    const success = await testOrderCreation();
    
    if (success) {
      console.log('\n🎉 SUCCESS! Order creation is working!');
    } else {
      console.log('\n❌ Order creation failed - check the detailed error above');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
  
  console.log('\n🏁 Test complete');
};

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testOrderCreation };