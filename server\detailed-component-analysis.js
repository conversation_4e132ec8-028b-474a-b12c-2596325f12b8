#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// File extensions to analyze
const FRONTEND_EXTENSIONS = ['.js', '.jsx', '.ts', '.tsx', '.vue', '.css', '.scss', '.sass'];
const BACKEND_EXTENSIONS = ['.js', '.ts', '.py', '.java', '.php', '.rb', '.go', '.cs'];
const CONFIG_EXTENSIONS = ['.json', '.yaml', '.yml', '.xml', '.toml'];

// Directories to exclude
const EXCLUDE_DIRS = [
    'node_modules', 
    '.git', 
    'dist', 
    'build', 
    'coverage', 
    '.next', 
    '.nuxt',
    '__pycache__',
    'vendor',
    'uploads',
    '.vscode',
    '.augment',
    '.qodo'
];

// Files to exclude
const EXCLUDE_FILES = [
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',
    '.DS_Store',
    'Thumbs.db'
];

function countLines(filePath) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');
        
        let codeLines = 0;
        let totalLines = lines.length;
        let commentLines = 0;
        let emptyLines = 0;
        
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed === '') {
                emptyLines++;
            } else if (trimmed.startsWith('//') || trimmed.startsWith('/*') || trimmed.startsWith('*') || trimmed.startsWith('#')) {
                commentLines++;
            } else {
                codeLines++;
            }
        }
        
        return {
            total: totalLines,
            code: codeLines,
            comments: commentLines,
            empty: emptyLines
        };
    } catch (error) {
        console.error(`Error reading file ${filePath}:`, error.message);
        return { total: 0, code: 0, comments: 0, empty: 0 };
    }
}

function shouldExcludeDir(dirName) {
    return EXCLUDE_DIRS.some(exclude => dirName.includes(exclude));
}

function shouldExcludeFile(fileName) {
    return EXCLUDE_FILES.includes(fileName);
}

function getFileType(filePath, baseDir) {
    const ext = path.extname(filePath).toLowerCase();
    
    if (baseDir.includes('client') || baseDir.includes('frontend')) {
        if (FRONTEND_EXTENSIONS.includes(ext)) return 'Frontend';
    }
    
    if (baseDir.includes('server') || baseDir.includes('backend') || baseDir.includes('api')) {
        if (BACKEND_EXTENSIONS.includes(ext)) return 'Backend';
    }
    
    if (CONFIG_EXTENSIONS.includes(ext)) return 'Config';
    
    // Fallback based on extension
    if (FRONTEND_EXTENSIONS.includes(ext)) return 'Frontend';
    if (BACKEND_EXTENSIONS.includes(ext)) return 'Backend';
    
    return 'Other';
}

function getComponentCategory(filePath) {
    const pathLower = filePath.toLowerCase();
    
    // Backend categories
    if (pathLower.includes('controller')) return 'Controller';
    if (pathLower.includes('model')) return 'Model';
    if (pathLower.includes('service')) return 'Service';
    if (pathLower.includes('middleware')) return 'Middleware';
    if (pathLower.includes('route')) return 'Route';
    if (pathLower.includes('validator')) return 'Validator';
    if (pathLower.includes('util')) return 'Utility';
    if (pathLower.includes('config')) return 'Config';
    if (pathLower.includes('test')) return 'Test';
    if (pathLower.includes('debug')) return 'Debug';
    if (pathLower.includes('migration')) return 'Migration';
    if (pathLower.includes('seed')) return 'Seed';
    if (pathLower.includes('schema')) return 'Schema';
    
    // Frontend categories
    if (pathLower.includes('component')) return 'Component';
    if (pathLower.includes('page')) return 'Page';
    if (pathLower.includes('context')) return 'Context';
    if (pathLower.includes('hook')) return 'Hook';
    if (pathLower.includes('service')) return 'Service';
    if (pathLower.includes('util')) return 'Utility';
    if (pathLower.includes('style')) return 'Style';
    if (pathLower.includes('asset')) return 'Asset';
    
    // Check by file extension and path structure
    const ext = path.extname(filePath).toLowerCase();
    if (['.jsx', '.tsx'].includes(ext)) {
        if (pathLower.includes('pages')) return 'Page';
        if (pathLower.includes('components')) return 'Component';
        if (pathLower.includes('contexts')) return 'Context';
        if (pathLower.includes('hooks')) return 'Hook';
        return 'Component';
    }
    
    if (['.css', '.scss', '.sass'].includes(ext)) return 'Style';
    if (['.js', '.ts'].includes(ext)) {
        if (pathLower.includes('pages')) return 'Page';
        return 'Script';
    }
    
    return 'Other';
}

function analyzeDirectory(dirPath, results = [], baseDir = '') {
    try {
        const items = fs.readdirSync(dirPath);
        
        for (const item of items) {
            const fullPath = path.join(dirPath, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                if (!shouldExcludeDir(item)) {
                    analyzeDirectory(fullPath, results, baseDir || item);
                }
            } else if (stat.isFile()) {
                if (!shouldExcludeFile(item)) {
                    const lines = countLines(fullPath);
                    const fileType = getFileType(fullPath, baseDir);
                    const category = getComponentCategory(fullPath);
                    const relativePath = path.relative(path.join(__dirname, '..'), fullPath);
                    
                    results.push({
                        name: item,
                        path: relativePath,
                        type: fileType,
                        category: category,
                        lines: lines,
                        size: stat.size
                    });
                }
            }
        }
    } catch (error) {
        console.error(`Error analyzing directory ${dirPath}:`, error.message);
    }
    
    return results;
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function generateDetailedReport(results) {
    // Filter for significant files (more than 5 lines)
    const significantFiles = results.filter(file => file.lines.total > 5);
    
    console.log('\n' + '='.repeat(100));
    console.log('DETAILED COMPONENT ANALYSIS FOR REFACTORING - MULTI-VENDOR ECOMMERCE');
    console.log('='.repeat(100));
    
    // Group by type and category
    const groupedResults = {};
    significantFiles.forEach(file => {
        const key = `${file.type}_${file.category}`;
        if (!groupedResults[key]) {
            groupedResults[key] = [];
        }
        groupedResults[key].push(file);
    });
    
    // Sort each group by line count
    Object.keys(groupedResults).forEach(key => {
        groupedResults[key].sort((a, b) => b.lines.total - a.lines.total);
    });
    
    // Backend Components Analysis
    console.log('\n' + '='.repeat(100));
    console.log('BACKEND COMPONENTS DETAILED ANALYSIS');
    console.log('='.repeat(100));
    
    const backendCategories = ['Controller', 'Model', 'Service', 'Middleware', 'Route', 'Validator', 'Utility', 'Schema', 'Test', 'Debug', 'Other'];
    
    backendCategories.forEach(category => {
        const key = `Backend_${category}`;
        if (groupedResults[key] && groupedResults[key].length > 0) {
            console.log(`\n${category.toUpperCase()} FILES:`);
            console.log('-'.repeat(100));
            console.log('Total | Code  | Comments | Empty | Size     | File Name');
            console.log('-'.repeat(100));
            
            groupedResults[key].forEach(file => {
                const total = file.lines.total.toString().padStart(5);
                const code = file.lines.code.toString().padStart(4);
                const comments = file.lines.comments.toString().padStart(8);
                const empty = file.lines.empty.toString().padStart(5);
                const size = formatBytes(file.size).padStart(8);
                
                console.log(`${total} | ${code} | ${comments} | ${empty} | ${size} | ${file.name}`);
                console.log(`      |      |          |       |          | ${file.path}`);
                console.log('-'.repeat(100));
            });
        }
    });
    
    // Frontend Components Analysis
    console.log('\n' + '='.repeat(100));
    console.log('FRONTEND COMPONENTS DETAILED ANALYSIS');
    console.log('='.repeat(100));
    
    const frontendCategories = ['Component', 'Page', 'Context', 'Hook', 'Service', 'Utility', 'Style', 'Script', 'Other'];
    
    frontendCategories.forEach(category => {
        const key = `Frontend_${category}`;
        if (groupedResults[key] && groupedResults[key].length > 0) {
            console.log(`\n${category.toUpperCase()} FILES:`);
            console.log('-'.repeat(100));
            console.log('Total | Code  | Comments | Empty | Size     | File Name');
            console.log('-'.repeat(100));
            
            groupedResults[key].forEach(file => {
                const total = file.lines.total.toString().padStart(5);
                const code = file.lines.code.toString().padStart(4);
                const comments = file.lines.comments.toString().padStart(8);
                const empty = file.lines.empty.toString().padStart(5);
                const size = formatBytes(file.size).padStart(8);
                
                console.log(`${total} | ${code} | ${comments} | ${empty} | ${size} | ${file.name}`);
                console.log(`      |      |          |       |          | ${file.path}`);
                console.log('-'.repeat(100));
            });
        }
    });
    
    // Refactoring Priority List
    console.log('\n' + '='.repeat(100));
    console.log('REFACTORING PRIORITY LIST (Files > 200 lines)');
    console.log('='.repeat(100));
    
    const highPriorityFiles = significantFiles
        .filter(file => file.lines.total > 200)
        .sort((a, b) => b.lines.total - a.lines.total);
    
    console.log('Priority | Total | Code  | Type      | Category   | File Name');
    console.log('-'.repeat(100));
    
    highPriorityFiles.forEach((file, index) => {
        const priority = (index + 1).toString().padStart(8);
        const total = file.lines.total.toString().padStart(5);
        const code = file.lines.code.toString().padStart(4);
        const type = file.type.padEnd(9);
        const category = file.category.padEnd(10);
        
        console.log(`${priority} | ${total} | ${code} | ${type} | ${category} | ${file.name}`);
        console.log(`         |       |      |           |            | ${file.path}`);
        console.log('-'.repeat(100));
    });
    
    // Summary Statistics
    console.log('\n' + '='.repeat(100));
    console.log('SUMMARY STATISTICS');
    console.log('='.repeat(100));
    
    const stats = {
        totalFiles: significantFiles.length,
        highComplexity: highPriorityFiles.length,
        backend: significantFiles.filter(f => f.type === 'Backend').length,
        frontend: significantFiles.filter(f => f.type === 'Frontend').length,
        totalLines: significantFiles.reduce((sum, f) => sum + f.lines.total, 0),
        totalCodeLines: significantFiles.reduce((sum, f) => sum + f.lines.code, 0),
        avgLinesPerFile: Math.round(significantFiles.reduce((sum, f) => sum + f.lines.total, 0) / significantFiles.length)
    };
    
    console.log(`Total Files Analyzed: ${stats.totalFiles}`);
    console.log(`High Complexity Files (>200 lines): ${stats.highComplexity}`);
    console.log(`Backend Components: ${stats.backend}`);
    console.log(`Frontend Components: ${stats.frontend}`);
    console.log(`Total Lines of Code: ${stats.totalLines.toLocaleString()}`);
    console.log(`Total Code Lines (excluding comments/empty): ${stats.totalCodeLines.toLocaleString()}`);
    console.log(`Average Lines per File: ${stats.avgLinesPerFile}`);
    
    // Category breakdown
    console.log('\nBreakdown by Category:');
    console.log('-'.repeat(50));
    
    const categoryStats = {};
    significantFiles.forEach(file => {
        const key = `${file.type}_${file.category}`;
        if (!categoryStats[key]) {
            categoryStats[key] = { count: 0, totalLines: 0 };
        }
        categoryStats[key].count++;
        categoryStats[key].totalLines += file.lines.total;
    });
    
    Object.entries(categoryStats)
        .sort((a, b) => b[1].totalLines - a[1].totalLines)
        .forEach(([key, stats]) => {
            const [type, category] = key.split('_');
            console.log(`${type} ${category}: ${stats.count} files, ${stats.totalLines.toLocaleString()} lines`);
        });
}

// Main execution
console.log('Starting detailed component analysis for refactoring...');

const results = [];
const projectRoot = path.join(__dirname, '..');

// Analyze client directory
const clientPath = path.join(projectRoot, 'client');
if (fs.existsSync(clientPath)) {
    console.log('Analyzing client directory...');
    analyzeDirectory(clientPath, results, 'client');
}

// Analyze server directory
const serverPath = path.join(projectRoot, 'server');
if (fs.existsSync(serverPath)) {
    console.log('Analyzing server directory...');
    analyzeDirectory(serverPath, results, 'server');
}

// Analyze root level files
console.log('Analyzing root directory files...');
const rootItems = fs.readdirSync(projectRoot);
rootItems.forEach(item => {
    const fullPath = path.join(projectRoot, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isFile() && !shouldExcludeFile(item)) {
        const lines = countLines(fullPath);
        const fileType = getFileType(fullPath, '');
        const category = getComponentCategory(fullPath);
        
        results.push({
            name: item,
            path: item,
            type: fileType,
            category: category,
            lines: lines,
            size: stat.size
        });
    }
});

generateDetailedReport(results);