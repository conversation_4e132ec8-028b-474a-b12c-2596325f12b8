const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

async function testUltraPermissive() {
  console.log('🚀 Testing Ultra-Permissive Carousel Upload Configuration...\n');
  
  const BASE_URL = 'http://localhost:5000';
  const ADMIN_CREDENTIALS = {
    email: '<EMAIL>',
    password: 'password@admin123'
  };
  
  try {
    // Step 1: Login as admin
    console.log('1. Logging in as admin...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, ADMIN_CREDENTIALS);
    
    let adminToken = null;
    if (loginResponse.data.success) {
      adminToken = loginResponse.data.data?.token || loginResponse.data.token;
    }
    
    if (!adminToken) {
      console.log('❌ Could not extract token from login response');
      return;
    }
    
    console.log('✅ Successfully logged in as admin');
    
    // Step 2: Test with valid JPEG
    console.log('\n2. Testing with properly formatted JPEG...');
    
    const jpegPath = path.join(__dirname, 'valid-test.jpg');
    if (!fs.existsSync(jpegPath)) {
      console.log('❌ Valid JPEG test file not found. Run create-valid-test-images.js first');
      return;
    }
    
    const jpegForm = new FormData();
    jpegForm.append('image', fs.createReadStream(jpegPath), {
      filename: 'ultra-permissive-test.jpg',
      contentType: 'image/jpeg'
    });
    jpegForm.append('title', 'Ultra Permissive JPEG Test');
    jpegForm.append('description', 'Testing with properly formatted JPEG file');
    jpegForm.append('linkUrl', 'https://example.com/ultra-test');
    
    try {
      console.log('📤 Uploading valid JPEG...');
      const jpegResponse = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, jpegForm, {
        headers: {
          ...jpegForm.getHeaders(),
          'Authorization': `Bearer ${adminToken}`
        },
        timeout: 60000
      });
      
      console.log('🎉 JPEG UPLOAD SUCCESSFUL!');
      console.log(`📥 Status: ${jpegResponse.status}`);
      console.log('📄 Response summary:');
      console.log(`   - Success: ${jpegResponse.data.success}`);
      console.log(`   - Message: ${jpegResponse.data.message}`);
      console.log(`   - Total carousel images: ${jpegResponse.data.data.carouselImages?.length || 0}`);
      
    } catch (jpegError) {
      console.log('❌ JPEG upload failed:');
      console.log(`📥 Status: ${jpegError.response?.status}`);
      console.log('📄 Error:');
      console.log(JSON.stringify(jpegError.response?.data, null, 2));
    }
    
    // Step 3: Test with valid PNG
    console.log('\n3. Testing with properly formatted PNG...');
    
    const pngPath = path.join(__dirname, 'valid-test.png');
    const pngForm = new FormData();
    pngForm.append('image', fs.createReadStream(pngPath), {
      filename: 'ultra-permissive-test.png',
      contentType: 'image/png'
    });
    pngForm.append('title', 'Ultra Permissive PNG Test');
    pngForm.append('description', 'Testing with properly formatted PNG file');
    pngForm.append('linkUrl', 'https://example.com/png-ultra-test');
    
    try {
      console.log('📤 Uploading valid PNG...');
      const pngResponse = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, pngForm, {
        headers: {
          ...pngForm.getHeaders(),
          'Authorization': `Bearer ${adminToken}`
        },
        timeout: 60000
      });
      
      console.log('✅ PNG upload successful!');
      console.log(`📥 Status: ${pngResponse.status}`);
      
    } catch (pngError) {
      console.log('❌ PNG upload failed:');
      console.log(`📥 Status: ${pngError.response?.status}`);
      console.log('📄 Error:', pngError.response?.data);
    }
    
    // Step 4: Test with valid GIF
    console.log('\n4. Testing with properly formatted GIF...');
    
    const gifPath = path.join(__dirname, 'valid-test.gif');
    const gifForm = new FormData();
    gifForm.append('image', fs.createReadStream(gifPath), {
      filename: 'ultra-permissive-test.gif',
      contentType: 'image/gif'
    });
    gifForm.append('title', 'Ultra Permissive GIF Test');
    gifForm.append('description', 'Testing with properly formatted GIF file');
    gifForm.append('linkUrl', 'https://example.com/gif-ultra-test');
    
    try {
      console.log('📤 Uploading valid GIF...');
      const gifResponse = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, gifForm, {
        headers: {
          ...gifForm.getHeaders(),
          'Authorization': `Bearer ${adminToken}`
        },
        timeout: 60000
      });
      
      console.log('✅ GIF upload successful!');
      console.log(`📥 Status: ${gifResponse.status}`);
      
    } catch (gifError) {
      console.log('❌ GIF upload failed:');
      console.log(`📥 Status: ${gifError.response?.status}`);
      console.log('📄 Error:', gifError.response?.data);
    }
    
    // Step 5: Test with file that has no extension
    console.log('\n5. Testing with file that has no extension...');
    
    // Copy the valid JPEG but remove extension
    const noExtPath = path.join(__dirname, 'test-no-extension');
    fs.copyFileSync(jpegPath, noExtPath);
    
    const noExtForm = new FormData();
    noExtForm.append('image', fs.createReadStream(noExtPath), {
      filename: 'test-no-extension',
      contentType: 'image/jpeg'
    });
    noExtForm.append('title', 'No Extension Test');
    noExtForm.append('description', 'Testing file with no extension');
    noExtForm.append('linkUrl', '');
    
    try {
      console.log('📤 Uploading file with no extension...');
      const noExtResponse = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, noExtForm, {
        headers: {
          ...noExtForm.getHeaders(),
          'Authorization': `Bearer ${adminToken}`
        },
        timeout: 60000
      });
      
      console.log('✅ No extension file upload successful!');
      console.log(`📥 Status: ${noExtResponse.status}`);
      
    } catch (noExtError) {
      console.log('❌ No extension file upload failed:');
      console.log(`📥 Status: ${noExtError.response?.status}`);
      console.log('📄 Error:', noExtError.response?.data);
    }
    
    // Cleanup
    if (fs.existsSync(noExtPath)) {
      fs.unlinkSync(noExtPath);
    }
    
    // Step 6: Get final homepage settings
    console.log('\n6. Checking final homepage settings...');
    
    try {
      const settingsResponse = await axios.get(`${BASE_URL}/api/admin/homepage-settings`, {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });
      
      console.log('✅ Retrieved homepage settings');
      const carouselImages = settingsResponse.data.data.carouselImages || [];
      console.log(`📊 Total carousel images: ${carouselImages.length}`);
      
      if (carouselImages.length > 0) {
        console.log('📋 Recent carousel images:');
        carouselImages.slice(-5).forEach((img, index) => {
          console.log(`  ${carouselImages.length - 4 + index}. "${img.title}" - ${img.imageUrl}`);
        });
      }
      
    } catch (settingsError) {
      console.log('❌ Failed to get homepage settings:', settingsError.response?.data);
    }
    
    console.log('\n🎯 ULTRA-PERMISSIVE TEST RESULTS:');
    console.log('');
    console.log('Configuration changes applied:');
    console.log('✅ Removed ALL file format restrictions');
    console.log('✅ Set resource_type to "auto" for Cloudinary auto-detection');
    console.log('✅ Created special carousel file filter with NO restrictions');
    console.log('✅ Increased file size limits to 50MB');
    console.log('✅ Accept files with or without extensions');
    console.log('✅ Let Cloudinary handle all validation');
    console.log('');
    console.log('If JPEG uploads are still failing, the issue is likely:');
    console.log('1. Cloudinary account limitations or settings');
    console.log('2. Network/connectivity issues');
    console.log('3. Server restart needed to apply configuration changes');
    console.log('4. Environment variables not properly set');
    console.log('');
    console.log('💡 Recommendation: Restart the server to ensure new configuration is loaded.');
    
  } catch (error) {
    console.error('❌ Test script failed:', error.message);
  }
}

// Run the test
testUltraPermissive().catch(console.error);