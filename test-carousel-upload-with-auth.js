const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

// Create test admin credentials
const adminCredentials = {
  email: '<EMAIL>',
  password: 'admin123'
};

async function createTestAdmin() {
  try {
    // Try registering an admin user
    const registerResponse = await axios.post(`${BASE_URL}/api/auth/register`, {
      firstName: 'Test',
      lastName: 'Admin',
      email: adminCredentials.email,
      password: adminCredentials.password,
      userType: 'admin'
    });
    console.log('Admin created:', registerResponse.data);
    return true;
  } catch (error) {
    if (error.response?.status === 400 && error.response?.data?.message?.includes('already exists')) {
      console.log('Admin already exists, proceeding...');
      return true;
    }
    console.log('Error creating admin:', error.response?.data || error.message);
    return false;
  }
}

async function loginAdmin() {
  try {
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, adminCredentials);
    console.log('Login successful');
    return loginResponse.data.data.token;
  } catch (error) {
    console.log('Login error:', error.response?.data || error.message);
    return null;
  }
}

async function testCarouselUploadWithAuth() {
  try {
    console.log('Testing carousel image upload with authentication...');
    
    // Step 1: Create admin user
    console.log('\n1. Creating test admin user...');
    await createTestAdmin();
    
    // Step 2: Login to get token
    console.log('\n2. Logging in to get auth token...');
    const token = await loginAdmin();
    if (!token) {
      console.log('Failed to get auth token, exiting...');
      return;
    }
    
    // Step 3: Test GET endpoint with auth
    console.log('\n3. Testing GET /api/admin/homepage-settings with auth...');
    try {
      const getResponse = await axios.get(`${BASE_URL}/api/admin/homepage-settings`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('GET Response:', getResponse.data);
    } catch (error) {
      console.log('GET Error:', error.response?.status, error.response?.data);
    }
    
    // Step 4: Create test image
    const testImagePath = path.join(__dirname, 'test-carousel-image.jpg');
    if (!fs.existsSync(testImagePath)) {
      // Create a minimal valid JPEG file for testing
      const jpeg_header = Buffer.from([0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46]);
      const jpeg_footer = Buffer.from([0xFF, 0xD9]);
      const testImageData = Buffer.concat([jpeg_header, Buffer.alloc(1000, 0x00), jpeg_footer]);
      fs.writeFileSync(testImagePath, testImageData);
      console.log('Created test image file:', testImagePath);
    }
    
    // Step 5: Test carousel upload with auth
    console.log('\n4. Testing POST /api/admin/homepage-settings/carousel with auth...');
    const form = new FormData();
    form.append('image', fs.createReadStream(testImagePath));
    form.append('title', 'Test Carousel Image');
    form.append('description', 'This is a test carousel image for debugging');
    form.append('linkUrl', 'https://example.com');
    
    try {
      const response = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, form, {
        headers: {
          ...form.getHeaders(),
          'Authorization': `Bearer ${token}`
        },
      });
      console.log('Upload Response:', response.data);
    } catch (error) {
      console.log('Upload Error:', error.response?.status, error.response?.statusText);
      console.log('Error Data:', error.response?.data);
      console.log('Error Details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        headers: error.response?.headers,
        data: error.response?.data
      });
    }
    
    // Step 6: Test without image (should fail)
    console.log('\n5. Testing upload without image file (should fail)...');
    const formWithoutImage = new FormData();
    formWithoutImage.append('title', 'Test Without Image');
    formWithoutImage.append('description', 'This should fail');
    
    try {
      const responseNoImage = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, formWithoutImage, {
        headers: {
          ...formWithoutImage.getHeaders(),
          'Authorization': `Bearer ${token}`
        },
      });
      console.log('No Image Response:', responseNoImage.data);
    } catch (error) {
      console.log('No Image Error (expected):', error.response?.status, error.response?.data);
    }
    
    // Cleanup
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
      console.log('\nCleaned up test image file');
    }
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

// Check if axios and form-data are available
try {
  require('axios');
  require('form-data');
  testCarouselUploadWithAuth();
} catch (error) {
  console.log('Missing dependencies. Please run: npm install axios form-data');
  console.log('Error:', error.message);
}
