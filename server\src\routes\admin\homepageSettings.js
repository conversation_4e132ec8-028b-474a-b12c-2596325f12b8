const express = require('express');
const router = express.Router();
const { verifyToken, requireUserType } = require('../../middleware/auth/authMiddleware');
const imageUpload = require('../../middleware/upload/imageUpload');
const {
  getHomepageSettings,
  updateGeneralSettings,
  addCarouselImage,
  updateCarouselImage,
  deleteCarouselImage,
  addPromotionImage,
  updatePromotionImage,
  deletePromotionImage,
  addFeaturedCategory,
  updateFeaturedCategory,
  deleteFeaturedCategory
} = require('../../controllers/admin/homepageSettingsController');

// Apply authentication middleware to all routes
router.use(verifyToken);
router.use(requireUserType(['admin']));

// General settings routes
router.get('/', getHomepageSettings);
router.put('/settings', updateGeneralSettings);

// Carousel image routes
router.post('/carousel', imageUpload.carouselImage(), addCarouselImage);
router.put('/carousel/:imageId', imageUpload.carouselImage(), updateCarouselImage);
router.delete('/carousel/:imageId', deleteCarouselImage);

// Promotion image routes
router.post('/promotions', imageUpload.promotionImage(), addPromotionImage);
router.put('/promotions/:imageId', imageUpload.promotionImage(), updatePromotionImage);
router.delete('/promotions/:imageId', deletePromotionImage);

// Featured category routes
router.post('/featured-categories', imageUpload.categoryImage(), addFeaturedCategory);
router.put('/featured-categories/:categoryId', imageUpload.categoryImage(), updateFeaturedCategory);
router.delete('/featured-categories/:categoryId', deleteFeaturedCategory);

module.exports = router;
