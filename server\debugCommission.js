const mongoose = require('mongoose');
require('dotenv').config();
const { Vendor } = require('./src/models');

async function debugCommission() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('🔍 Debugging commission data...');
    
    // Get a sample vendor
    const vendor = await Vendor.findOne({});
    console.log('📋 Sample vendor commission before:', JSON.stringify(vendor.commission, null, 2));
    
    // Update commission data
    await Vendor.findByIdAndUpdate(vendor._id, {
      'commission.pendingAmount': 25000,
      'commission.totalEarned': 50000,
      'commission.totalPaid': 25000
    });
    
    // Check the updated vendor
    const updatedVendor = await Vendor.findById(vendor._id);
    console.log('📋 Sample vendor commission after:', JSON.stringify(updatedVendor.commission, null, 2));
    
    // Update all vendors with commission data
    const updateResult = await Vendor.updateMany({}, {
      $set: {
        'commission.pendingAmount': 15000,
        'commission.totalEarned': 30000,
        'commission.totalPaid': 15000
      }
    });
    
    console.log('✅ Updated', updateResult.modifiedCount, 'vendors');
    
    // Test the statistics function
    const stats = await Vendor.getStatistics();
    console.log('📊 Vendor Statistics:', JSON.stringify(stats, null, 2));
    
    // Also check the total manually
    const vendors = await Vendor.find({}, 'commission');
    const totalPending = vendors.reduce((sum, v) => sum + (v.commission.pendingAmount || 0), 0);
    console.log('💰 Manual calculation - Total Pending Commission:', totalPending);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await mongoose.disconnect();
  }
}

debugCommission();
