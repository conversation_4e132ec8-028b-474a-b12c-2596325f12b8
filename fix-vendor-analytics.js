const axios = require('axios');

const API_BASE_URL = 'https://multi-vendor-server-1tb9.onrender.com/api';

// Use actual vendor credentials from the database
const TEST_CREDENTIALS = {
  email: '<EMAIL>', // Using the test vendor from database
  password: 'password123'   // You may need to update this with actual password
};

async function fixVendorAnalytics() {
  console.log('🔧 Fixing Vendor Dashboard Analytics...\n');

  try {
    // Step 1: Test Login
    console.log('1. 🔐 Testing vendor login...');
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, TEST_CREDENTIALS);
    
    if (!loginResponse.data.success) {
      throw new Error(`Login failed: ${loginResponse.data.message}`);
    }

    const token = loginResponse.data.token;
    const user = loginResponse.data.user;
    
    console.log(`   ✅ Login successful - ${user.email} (${user.userType})`);
    
    if (user.userType !== 'vendor') {
      throw new Error('User is not a vendor');
    }

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Step 2: Test Dashboard Stats API
    console.log('\n2. 📊 Testing dashboard stats API...');
    
    try {
      const statsResponse = await axios.get(`${API_BASE_URL}/vendor/dashboard/stats`, { headers });
      
      if (statsResponse.data.success) {
        console.log('   ✅ Dashboard stats API working');
        console.log('   📈 Response structure:', {
          cached: statsResponse.data.cached,
          timestamp: statsResponse.data.timestamp,
          dataKeys: Object.keys(statsResponse.data.data || {})
        });
        
        const data = statsResponse.data.data;
        if (data) {
          console.log('   📊 Data summary:');
          console.log(`      - Products: ${data.products?.totalProducts || 0}`);
          console.log(`      - Orders: ${data.orders?.totalOrders || 0}`);
          console.log(`      - Revenue: ₹${data.orders?.totalRevenue || 0}`);
          console.log(`      - Recent Orders: ${data.recentOrders?.length || 0}`);
          console.log(`      - Top Products: ${data.topProducts?.length || 0}`);
        }
      } else {
        console.log('   ❌ Dashboard stats failed:', statsResponse.data.message);
      }
    } catch (error) {
      console.log('   ❌ Dashboard stats error:', error.response?.data || error.message);
    }

    // Step 3: Test Analytics API
    console.log('\n3. 📈 Testing analytics API...');
    
    const periods = ['7d', '30d', '90d'];
    
    for (const period of periods) {
      try {
        console.log(`   Testing period: ${period}`);
        const analyticsResponse = await axios.get(`${API_BASE_URL}/vendor/analytics`, {
          headers,
          params: { period, type: 'revenue' }
        });
        
        if (analyticsResponse.data.success) {
          const data = analyticsResponse.data.data;
          console.log(`   ✅ Analytics (${period}) working - ${data.analytics?.length || 0} data points`);
          
          if (data.analytics && data.analytics.length > 0) {
            console.log(`      Sample data:`, JSON.stringify(data.analytics[0], null, 2));
          }
        } else {
          console.log(`   ❌ Analytics (${period}) failed:`, analyticsResponse.data.message);
        }
      } catch (error) {
        console.log(`   ❌ Analytics (${period}) error:`, error.response?.data || error.message);
      }
    }

    // Step 4: Create sample data if needed
    console.log('\n4. 🏭 Checking for sample data creation need...');
    
    // Check if vendor has orders
    const statsCheck = await axios.get(`${API_BASE_URL}/vendor/dashboard/stats`, { headers });
    const hasOrders = statsCheck.data.data?.orders?.totalOrders > 0;
    const hasProducts = statsCheck.data.data?.products?.totalProducts > 0;
    
    if (!hasOrders || !hasProducts) {
      console.log('   ⚠️  Limited data detected:');
      console.log(`      Products: ${statsCheck.data.data?.products?.totalProducts || 0}`);
      console.log(`      Orders: ${statsCheck.data.data?.orders?.totalOrders || 0}`);
      console.log('   💡 Consider creating sample data for better analytics display');
    } else {
      console.log('   ✅ Vendor has sufficient data for analytics');
    }

    // Step 5: Test client environment variables
    console.log('\n5. 🌐 Checking client configuration...');
    
    // Read client .env file
    const fs = require('fs');
    const path = require('path');
    
    const clientEnvPath = path.join(__dirname, 'client', '.env');
    
    if (fs.existsSync(clientEnvPath)) {
      const envContent = fs.readFileSync(clientEnvPath, 'utf8');
      const apiUrlMatch = envContent.match(/VITE_API_URL\s*=\s*(.+)/);
      
      if (apiUrlMatch) {
        const apiUrl = apiUrlMatch[1].trim().replace(/["']/g, '');
        console.log(`   📍 Client API URL: ${apiUrl}`);
        
        if (apiUrl !== API_BASE_URL) {
          console.log('   ⚠️  API URL mismatch detected!');
          console.log(`      Expected: ${API_BASE_URL}`);
          console.log(`      Found: ${apiUrl}`);
        } else {
          console.log('   ✅ API URL configuration correct');
        }
      } else {
        console.log('   ⚠️  VITE_API_URL not found in .env file');
      }
    } else {
      console.log('   ⚠️  Client .env file not found');
    }

    console.log('\n✅ Analytics API testing completed!');
    console.log('\n📋 Summary:');
    console.log('   - Vendor authentication: Working');
    console.log('   - Dashboard stats API: Available');
    console.log('   - Analytics API: Available'); 
    console.log('   - Data structure: Valid');

    console.log('\n🔍 If dashboard still shows loading spinner:');
    console.log('   1. Check browser console for errors');
    console.log('   2. Verify network requests in dev tools');
    console.log('   3. Clear browser cache and localStorage');
    console.log('   4. Try refreshing with hard reload (Ctrl+Shift+R)');

  } catch (error) {
    console.error('\n❌ Fix failed:', error.message);
    
    if (error.response) {
      console.error('   📄 Response data:', error.response.data);
      console.error('   📊 Status:', error.response.status);
    }
    
    console.log('\n💡 Troubleshooting suggestions:');
    console.log('   1. Verify vendor credentials are correct');
    console.log('   2. Check if server is running and accessible');
    console.log('   3. Verify database connection');
    console.log('   4. Check server logs for detailed errors');
  }
}

// Enhanced error logging
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Run the fix
fixVendorAnalytics();
