const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');

async function testCarouselUploadFinal() {
  console.log('🔍 Final Carousel Upload Test with Valid Images...\n');
  
  const BASE_URL = 'http://localhost:5000';
  const ADMIN_CREDENTIALS = {
    email: '<EMAIL>',
    password: 'password@admin123'
  };
  
  try {
    // Step 1: Login as admin
    console.log('1. Logging in as admin...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, ADMIN_CREDENTIALS);
    
    let adminToken = null;
    if (loginResponse.data.success) {
      adminToken = loginResponse.data.data?.token || loginResponse.data.token;
    }
    
    if (!adminToken) {
      console.log('❌ Could not extract token from login response');
      return;
    }
    
    console.log('✅ Successfully logged in as admin');
    
    // Step 2: Test with PNG file
    console.log('\n2. Testing carousel upload with PNG file...');
    
    const pngPath = path.join(__dirname, 'test-image.png');
    if (!fs.existsSync(pngPath)) {
      console.log('❌ PNG test file not found. Run create-real-test-image.js first');
      return;
    }
    
    const pngForm = new FormData();
    pngForm.append('image', fs.createReadStream(pngPath), {
      filename: 'test-carousel.png',
      contentType: 'image/png'
    });
    pngForm.append('title', 'PNG Test Carousel');
    pngForm.append('description', 'Testing with valid PNG file');
    pngForm.append('linkUrl', 'https://example.com/png-test');
    
    try {
      console.log('📤 Uploading PNG...');
      const pngResponse = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, pngForm, {
        headers: {
          ...pngForm.getHeaders(),
          'Authorization': `Bearer ${adminToken}`
        },
        timeout: 60000
      });
      
      console.log('✅ PNG UPLOAD SUCCESSFUL!');
      console.log(`📥 Status: ${pngResponse.status}`);
      console.log('📄 Response:');
      console.log(JSON.stringify(pngResponse.data, null, 2));
      
    } catch (pngError) {
      console.log('❌ PNG upload failed:');
      console.log(`��� Status: ${pngError.response?.status}`);
      console.log('📄 Error:');
      console.log(JSON.stringify(pngError.response?.data, null, 2));
    }
    
    // Step 3: Test with JPEG file
    console.log('\n3. Testing carousel upload with JPEG file...');
    
    const jpegPath = path.join(__dirname, 'test-image.jpg');
    if (!fs.existsSync(jpegPath)) {
      console.log('❌ JPEG test file not found. Run create-real-test-image.js first');
      return;
    }
    
    const jpegForm = new FormData();
    jpegForm.append('image', fs.createReadStream(jpegPath), {
      filename: 'test-carousel.jpg',
      contentType: 'image/jpeg'
    });
    jpegForm.append('title', 'JPEG Test Carousel');
    jpegForm.append('description', 'Testing with valid JPEG file');
    jpegForm.append('linkUrl', 'https://example.com/jpeg-test');
    
    try {
      console.log('📤 Uploading JPEG...');
      const jpegResponse = await axios.post(`${BASE_URL}/api/admin/homepage-settings/carousel`, jpegForm, {
        headers: {
          ...jpegForm.getHeaders(),
          'Authorization': `Bearer ${adminToken}`
        },
        timeout: 60000
      });
      
      console.log('✅ JPEG UPLOAD SUCCESSFUL!');
      console.log(`📥 Status: ${jpegResponse.status}`);
      console.log('📄 Response:');
      console.log(JSON.stringify(jpegResponse.data, null, 2));
      
    } catch (jpegError) {
      console.log('❌ JPEG upload failed:');
      console.log(`📥 Status: ${jpegError.response?.status}`);
      console.log('📄 Error:');
      console.log(JSON.stringify(jpegError.response?.data, null, 2));
    }
    
    // Step 4: Check final homepage settings
    console.log('\n4. Checking final homepage settings...');
    
    try {
      const settingsResponse = await axios.get(`${BASE_URL}/api/admin/homepage-settings`, {
        headers: {
          'Authorization': `Bearer ${adminToken}`
        }
      });
      
      console.log('✅ Retrieved homepage settings');
      const carouselImages = settingsResponse.data.data.carouselImages || [];
      console.log(`📊 Total carousel images: ${carouselImages.length}`);
      
      if (carouselImages.length > 0) {
        console.log('📋 All carousel images:');
        carouselImages.forEach((img, index) => {
          console.log(`  ${index + 1}. "${img.title}" - ${img.imageUrl}`);
        });
      }
      
    } catch (settingsError) {
      console.log('❌ Failed to get homepage settings:', settingsError.response?.data);
    }
    
    console.log('\n📋 FINAL ANALYSIS:');
    console.log('');
    console.log('If uploads are now working:');
    console.log('✅ The issue was with invalid image file format');
    console.log('✅ Server-side validation is working correctly');
    console.log('✅ Multer and Cloudinary are configured properly');
    console.log('');
    console.log('For frontend implementation, ensure:');
    console.log('1. Use proper image files (PNG, JPEG, etc.)');
    console.log('2. Set correct Content-Type headers');
    console.log('3. Use field name "image" for file input');
    console.log('4. Include proper authentication token');
    console.log('5. Handle file validation errors gracefully');
    
  } catch (error) {
    console.error('❌ Test script failed:', error.message);
    if (error.response) {
      console.log('Response data:', error.response.data);
    }
  }
}

// Run the final test
testCarouselUploadFinal().catch(console.error);